#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpn 函数的脚本

这个脚本测试 dpm_interpn 函数的各种功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from dpm import dpm_interpn


def test_1d_interpolation():
    """测试一维插值功能"""
    print("=" * 60)
    print("测试 dpm_interpn 一维插值功能")
    print("=" * 60)
    
    try:
        # 创建一维测试数据
        xx = np.linspace(0, 10, 11)  # [0, 1, 2, ..., 10]
        yy = xx**2  # 二次函数
        
        # 测试插值点
        A = np.array([0.5, 2.5, 5.0, 7.5, 9.5])
        
        print(f"输入网格 xx: {xx}")
        print(f"输入值 yy: {yy}")
        print(f"插值点 A: {A}")
        
        # 调用一维插值函数
        y = dpm_interpn(xx, yy, A)
        
        print(f"插值结果 y: {y}")
        
        # 验证结果
        expected = A**2
        print("\n验证:")
        for i, (a, y_val, exp) in enumerate(zip(A, y, expected)):
            error = abs(y_val - exp)
            print(f"  A[{i}] = {a:.1f}: y = {y_val:.4f}, 期望 = {exp:.4f}, 误差 = {error:.4f}")
        
        print("✓ dpm_interpn 一维插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpn 一维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_2d_interpolation():
    """测试二维插值功能"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpn 二维插值功能")
    print("=" * 60)
    
    try:
        # 创建二维测试数据
        xx1 = np.linspace(0, 4, 5)  # [0, 1, 2, 3, 4]
        xx2 = np.linspace(0, 3, 4)  # [0, 1, 2, 3]
        
        # 创建二维值矩阵
        X1, X2 = np.meshgrid(xx1, xx2, indexing='ij')
        YY = X1**2 + X2**2  # 简单的二次函数
        YY = YY.T  # 转置以匹配MATLAB的列优先顺序
        
        # 测试插值点
        A1 = np.array([0.5, 1.5, 2.5, 3.5])
        A2 = np.array([0.5, 1.0, 1.5, 2.5])
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        print(f"插值点 A1: {A1}")
        print(f"插值点 A2: {A2}")
        
        # 调用二维插值函数，使用新的参数顺序：dpm_interpn(xx2, xx1, YY, A2, A1)
        y = dpm_interpn(xx2, xx1, YY, A2, A1)
        
        print(f"插值结果 y: {y}")
        
        # 验证结果
        expected = A1**2 + A2**2
        print("\n验证:")
        for i, (a1, a2, y_val, exp) in enumerate(zip(A1, A2, y, expected)):
            error = abs(y_val - exp)
            print(f"  (A1[{i}]={a1:.1f}, A2[{i}]={a2:.1f}): y = {y_val:.4f}, 期望 = {exp:.4f}, 误差 = {error:.4f}")
        
        print("✓ dpm_interpn 二维插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpn 二维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_3d_interpolation():
    """测试三维插值功能"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpn 三维插值功能")
    print("=" * 60)
    
    try:
        # 创建三维测试数据
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1, 2])
        
        # 创建三维值矩阵
        YY = np.random.rand(3, 2, 3) * 10  # 随机3D矩阵
        
        # 测试插值点
        A1 = np.array([0.5, 1.5])
        A2 = np.array([0.3, 0.7])
        A3 = np.array([0.8, 1.2])
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"第三维网格 xx3: {xx3}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        print(f"插值点 A1: {A1}")
        print(f"插值点 A2: {A2}")
        print(f"插值点 A3: {A3}")
        
        # 调用三维插值函数，使用新的参数顺序：dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
        y = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
        
        print(f"插值结果 y: {y}")
        
        print("✓ dpm_interpn 三维插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpn 三维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpn 边界情况")
    print("=" * 60)
    
    try:
        # 测试1: 边界外的点
        print("测试1: 边界外的点")
        xx = np.array([0, 1, 2, 3, 4])
        yy = np.array([0, 1, 4, 9, 16])
        A_out = np.array([-1, 5])  # 边界外的点
        
        y_out = dpm_interpn(xx, yy, A_out)
        print(f"  边界外插值结果: {y_out}")
        
        # 测试2: 单点插值
        print("\n测试2: 单点插值")
        A_single = np.array([2.5])
        y_single = dpm_interpn(xx, yy, A_single)
        print(f"  单点插值结果: {y_single}")
        
        # 测试3: 多维数组输入
        print("\n测试3: 多维数组输入")
        A_2d = np.array([[1.5, 2.5], [3.5, 0.5]])
        y_2d = dpm_interpn(xx, yy, A_2d)
        print(f"  2D输入形状: {A_2d.shape}")
        print(f"  2D输出形状: {y_2d.shape}")
        print(f"  2D插值结果: {y_2d}")
        
        # 测试4: 常数函数
        print("\n测试4: 常数函数")
        yy_const = np.array([5, 5, 5, 5, 5])
        A_const = np.array([0.5, 1.5, 2.5, 3.5])
        y_const = dpm_interpn(xx, yy_const, A_const)
        print(f"  常数函数插值结果: {y_const} (应该都是5.0)")
        
        print("✓ dpm_interpn 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpn 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_with_visualization():
    """测试并可视化插值结果"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpn 并可视化")
    print("=" * 60)
    
    try:
        # 一维可视化
        xx = np.linspace(0, 2*np.pi, 10)
        yy = np.sin(xx)
        A_dense = np.linspace(0, 2*np.pi, 100)
        y_interp = dpm_interpn(xx, yy, A_dense)
        
        # 二维可视化
        xx1 = np.linspace(0, 3, 4)
        xx2 = np.linspace(0, 2, 3)
        X1, X2 = np.meshgrid(xx1, xx2, indexing='ij')
        YY = np.sin(X1) * np.cos(X2)
        YY = YY.T
        
        A1_grid = np.linspace(0, 3, 20)
        A2_grid = np.linspace(0, 2, 15)
        A1_mesh, A2_mesh = np.meshgrid(A1_grid, A2_grid, indexing='ij')
        
        y_2d_interp = np.zeros_like(A1_mesh)
        for i in range(A1_mesh.shape[0]):
            for j in range(A1_mesh.shape[1]):
                y_2d_interp[i, j] = dpm_interpn(xx2, xx1, YY,
                                               np.array([A2_mesh[i, j]]),
                                               np.array([A1_mesh[i, j]]))[0]
        
        # 创建可视化
        try:
            fig = plt.figure(figsize=(15, 5))
            
            # 一维插值可视化
            ax1 = fig.add_subplot(131)
            ax1.plot(xx, yy, 'bo-', label='原始数据点', markersize=6)
            ax1.plot(A_dense, y_interp, 'r-', label='插值结果', linewidth=2)
            ax1.plot(A_dense, np.sin(A_dense), 'g--', label='真实函数', alpha=0.7)
            ax1.set_xlabel('x')
            ax1.set_ylabel('y')
            ax1.set_title('一维插值')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 二维原始数据
            ax2 = fig.add_subplot(132, projection='3d')
            X1_orig, X2_orig = np.meshgrid(xx1, xx2, indexing='ij')
            ax2.plot_surface(X1_orig, X2_orig, YY.T, alpha=0.7, cmap='viridis')
            ax2.set_title('二维原始数据')
            ax2.set_xlabel('X1')
            ax2.set_ylabel('X2')
            ax2.set_zlabel('Y')
            
            # 二维插值结果
            ax3 = fig.add_subplot(133, projection='3d')
            ax3.plot_surface(A1_mesh, A2_mesh, y_2d_interp, alpha=0.7, cmap='plasma')
            ax3.set_title('二维插值结果')
            ax3.set_xlabel('A1')
            ax3.set_ylabel('A2')
            ax3.set_zlabel('Y')
            
            plt.tight_layout()
            plt.savefig('dpm_interpn_test.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            print("✓ 可视化图表已保存为 'dpm_interpn_test.png'")
            
        except Exception as plot_error:
            print(f"可视化失败（但插值成功）: {plot_error}")
        
        print("✓ dpm_interpn 可视化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpn 可视化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("dpm_interpn 函数测试程序")
    print("这个程序测试多维插值函数的各种功能")
    
    success_count = 0
    total_tests = 5
    
    # 测试1: 一维插值
    if test_1d_interpolation():
        success_count += 1
    
    # 测试2: 二维插值
    if test_2d_interpolation():
        success_count += 1
    
    # 测试3: 三维插值
    if test_3d_interpolation():
        success_count += 1
    
    # 测试4: 边界情况
    if test_edge_cases():
        success_count += 1
    
    # 测试5: 可视化测试
    if test_with_visualization():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！dpm_interpn 函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
