#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 input_check_grd 函数

验证修改后的input_check_grd函数是否与MATLAB版本逻辑一致
"""

from dpm import Grid, input_check_grd


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试基本功能")
    print("=" * 60)
    
    try:
        # 创建测试Grid对象
        grd = Grid()
        
        # 设置状态变量
        grd.Nx = {0: [401]}  # 标量，应该被复制到T+1个元素
        grd.Xn = {0: {'hi': [0.6], 'lo': [0.4]}}  # 标量，应该被复制
        
        # 设置控制变量
        grd.Nu = {0: [351]}  # 标量，应该被复制到T个元素
        grd.Un = {0: {'hi': [1], 'lo': [-2.5]}}  # 标量，应该被复制
        
        T = 5  # 测试时间步数
        
        print(f"修正前:")
        print(f"  grd.Nx[0]: {grd.Nx[0]} (长度: {len(grd.Nx[0])})")
        print(f"  grd.Xn[0]['hi']: {grd.Xn[0]['hi']} (长度: {len(grd.Xn[0]['hi'])})")
        print(f"  grd.Xn[0]['lo']: {grd.Xn[0]['lo']} (长度: {len(grd.Xn[0]['lo'])})")
        print(f"  grd.Nu[0]: {grd.Nu[0]} (长度: {len(grd.Nu[0])})")
        print(f"  grd.Un[0]['hi']: {grd.Un[0]['hi']} (长度: {len(grd.Un[0]['hi'])})")
        print(f"  grd.Un[0]['lo']: {grd.Un[0]['lo']} (长度: {len(grd.Un[0]['lo'])})")
        
        # 调用input_check_grd
        grd_corrected = input_check_grd(grd, T)
        
        print(f"\n修正后 (T={T}):")
        print(f"  grd.Nx[0]: {grd_corrected.Nx[0]} (长度: {len(grd_corrected.Nx[0])}, 期望: {T+1})")
        print(f"  grd.Xn[0]['hi']: {grd_corrected.Xn[0]['hi']} (长度: {len(grd_corrected.Xn[0]['hi'])}, 期望: {T+1})")
        print(f"  grd.Xn[0]['lo']: {grd_corrected.Xn[0]['lo']} (长度: {len(grd_corrected.Xn[0]['lo'])}, 期望: {T+1})")
        print(f"  grd.Nu[0]: {grd_corrected.Nu[0]} (长度: {len(grd_corrected.Nu[0])}, 期望: {T})")
        print(f"  grd.Un[0]['hi']: {grd_corrected.Un[0]['hi']} (长度: {len(grd_corrected.Un[0]['hi'])}, 期望: {T})")
        print(f"  grd.Un[0]['lo']: {grd_corrected.Un[0]['lo']} (长度: {len(grd_corrected.Un[0]['lo'])}, 期望: {T})")
        
        # 验证结果
        success = True
        if len(grd_corrected.Nx[0]) != T + 1:
            print(f"✗ Nx[0]长度错误: {len(grd_corrected.Nx[0])} != {T+1}")
            success = False
        if len(grd_corrected.Xn[0]['hi']) != T + 1:
            print(f"✗ Xn[0]['hi']长度错误: {len(grd_corrected.Xn[0]['hi'])} != {T+1}")
            success = False
        if len(grd_corrected.Xn[0]['lo']) != T + 1:
            print(f"✗ Xn[0]['lo']长度错误: {len(grd_corrected.Xn[0]['lo'])} != {T+1}")
            success = False
        if len(grd_corrected.Nu[0]) != T:
            print(f"✗ Nu[0]长度错误: {len(grd_corrected.Nu[0])} != {T}")
            success = False
        if len(grd_corrected.Un[0]['hi']) != T:
            print(f"✗ Un[0]['hi']长度错误: {len(grd_corrected.Un[0]['hi'])} != {T}")
            success = False
        if len(grd_corrected.Un[0]['lo']) != T:
            print(f"✗ Un[0]['lo']长度错误: {len(grd_corrected.Un[0]['lo'])} != {T}")
            success = False
        
        if success:
            print("✓ 基本功能测试通过")
        
        return success
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_error_conditions():
    """测试错误条件"""
    print("\n" + "=" * 60)
    print("测试错误条件")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 测试1: T < 1
    try:
        grd = Grid()
        grd.Nx = {0: [401]}
        input_check_grd(grd, 0)
        print("✗ 测试1失败: 应该抛出T<1错误")
    except ValueError as e:
        if 'prb.N must be greater than 0' in str(e):
            print("✓ 测试1通过: 正确检测T<1错误")
            success_count += 1
        else:
            print(f"✗ 测试1失败: 错误消息不正确: {e}")
    
    # 测试2: Nx < 1
    try:
        grd = Grid()
        grd.Nx = {0: [0]}  # 无效的网格点数
        input_check_grd(grd, 5)
        print("✗ 测试2失败: 应该抛出Nx<1错误")
    except ValueError as e:
        if 'grd.Nx[0] must be equal or greater than 1' in str(e):
            print("✓ 测试2通过: 正确检测Nx<1错误")
            success_count += 1
        else:
            print(f"✗ 测试2失败: 错误消息不正确: {e}")
    
    # 测试3: Nu < 1
    try:
        grd = Grid()
        grd.Nu = {0: [-1]}  # 无效的网格点数
        input_check_grd(grd, 5)
        print("✗ 测试3失败: 应该抛出Nu<1错误")
    except ValueError as e:
        if 'grd.Nu[0] must be equal or greater than 1' in str(e):
            print("✓ 测试3通过: 正确检测Nu<1错误")
            success_count += 1
        else:
            print(f"✗ 测试3失败: 错误消息不正确: {e}")
    
    # 测试4: 数组长度不匹配
    try:
        grd = Grid()
        grd.Nx = {0: [401]}
        grd.Xn = {0: {'hi': [0.6, 0.7, 0.8]}}  # 长度为3，但T=5需要T+1=6
        input_check_grd(grd, 5)
        print("✗ 测试4失败: 应该抛出数组长度不匹配错误")
    except ValueError as e:
        if 'must be a scalar OR have the same length as the problem' in str(e):
            print("✓ 测试4通过: 正确检测数组长度不匹配错误")
            success_count += 1
        else:
            print(f"✗ 测试4失败: 错误消息不正确: {e}")
    
    print(f"\n错误条件测试: {success_count}/{total_tests} 通过")
    return success_count == total_tests


def test_repmat_logic():
    """测试repmat逻辑（标量复制）"""
    print("\n" + "=" * 60)
    print("测试repmat逻辑")
    print("=" * 60)
    
    try:
        # 创建包含标量的Grid对象
        grd = Grid()
        
        # 标量状态变量
        grd.Nx = {0: 401}  # 标量
        grd.Xn = {0: {'hi': 0.6, 'lo': 0.4}}  # 标量
        
        # 标量控制变量
        grd.Nu = {0: 351}  # 标量
        grd.Un = {0: {'hi': 1.0, 'lo': -2.5}}  # 标量
        
        T = 3
        
        print(f"修正前 (标量):")
        print(f"  grd.Nx[0]: {grd.Nx[0]} (类型: {type(grd.Nx[0])})")
        print(f"  grd.Xn[0]['hi']: {grd.Xn[0]['hi']} (类型: {type(grd.Xn[0]['hi'])})")
        print(f"  grd.Nu[0]: {grd.Nu[0]} (类型: {type(grd.Nu[0])})")
        print(f"  grd.Un[0]['hi']: {grd.Un[0]['hi']} (类型: {type(grd.Un[0]['hi'])})")
        
        # 调用input_check_grd
        grd_corrected = input_check_grd(grd, T)
        
        print(f"\n修正后 (T={T}):")
        print(f"  grd.Nx[0]: {grd_corrected.Nx[0]} (长度: {len(grd_corrected.Nx[0])}, 期望: {T+1})")
        print(f"  grd.Xn[0]['hi']: {grd_corrected.Xn[0]['hi']} (长度: {len(grd_corrected.Xn[0]['hi'])}, 期望: {T+1})")
        print(f"  grd.Nu[0]: {grd_corrected.Nu[0]} (长度: {len(grd_corrected.Nu[0])}, 期望: {T})")
        print(f"  grd.Un[0]['hi']: {grd_corrected.Un[0]['hi']} (长度: {len(grd_corrected.Un[0]['hi'])}, 期望: {T})")
        
        # 验证repmat逻辑
        success = True
        
        # 检查状态变量复制
        if grd_corrected.Nx[0] != [401] * (T + 1):
            print(f"✗ Nx[0] repmat错误: {grd_corrected.Nx[0]} != {[401] * (T + 1)}")
            success = False
        
        if grd_corrected.Xn[0]['hi'] != [0.6] * (T + 1):
            print(f"✗ Xn[0]['hi'] repmat错误: {grd_corrected.Xn[0]['hi']} != {[0.6] * (T + 1)}")
            success = False
        
        # 检查控制变量复制
        if grd_corrected.Nu[0] != [351] * T:
            print(f"✗ Nu[0] repmat错误: {grd_corrected.Nu[0]} != {[351] * T}")
            success = False
        
        if grd_corrected.Un[0]['hi'] != [1.0] * T:
            print(f"✗ Un[0]['hi'] repmat错误: {grd_corrected.Un[0]['hi']} != {[1.0] * T}")
            success = False
        
        if success:
            print("✓ repmat逻辑测试通过")
        
        return success
        
    except Exception as e:
        print(f"✗ repmat逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("input_check_grd 函数测试程序")
    print("验证修改后的函数是否与MATLAB版本逻辑一致")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 基本功能
    if test_basic_functionality():
        success_count += 1
    
    # 测试2: 错误条件
    if test_error_conditions():
        success_count += 1
    
    # 测试3: repmat逻辑
    if test_repmat_logic():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！")
        print("✓ 基本功能正常")
        print("✓ 错误检查正确")
        print("✓ repmat逻辑正确")
        print("\ninput_check_grd函数已与MATLAB版本逻辑保持一致！")
    else:
        print("⚠️  部分测试失败。请检查函数实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
