#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插值方法对比测试

比较 np.interp 和 scipy.interpolate.interp1d 的差异，
以及它们与MATLAB interp1函数的相似性
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d


def test_interpolation_methods():
    """测试不同插值方法的差异"""
    print("=" * 60)
    print("插值方法对比测试")
    print("=" * 60)
    
    # 创建测试数据
    x_data = np.array([0, 1, 2, 3, 4, 5])
    y_data = np.array([0, 1, 4, 9, 16, 25])  # y = x^2
    
    # 查询点（包括边界外的点）
    x_query = np.array([-0.5, 0.5, 1.5, 2.5, 3.5, 4.5, 5.5])
    
    print("原始数据:")
    print(f"x_data: {x_data}")
    print(f"y_data: {y_data}")
    print(f"查询点: {x_query}")
    
    # 方法1: np.interp
    y_np_interp = np.interp(x_query, x_data, y_data)
    
    # 方法2: scipy.interpolate.interp1d (线性插值)
    f_scipy_linear = interp1d(x_data, y_data, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
    y_scipy_linear = f_scipy_linear(x_query)
    
    # 方法3: scipy.interpolate.interp1d (三次插值)
    f_scipy_cubic = interp1d(x_data, y_data, kind='cubic', 
                             bounds_error=False, fill_value='extrapolate')
    y_scipy_cubic = f_scipy_cubic(x_query)
    
    # 方法4: scipy.interpolate.interp1d (边界值填充)
    f_scipy_boundary = interp1d(x_data, y_data, kind='linear', 
                                bounds_error=False, fill_value=(y_data[0], y_data[-1]))
    y_scipy_boundary = f_scipy_boundary(x_query)
    
    print("\n插值结果对比:")
    print("查询点    np.interp   scipy线性   scipy三次   scipy边界")
    print("-" * 60)
    for i, x in enumerate(x_query):
        print(f"{x:6.1f}    {y_np_interp[i]:8.3f}   {y_scipy_linear[i]:8.3f}   "
              f"{y_scipy_cubic[i]:8.3f}   {y_scipy_boundary[i]:8.3f}")
    
    # 分析差异
    print("\n差异分析:")
    print("1. 边界内插值:")
    inside_mask = (x_query >= x_data[0]) & (x_query <= x_data[-1])
    inside_diff = np.abs(y_np_interp[inside_mask] - y_scipy_linear[inside_mask])
    print(f"   np.interp vs scipy线性插值最大差异: {np.max(inside_diff):.10f}")
    
    print("2. 边界外插值:")
    outside_mask = ~inside_mask
    if np.any(outside_mask):
        print(f"   np.interp (边界值): {y_np_interp[outside_mask]}")
        print(f"   scipy外推: {y_scipy_linear[outside_mask]}")
        print(f"   scipy边界填充: {y_scipy_boundary[outside_mask]}")
    
    return True


def test_battery_voltage_interpolation():
    """测试电池电压插值的具体案例"""
    print("\n" + "=" * 60)
    print("电池电压插值案例测试")
    print("=" * 60)
    
    # 电池SOC和开路电压数据（来自PHEV模型）
    soc_list = np.arange(0, 1.1, 0.1)
    V_oc = np.array([326.1, 336.2, 343.1, 347.3, 349.7, 353.2, 360.5, 
                     368.2, 377.5, 387.8, 401.3])
    
    # 测试SOC值（包括边界外的值）
    soc_test = np.array([0.05, 0.25, 0.55, 0.85, 0.95, 1.05])
    
    print("SOC数据:")
    print(f"soc_list: {soc_list}")
    print(f"V_oc: {V_oc}")
    print(f"测试SOC: {soc_test}")
    
    # 方法1: np.interp
    v_np = np.interp(soc_test, soc_list, V_oc)
    
    # 方法2: scipy.interpolate.interp1d (外推)
    v_interp_extrap = interp1d(soc_list, V_oc, kind='linear', 
                               bounds_error=False, fill_value='extrapolate')
    v_scipy_extrap = v_interp_extrap(soc_test)
    
    # 方法3: scipy.interpolate.interp1d (边界值)
    v_interp_bound = interp1d(soc_list, V_oc, kind='linear', 
                              bounds_error=False, fill_value=(V_oc[0], V_oc[-1]))
    v_scipy_bound = v_interp_bound(soc_test)
    
    print("\n电池电压插值结果:")
    print("SOC     np.interp   scipy外推   scipy边界   差异(np-外推)")
    print("-" * 65)
    for i, soc in enumerate(soc_test):
        diff = abs(v_np[i] - v_scipy_extrap[i])
        print(f"{soc:5.2f}   {v_np[i]:8.2f}   {v_scipy_extrap[i]:8.2f}   "
              f"{v_scipy_bound[i]:8.2f}   {diff:10.6f}")
    
    # MATLAB行为分析
    print("\nMATLAB interp1行为分析:")
    print("- 默认情况下，MATLAB interp1在边界外返回NaN")
    print("- 使用'linear'和'extrap'选项可以进行外推")
    print("- scipy.interpolate.interp1d的fill_value='extrapolate'最接近MATLAB的'extrap'选项")
    print("- np.interp总是使用边界值，类似于MATLAB的边界值填充")
    
    return True


def test_engine_efficiency_interpolation():
    """测试发动机效率插值的具体案例"""
    print("\n" + "=" * 60)
    print("发动机效率插值案例测试")
    print("=" * 60)
    
    # 简化的发动机数据
    we_list = np.array([0, 100, 200, 300, 400, 500]) * np.pi/30  # 转速 rad/s
    Te_list = np.array([0, 50, 100, 150])  # 扭矩 Nm
    
    # 简化的效率矩阵
    eta = np.array([
        [0.1, 0.1, 0.1, 0.1, 0.1, 0.1],
        [0.1, 0.3, 0.35, 0.38, 0.36, 0.32],
        [0.1, 0.32, 0.38, 0.40, 0.38, 0.34],
        [0.1, 0.30, 0.36, 0.38, 0.36, 0.32]
    ])
    
    # 测试点
    we_test = 250 * np.pi/30  # 250 rpm
    Te_test = 75  # 75 Nm
    
    print("发动机数据:")
    print(f"转速列表 (rpm): {we_list * 30/np.pi}")
    print(f"扭矩列表 (Nm): {Te_list}")
    print(f"测试点: {we_test * 30/np.pi:.1f} rpm, {Te_test} Nm")
    
    # 方法1: 使用np.interp进行二维插值（分步）
    # 先对扭矩插值
    eta_at_we = []
    for i in range(len(we_list)):
        eta_at_we.append(np.interp(Te_test, Te_list, eta[:, i]))
    eta_at_we = np.array(eta_at_we)
    
    # 再对转速插值
    eta_np = np.interp(we_test, we_list, eta_at_we)
    
    # 方法2: 使用scipy的RegularGridInterpolator（在原代码中已使用）
    from scipy.interpolate import RegularGridInterpolator
    eta_interp = RegularGridInterpolator((Te_list, we_list), eta, 
                                         bounds_error=False, fill_value=None)
    eta_scipy = eta_interp([Te_test, we_test])[0]
    
    print(f"\n效率插值结果:")
    print(f"np.interp (分步): {eta_np:.6f}")
    print(f"scipy RegularGridInterpolator: {eta_scipy:.6f}")
    print(f"差异: {abs(eta_np - eta_scipy):.8f}")
    
    print("\n二维插值方法比较:")
    print("- np.interp: 只能进行一维插值，需要分步处理二维数据")
    print("- scipy.interpolate: 提供专门的二维/多维插值工具")
    print("- MATLAB的interp2更接近scipy的RegularGridInterpolator")
    
    return True


def main():
    """主测试函数"""
    print("插值方法对比测试程序")
    print("比较不同插值方法与MATLAB的相似性")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 基本插值方法对比
    if test_interpolation_methods():
        success_count += 1
    
    # 测试2: 电池电压插值案例
    if test_battery_voltage_interpolation():
        success_count += 1
    
    # 测试3: 发动机效率插值案例
    if test_engine_efficiency_interpolation():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    print("\n结论和建议:")
    print("1. 对于边界内插值，np.interp和scipy.interp1d(linear)结果相同")
    print("2. 对于边界外插值：")
    print("   - np.interp: 返回边界值（类似MATLAB默认行为）")
    print("   - scipy.interp1d + extrapolate: 线性外推（类似MATLAB 'extrap'）")
    print("3. scipy.interpolate.interp1d更接近MATLAB interp1的完整功能")
    print("4. 对于多维插值，应使用scipy的专门工具")
    print("5. 建议在PHEV模型中使用scipy.interpolate以获得更好的MATLAB兼容性")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
