% load driving cycle
load VSpeed_WLTC.mat;

% create grid
clear grd
grd.Nx{1}    = 401; % SOC resolution 0.05%
grd.Xn{1}.hi = 0.6; % Maximum Battery SOC
grd.Xn{1}.lo = 0.4; % Minimum Battery SOC

grd.Nu{1}    = 351; % Torque distribution resolution
grd.Un{1}.hi = 1; % 1:EV/Regen, 0:Engine Only, 0-1:Power assist, <0:Recharge
grd.Un{1}.lo = -2.5;	% Att: Lower bound may vary with engine size.

grd.Nu{2} = 3;
grd.Un{2}.hi = 2; % 0:Series mode, 1:P1 mode, 2:P3 mode
grd.Un{2}.lo = 0;

% set initial state
grd.X0{1} = 0.50;

% final state constraints
grd.XN{1}.hi = 0.5 - 0*0.01;
grd.XN{1}.lo = 0.495 - 0*0.01;

% define problem
clear prb
prb.W{1} = speed_vector; % (n-second elements)
prb.W{2} = acceleration_vector; % (n-second elements)
prb.W{3} = gearnumber_vector; % (n-second elements)
prb.Ts = 1;
prb.N  = (length(prb.W{1})-1)*1/prb.Ts + 1; %NEDC=1179, WLTC/CLTC=1799, FTP75=1876

% set options
options = dpm();
options.MyInf = 1000;
options.BoundaryMethod = 'Line'; % also possible: 'none' or 'LevelSet';
if strcmp(options.BoundaryMethod,'Line') 
    %these options are only needed if 'Line' is used
    options.Iter = 5;
    options.Tol = 1e-8;
    options.FixedGrid = 0;
end

[res,dyn] = dpm(@phev_ht21_serip1p3,[],grd,prb,options);
