#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 dpm_interpf2sbh 函数的脚本

这个脚本快速验证 dpm_interpf2sbh 函数的基本功能
"""

import numpy as np
from dpm import dpm_interpf2sbh


def main():
    """主验证函数"""
    print("快速验证 dpm_interpf2sbh 函数")
    print("=" * 40)
    
    try:
        # 创建简单的测试设置
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        YY = np.array([[0, 1, 4],
                       [1, 2, 5]])  # 2x3矩阵 (xx2 x xx1)
        
        # 设置边界矩阵 (2 x length(xx2))
        lim = np.array([[0.5, 0.5],    # 下边界
                        [1.5, 1.5]])   # 上边界
        
        print(f"输入网格 xx1: {xx1}")
        print(f"输入网格 xx2: {xx2}")
        print(f"值矩阵 YY:\n{YY}")
        print(f"边界矩阵 lim:\n{lim}")
        
        # 测试不同的插值点
        test_cases = [
            (1.0, 0.5, "边界内插值"),
            (0.2, 0.5, "第一维边界外（下）"),
            (1.8, 0.5, "第一维边界外（上）"),
            (1.0, 0.3, "第二维网格内"),
            (1.2, 0.7, "一般情况"),
        ]
        
        print("\n插值测试:")
        for a1, a2, description in test_cases:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim)
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f} ({description})")
        
        print("\n验证完成!")
        
        # 简单的正确性检查
        print(f"\n正确性检查:")
        
        # 检查边界处理
        y_center = dpm_interpf2sbh(xx1, xx2, YY, 1.0, 0.5, lim)
        y_below = dpm_interpf2sbh(xx1, xx2, YY, 0.2, 0.5, lim)
        y_above = dpm_interpf2sbh(xx1, xx2, YY, 1.8, 0.5, lim)
        
        print(f"中心点 (1.0, 0.5): y = {y_center:.4f}")
        print(f"下边界外 (0.2, 0.5): y = {y_below:.4f}")
        print(f"上边界外 (1.8, 0.5): y = {y_above:.4f}")
        
        # 检查第二维插值的影响
        y_a2_0 = dpm_interpf2sbh(xx1, xx2, YY, 1.0, 0.0, lim)
        y_a2_1 = dpm_interpf2sbh(xx1, xx2, YY, 1.0, 1.0, lim)
        
        print(f"第二维=0.0时: y = {y_a2_0:.4f}")
        print(f"第二维=1.0时: y = {y_a2_1:.4f}")
        
        print("✓ 基本功能验证完成")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
