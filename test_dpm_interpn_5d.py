#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpn 五维插值功能的脚本

这个脚本专门测试根据MATLAB版本实现的五维插值功能
"""

import numpy as np
from dpm import dpm_interpn


def test_5d_basic_interpolation():
    """测试基本的五维插值功能"""
    print("=" * 60)
    print("测试五维插值基本功能")
    print("=" * 60)
    
    try:
        # 创建五维测试数据 (最小的2x2x2x2x2网格)
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        xx4 = np.array([0, 1])
        xx5 = np.array([0, 1])
        
        # 创建五维值矩阵，使用线性函数 f(x1,x2,x3,x4,x5) = x1 + x2 + x3 + x4 + x5
        YY = np.zeros((2, 2, 2, 2, 2))  # (xx5, xx4, xx3, xx2, xx1) 顺序
        for i5, x5 in enumerate(xx5):
            for i4, x4 in enumerate(xx4):
                for i3, x3 in enumerate(xx3):
                    for i2, x2 in enumerate(xx2):
                        for i1, x1 in enumerate(xx1):
                            YY[i5, i4, i3, i2, i1] = x1 + x2 + x3 + x4 + x5
        
        print(f"五维网格: xx1={xx1}, xx2={xx2}, xx3={xx3}, xx4={xx4}, xx5={xx5}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        
        # 显示矩阵的一些切片
        print("值矩阵切片:")
        print(f"  YY[0,0,0,:,:] (x3=0,x4=0,x5=0):\n{YY[0,0,0,:,:]}")
        print(f"  YY[1,1,1,:,:] (x3=1,x4=1,x5=1):\n{YY[1,1,1,:,:]}")
        
        # 测试插值点
        test_cases = [
            (0.5, 0.5, 0.5, 0.5, 0.5, "中心点"),
            (1.0, 0.0, 1.0, 0.0, 1.0, "网格点"),
            (0.25, 0.75, 0.25, 0.75, 0.25, "一般插值点"),
            (0.0, 0.0, 0.0, 0.0, 0.0, "原点"),
            (1.0, 1.0, 1.0, 1.0, 1.0, "对角顶点"),
        ]
        
        print("\n插值测试:")
        max_error = 0
        for a1, a2, a3, a4, a5, description in test_cases:
            A1 = np.array([a1])
            A2 = np.array([a2])
            A3 = np.array([a3])
            A4 = np.array([a4])
            A5 = np.array([a5])
            
            y = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
            expected = a1 + a2 + a3 + a4 + a5  # 线性函数的期望值
            error = abs(y[0] - expected)
            max_error = max(max_error, error)
            
            print(f"  ({a1:.2f}, {a2:.2f}, {a3:.2f}, {a4:.2f}, {a5:.2f}): y = {y[0]:.6f}, 期望 = {expected:.6f}, 误差 = {error:.8f} ({description})")
        
        print(f"最大误差: {max_error:.8f}")
        
        print("✓ 五维插值基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 五维插值基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_5d_corner_points():
    """测试五维插值的角点精确性"""
    print("\n" + "=" * 60)
    print("测试五维插值角点精确性")
    print("=" * 60)
    
    try:
        # 创建2x2x2x2x2的测试网格
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        xx4 = np.array([0, 1])
        xx5 = np.array([0, 1])
        
        # 创建2x2x2x2x2矩阵，每个角点有唯一值
        YY = np.zeros((2, 2, 2, 2, 2))
        value = 1
        for i5 in range(2):
            for i4 in range(2):
                for i3 in range(2):
                    for i2 in range(2):
                        for i1 in range(2):
                            YY[i5, i4, i3, i2, i1] = value
                            value += 1
        
        print(f"2x2x2x2x2网格，值从1到32")
        print(f"YY形状: {YY.shape}")
        
        # 测试几个关键角点
        corner_tests = [
            (0, 0, 0, 0, 0, YY[0, 0, 0, 0, 0]),  # 第一个角点
            (1, 0, 0, 0, 0, YY[0, 0, 0, 0, 1]),  # x1=1的角点
            (0, 1, 0, 0, 0, YY[0, 0, 0, 1, 0]),  # x2=1的角点
            (1, 1, 1, 1, 1, YY[1, 1, 1, 1, 1]),  # 最后一个角点
        ]
        
        print("\n关键角点精确性测试:")
        max_corner_error = 0
        for a1, a2, a3, a4, a5, expected in corner_tests:
            A1, A2, A3, A4, A5 = np.array([a1]), np.array([a2]), np.array([a3]), np.array([a4]), np.array([a5])
            y = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
            error = abs(y[0] - expected)
            max_corner_error = max(max_corner_error, error)
            print(f"  ({a1}, {a2}, {a3}, {a4}, {a5}): y = {y[0]:.8f}, 期望 = {expected:.8f}, 误差 = {error:.10f}")
        
        print(f"角点最大误差: {max_corner_error:.10f}")
        
        # 测试中心点
        print("\n中心点测试:")
        A1, A2, A3, A4, A5 = np.array([0.5]), np.array([0.5]), np.array([0.5]), np.array([0.5]), np.array([0.5])
        y_center = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
        expected_center = np.mean(YY)  # 理论上应该是所有值的平均
        
        print(f"  中心点 (0.5, 0.5, 0.5, 0.5, 0.5): y = {y_center[0]:.6f}")
        print(f"  期望 (平均值): {expected_center:.6f}")
        print(f"  误差: {abs(y_center[0] - expected_center):.8f}")
        
        print("✓ 五维插值角点精确性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 五维插值角点精确性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_5d_boundary_handling():
    """测试五维插值的边界处理"""
    print("\n" + "=" * 60)
    print("测试五维插值边界处理")
    print("=" * 60)
    
    try:
        # 创建较小的网格（计算复杂度考虑）
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        xx4 = np.array([0, 1])
        xx5 = np.array([0, 1])
        
        # 创建随机值矩阵
        np.random.seed(42)
        YY = np.random.rand(2, 2, 2, 2, 3) * 10
        
        print(f"网格大小: {len(xx1)} x {len(xx2)} x {len(xx3)} x {len(xx4)} x {len(xx5)}")
        print(f"值矩阵形状: {YY.shape}")
        
        # 测试边界外的点
        boundary_tests = [
            (-0.5, 0.5, 0.5, 0.5, 0.5, "第一维下边界外"),
            (2.5, 0.5, 0.5, 0.5, 0.5, "第一维上边界外"),
            (1.0, -0.5, 0.5, 0.5, 0.5, "第二维下边界外"),
            (1.0, 1.5, 0.5, 0.5, 0.5, "第二维上边界外"),
        ]
        
        print("\n边界外点测试:")
        for a1, a2, a3, a4, a5, description in boundary_tests:
            A1, A2, A3, A4, A5 = np.array([a1]), np.array([a2]), np.array([a3]), np.array([a4]), np.array([a5])
            y = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
            print(f"  ({a1:4.1f}, {a2:4.1f}, {a3:4.1f}, {a4:4.1f}, {a5:4.1f}): y = {y[0]:8.4f} ({description})")
        
        print("✓ 五维插值边界处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 五维插值边界处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_5d_vectorized():
    """测试五维插值的向量化处理"""
    print("\n" + "=" * 60)
    print("测试五维插值向量化处理")
    print("=" * 60)
    
    try:
        # 创建小的测试网格
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        xx4 = np.array([0, 1])
        xx5 = np.array([0, 1])
        
        # 创建线性函数值矩阵
        YY = np.zeros((2, 2, 2, 2, 2))
        for i5, x5 in enumerate(xx5):
            for i4, x4 in enumerate(xx4):
                for i3, x3 in enumerate(xx3):
                    for i2, x2 in enumerate(xx2):
                        for i1, x1 in enumerate(xx1):
                            YY[i5, i4, i3, i2, i1] = x1 + x2 + x3 + x4 + x5
        
        print(f"网格: xx1={xx1}, xx2={xx2}, xx3={xx3}, xx4={xx4}, xx5={xx5}")
        print(f"值矩阵形状: {YY.shape}")
        
        # 测试向量化输入
        A1 = np.array([0.25, 0.75])
        A2 = np.array([0.3, 0.7])
        A3 = np.array([0.4, 0.6])
        A4 = np.array([0.2, 0.8])
        A5 = np.array([0.1, 0.9])
        
        print(f"\n向量化输入:")
        print(f"A1: {A1}")
        print(f"A2: {A2}")
        print(f"A3: {A3}")
        print(f"A4: {A4}")
        print(f"A5: {A5}")
        
        y_vector = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
        expected_vector = A1 + A2 + A3 + A4 + A5
        
        print(f"\n向量化结果:")
        print(f"插值结果: {y_vector}")
        print(f"期望结果: {expected_vector}")
        print(f"误差: {np.abs(y_vector - expected_vector)}")
        
        print("✓ 五维插值向量化处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 五维插值向量化处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("五维插值函数专项测试程序")
    print("这个程序测试根据MATLAB版本实现的 dpm_interpn 五维插值功能")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 基本功能
    if test_5d_basic_interpolation():
        success_count += 1
    
    # 测试2: 角点精确性
    if test_5d_corner_points():
        success_count += 1
    
    # 测试3: 边界处理
    if test_5d_boundary_handling():
        success_count += 1
    
    # 测试4: 向量化处理
    if test_5d_vectorized():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有五维插值测试都通过了！函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查五维插值实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
