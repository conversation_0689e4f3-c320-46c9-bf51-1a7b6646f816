#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的 dpm_interpn 一维和二维插值功能的脚本

这个脚本专门测试根据MATLAB版本改进后的一维和二维插值实现
"""

import numpy as np
import matplotlib.pyplot as plt
from dpm import dpm_interpn


def test_improved_1d_interpolation():
    """测试改进后的一维插值功能"""
    print("=" * 60)
    print("测试改进后的一维插值功能")
    print("=" * 60)
    
    try:
        # 创建一维测试数据
        xx = np.linspace(0, 10, 11)  # [0, 1, 2, ..., 10]
        yy = xx**2  # 二次函数
        
        # 测试插值点
        A = np.array([0.5, 2.5, 5.0, 7.5, 9.5])
        
        print(f"输入网格 xx: {xx}")
        print(f"输入值 yy: {yy}")
        print(f"插值点 A: {A}")
        
        # 调用一维插值函数
        y = dpm_interpn(xx, yy, A)
        
        print(f"插值结果 y: {y}")
        
        # 验证结果
        expected = A**2
        print("\n验证:")
        max_error = 0
        for i, (a, y_val, exp) in enumerate(zip(A, y, expected)):
            error = abs(y_val - exp)
            max_error = max(max_error, error)
            print(f"  A[{i}] = {a:.1f}: y = {y_val:.4f}, 期望 = {exp:.4f}, 误差 = {error:.6f}")
        
        print(f"最大误差: {max_error:.8f}")
        
        # 测试边界情况
        print("\n边界情况测试:")
        boundary_points = np.array([0.0, 10.0, -1.0, 11.0])  # 包括边界外的点
        y_boundary = dpm_interpn(xx, yy, boundary_points)
        
        for i, (a, y_val) in enumerate(zip(boundary_points, y_boundary)):
            print(f"  A = {a:4.1f}: y = {y_val:8.4f}")
        
        # 测试多维输入
        print("\n多维输入测试:")
        A_2d = np.array([[1.5, 2.5], [3.5, 4.5]])
        y_2d = dpm_interpn(xx, yy, A_2d)
        expected_2d = A_2d**2
        
        print(f"  2D输入形状: {A_2d.shape}")
        print(f"  2D输出形状: {y_2d.shape}")
        print(f"  2D插值结果:\n{y_2d}")
        print(f"  2D期望结果:\n{expected_2d}")
        print(f"  2D误差:\n{np.abs(y_2d - expected_2d)}")
        
        print("✓ 改进后的一维插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的一维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_improved_2d_interpolation():
    """测试改进后的二维插值功能"""
    print("\n" + "=" * 60)
    print("测试改进后的二维插值功能")
    print("=" * 60)
    
    try:
        # 创建二维测试数据
        xx1 = np.linspace(0, 4, 5)  # [0, 1, 2, 3, 4]
        xx2 = np.linspace(0, 3, 4)  # [0, 1, 2, 3]
        
        # 创建二维值矩阵，使用线性函数 f(x1,x2) = x1 + 2*x2
        YY = np.zeros((4, 5))  # (xx2, xx1) 顺序
        for i2, x2 in enumerate(xx2):
            for i1, x1 in enumerate(xx1):
                YY[i2, i1] = x1 + 2*x2
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        print(f"YY矩阵:\n{YY}")
        
        # 测试插值点
        test_cases = [
            (0.5, 0.5, "中心区域"),
            (1.0, 0.0, "边界点"),
            (2.5, 1.5, "一般插值点"),
            (0.0, 0.0, "原点"),
            (4.0, 3.0, "右上角"),
        ]
        
        print("\n插值测试:")
        max_error = 0
        for a1, a2, description in test_cases:
            A1 = np.array([a1])
            A2 = np.array([a2])
            
            y = dpm_interpn(xx1, xx2, YY, A1, A2)
            expected = a1 + 2*a2  # 线性函数的期望值
            error = abs(y[0] - expected)
            max_error = max(max_error, error)
            
            print(f"  ({a1:.1f}, {a2:.1f}): y = {y[0]:.6f}, 期望 = {expected:.6f}, 误差 = {error:.8f} ({description})")
        
        print(f"最大误差: {max_error:.8f}")
        
        # 测试角点精确性
        print("\n角点精确性测试:")
        corner_tests = [
            (0, 0, YY[0, 0]),  # 左下角
            (4, 0, YY[0, 4]),  # 右下角
            (0, 3, YY[3, 0]),  # 左上角
            (4, 3, YY[3, 4]),  # 右上角
        ]
        
        corner_max_error = 0
        for a1, a2, expected in corner_tests:
            A1, A2 = np.array([a1]), np.array([a2])
            y = dpm_interpn(xx1, xx2, YY, A1, A2)
            error = abs(y[0] - expected)
            corner_max_error = max(corner_max_error, error)
            print(f"  ({a1}, {a2}): y = {y[0]:.8f}, 期望 = {expected:.8f}, 误差 = {error:.10f}")
        
        print(f"角点最大误差: {corner_max_error:.10f}")
        
        # 测试边界外的点
        print("\n边界外点测试:")
        boundary_tests = [
            (-0.5, 1.5, "第一维下边界外"),
            (4.5, 1.5, "第一维上边界外"),
            (2.0, -0.5, "第二维下边界外"),
            (2.0, 3.5, "第二维上边界外"),
        ]
        
        for a1, a2, description in boundary_tests:
            A1, A2 = np.array([a1]), np.array([a2])
            y = dpm_interpn(xx1, xx2, YY, A1, A2)
            print(f"  ({a1:4.1f}, {a2:4.1f}): y = {y[0]:8.4f} ({description})")
        
        # 测试向量化输入
        print("\n向量化输入测试:")
        A1_vec = np.array([0.5, 1.5, 2.5, 3.5])
        A2_vec = np.array([0.3, 0.8, 1.2, 2.7])
        
        y_vec = dpm_interpn(xx1, xx2, YY, A1_vec, A2_vec)
        expected_vec = A1_vec + 2*A2_vec
        
        print(f"  向量输入: A1={A1_vec}, A2={A2_vec}")
        print(f"  向量结果: {y_vec}")
        print(f"  期望结果: {expected_vec}")
        print(f"  向量误差: {np.abs(y_vec - expected_vec)}")
        
        print("✓ 改进后的二维插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的二维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_improved_precision_comparison():
    """测试改进后的精度对比"""
    print("\n" + "=" * 60)
    print("测试改进后的插值精度对比")
    print("=" * 60)
    
    try:
        # 一维精度测试
        print("一维插值精度测试:")
        xx = np.linspace(0, 1, 11)
        yy = np.sin(np.pi * xx)  # 正弦函数
        
        # 密集测试点
        A_dense = np.linspace(0.1, 0.9, 9)
        y_interp = dpm_interpn(xx, yy, A_dense)
        y_exact = np.sin(np.pi * A_dense)
        
        errors_1d = np.abs(y_interp - y_exact)
        print(f"  一维插值最大误差: {np.max(errors_1d):.8f}")
        print(f"  一维插值平均误差: {np.mean(errors_1d):.8f}")
        
        # 二维精度测试
        print("\n二维插值精度测试:")
        xx1 = np.linspace(0, 1, 6)
        xx2 = np.linspace(0, 1, 6)
        
        # 创建正弦函数值矩阵
        YY = np.zeros((6, 6))
        for i2, x2 in enumerate(xx2):
            for i1, x1 in enumerate(xx1):
                YY[i2, i1] = np.sin(np.pi * x1) * np.cos(np.pi * x2)
        
        # 密集测试点
        A1_dense = np.linspace(0.1, 0.9, 5)
        A2_dense = np.linspace(0.1, 0.9, 5)
        A1_mesh, A2_mesh = np.meshgrid(A1_dense, A2_dense, indexing='ij')
        
        y_interp_2d = dpm_interpn(xx1, xx2, YY, A1_mesh, A2_mesh)
        y_exact_2d = np.sin(np.pi * A1_mesh) * np.cos(np.pi * A2_mesh)
        
        errors_2d = np.abs(y_interp_2d - y_exact_2d)
        print(f"  二维插值最大误差: {np.max(errors_2d):.8f}")
        print(f"  二维插值平均误差: {np.mean(errors_2d):.8f}")
        
        print("✓ 改进后的插值精度测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的插值精度测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("改进后的一维和二维插值函数专项测试程序")
    print("这个程序测试根据MATLAB版本改进后的 dpm_interpn 一维和二维插值功能")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 一维插值
    if test_improved_1d_interpolation():
        success_count += 1
    
    # 测试2: 二维插值
    if test_improved_2d_interpolation():
        success_count += 1
    
    # 测试3: 精度对比
    if test_improved_precision_comparison():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有改进后的一维和二维插值测试都通过了！函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查改进后的插值实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
