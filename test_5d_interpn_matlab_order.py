#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的dpm_interpn五维插值函数
验证参数顺序与MATLAB的dpm_interpn保持一致

MATLAB参数顺序: dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
Python参数顺序: dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
"""

import numpy as np
from dpm import dpm_interpn


def test_simple_5d_case():
    """测试简单的五维插值案例"""
    print("测试简单的五维插值案例")
    print("=" * 50)
    
    # 创建简单的2x2x2x2x2网格
    xx1 = np.array([0, 1])  # 第一维网格（第五维索引）
    xx2 = np.array([0, 1])  # 第二维网格（第四维索引）
    xx3 = np.array([0, 1])  # 第三维网格（第三维索引）
    xx4 = np.array([0, 1])  # 第四维网格（第二维索引）
    xx5 = np.array([0, 1])  # 第五维网格（第一维索引）
    
    # 创建简单的值矩阵
    # YY[i,j,k,l,m] 表示在 (xx5[i], xx4[j], xx3[k], xx2[l], xx1[m]) 处的值
    YY = np.zeros((2, 2, 2, 2, 2))
    for i in range(2):
        for j in range(2):
            for k in range(2):
                for l in range(2):
                    for m in range(2):
                        YY[i, j, k, l, m] = i + j + k + l + m  # 简单的线性函数
    
    print(f"xx1 (第五维): {xx1}")
    print(f"xx2 (第四维): {xx2}")
    print(f"xx3 (第三维): {xx3}")
    print(f"xx4 (第二维): {xx4}")
    print(f"xx5 (第一维): {xx5}")
    print(f"YY 矩阵形状: {YY.shape}")
    print(f"YY[0,0,0,0,0] = {YY[0,0,0,0,0]} (xx5=0, xx4=0, xx3=0, xx2=0, xx1=0)")
    print(f"YY[1,1,1,1,1] = {YY[1,1,1,1,1]} (xx5=1, xx4=1, xx3=1, xx2=1, xx1=1)")
    
    # 测试插值点
    A1 = np.array([0.5])  # 第一维查询点（第五维方向）
    A2 = np.array([0.5])  # 第二维查询点（第四维方向）
    A3 = np.array([0.5])  # 第三维查询点（第三维方向）
    A4 = np.array([0.5])  # 第四维查询点（第二维方向）
    A5 = np.array([0.5])  # 第五维查询点（第一维方向）
    
    print(f"\n查询点: A1={A1[0]} (第一维), A2={A2[0]} (第二维), A3={A3[0]} (第三维), A4={A4[0]} (第四维), A5={A5[0]} (第五维)")
    
    # 使用新的参数顺序调用：dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
    result = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
    
    print(f"插值结果: {result[0]}")
    
    # 期望值：A5 + A4 + A3 + A2 + A1 = 0.5 + 0.5 + 0.5 + 0.5 + 0.5 = 2.5
    expected = A5[0] + A4[0] + A3[0] + A2[0] + A1[0]
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ 简单五维测试通过")
        return True
    else:
        print("✗ 简单五维测试失败")
        return False


def test_corner_points_5d():
    """测试五维角点"""
    print("\n测试五维角点")
    print("=" * 50)
    
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    xx4 = np.array([0, 1])
    xx5 = np.array([0, 1])
    
    # 创建简单的值矩阵
    YY = np.zeros((2, 2, 2, 2, 2))
    value = 0
    for i in range(2):
        for j in range(2):
            for k in range(2):
                for l in range(2):
                    for m in range(2):
                        YY[i, j, k, l, m] = value
                        value += 1
    
    print(f"YY 矩阵形状: {YY.shape}")
    print(f"YY 矩阵值范围: {np.min(YY)} 到 {np.max(YY)}")
    
    # 测试几个角点
    test_points = [
        (np.array([0]), np.array([0]), np.array([0]), np.array([0]), np.array([0]), YY[0,0,0,0,0]),  # (A5=0, A4=0, A3=0, A2=0, A1=0)
        (np.array([1]), np.array([0]), np.array([0]), np.array([0]), np.array([0]), YY[0,0,0,0,1]),  # (A5=0, A4=0, A3=0, A2=0, A1=1)
        (np.array([0]), np.array([1]), np.array([0]), np.array([0]), np.array([0]), YY[0,0,0,1,0]),  # (A5=0, A4=0, A3=0, A2=1, A1=0)
        (np.array([1]), np.array([1]), np.array([1]), np.array([1]), np.array([1]), YY[1,1,1,1,1]),  # (A5=1, A4=1, A3=1, A2=1, A1=1)
    ]
    
    all_passed = True
    for i, (A1, A2, A3, A4, A5, expected) in enumerate(test_points):
        result = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
        error = abs(result[0] - expected)
        print(f"角点 {i+1}: A1={A1[0]}, A2={A2[0]}, A3={A3[0]}, A4={A4[0]}, A5={A5[0]} -> 结果={result[0]}, 期望={expected}, 误差={error}")
        if error > 1e-10:
            all_passed = False
    
    if all_passed:
        print("✓ 五维角点测试通过")
        return True
    else:
        print("✗ 五维角点测试失败")
        return False


def test_center_point_5d():
    """测试五维中心点插值"""
    print("\n测试五维中心点插值")
    print("=" * 50)
    
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    xx4 = np.array([0, 1])
    xx5 = np.array([0, 1])
    
    # 使用简单的值矩阵
    YY = np.zeros((2, 2, 2, 2, 2))
    value = 0
    for i in range(2):
        for j in range(2):
            for k in range(2):
                for l in range(2):
                    for m in range(2):
                        YY[i, j, k, l, m] = value
                        value += 1
    
    # 测试中心点
    A1 = np.array([0.5])
    A2 = np.array([0.5])
    A3 = np.array([0.5])
    A4 = np.array([0.5])
    A5 = np.array([0.5])
    
    result = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
    
    # 手动计算五线性插值
    # 32个角点的值：0, 1, 2, ..., 31
    # 在中心点(0.5, 0.5, 0.5, 0.5, 0.5)处，每个角点的权重都是1/32
    expected = np.mean(YY)  # 所有值的平均值
    
    print(f"中心点: A1={A1[0]}, A2={A2[0]}, A3={A3[0]}, A4={A4[0]}, A5={A5[0]}")
    print(f"结果: {result[0]}")
    print(f"期望: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ 五维中心点测试通过")
        return True
    else:
        print("✗ 五维中心点测试失败")
        return False


def test_matlab_consistency_5d():
    """测试与MATLAB的一致性"""
    print("\n测试与MATLAB的一致性")
    print("=" * 50)
    
    # 使用与MATLAB测试相同的数据
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    xx4 = np.array([0, 1])
    xx5 = np.array([0, 1])
    
    # 创建线性函数：f(x5, x4, x3, x2, x1) = x5 + x4 + x3 + x2 + x1
    YY = np.zeros((len(xx5), len(xx4), len(xx3), len(xx2), len(xx1)))
    for i in range(len(xx5)):
        for j in range(len(xx4)):
            for k in range(len(xx3)):
                for l in range(len(xx2)):
                    for m in range(len(xx1)):
                        YY[i, j, k, l, m] = xx5[i] + xx4[j] + xx3[k] + xx2[l] + xx1[m]
    
    print(f"xx1: {xx1}")
    print(f"xx2: {xx2}")
    print(f"xx3: {xx3}")
    print(f"xx4: {xx4}")
    print(f"xx5: {xx5}")
    print(f"YY 矩阵形状: {YY.shape}")
    
    # 测试点
    A1 = np.array([0.3])  # 第一维查询点
    A2 = np.array([0.7])  # 第二维查询点
    A3 = np.array([0.2])  # 第三维查询点
    A4 = np.array([0.8])  # 第四维查询点
    A5 = np.array([0.4])  # 第五维查询点
    
    result = dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
    
    # 期望值：A5 + A4 + A3 + A2 + A1 = 0.4 + 0.8 + 0.2 + 0.7 + 0.3 = 2.4
    expected = A5[0] + A4[0] + A3[0] + A2[0] + A1[0]
    
    print(f"查询点: A1={A1[0]}, A2={A2[0]}, A3={A3[0]}, A4={A4[0]}, A5={A5[0]}")
    print(f"插值结果: {result[0]}")
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ MATLAB一致性测试通过")
        return True
    else:
        print("✗ MATLAB一致性测试失败")
        return False


def main():
    """主测试函数"""
    print("测试修改后的dpm_interpn五维插值函数")
    print("验证参数顺序：dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)")
    print()
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 简单案例
    if test_simple_5d_case():
        success_count += 1
    
    # 测试2: 角点测试
    if test_corner_points_5d():
        success_count += 1
    
    # 测试3: 中心点测试
    if test_center_point_5d():
        success_count += 1
    
    # 测试4: MATLAB一致性
    if test_matlab_consistency_5d():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！五维插值参数顺序修改成功。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
