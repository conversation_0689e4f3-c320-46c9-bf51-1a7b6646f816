#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态规划矩阵运算

"""

import numpy as np
import warnings
import os
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from dataclasses import dataclass, field
import copy


@dataclass
class Grid:
    """网格结构定义"""
    X0: Dict[int, float] = field(default_factory=dict)  # 初始状态
    XN: Dict[int, Dict[str, float]] = field(default_factory=dict)  # 最终状态约束
    Nx: Dict[int, Union[int, List[int]]] = field(default_factory=dict)  # 状态网格元素数量
    Xn: Dict[int, Dict[str, Union[float, List[float]]]] = field(default_factory=dict)  # 状态网格边界
    Nu: Dict[int, Union[int, List[int]]] = field(default_factory=dict)  # 输入网格元素数量
    Un: Dict[int, Dict[str, Union[float, List[float]]]] = field(default_factory=dict)  # 输入网格边界


@dataclass
class Problem:
    """问题参数结构"""
    Ts: float  # 时间步长
    N: int  # 问题中的时间步数
    N0: int = 1  # 起始时间索引
    W: Dict[int, np.ndarray] = field(default_factory=dict)  # 扰动向量


@dataclass
class Options:
    """选项结构"""
    Waitbar: str = 'off'  # 'on'/'off' 显示或隐藏等待条
    Verbose: str = 'on'  # 'on'/'off' 命令窗口状态通知
    Warnings: str = 'on'  # 'on'/'off' 显示或隐藏警告
    SaveMap: Union[str, int] = 'on'  # 'on'/'off' 保存代价函数映射
    MyInf: float = 1e4  # 不可行状态的大数值
    Minimize: int = 1  # 1/0 最小化或最大化
    InputType: str = 'c'  # 输入类型字符串 ('c'连续, 'd'离散)
    BoundaryMethod: str = 'none'  # 边界方法: 'none', 'Line' 或 'LevelSet'
    FixedGrid: int = 0  # 0/1 使用GRD中定义的网格或根据边界线调整
    Iter: int = 10  # 反演模型时的最大迭代次数
    Tol: float = 1e-8  # 反演模型时的最小容差
    gN: Dict[int, np.ndarray] = field(default_factory=dict)  # 最终时刻的代价矩阵


@dataclass
class Input:
    """输入结构"""
    X: Dict[int, np.ndarray] = field(default_factory=dict)  # 状态
    U: Dict[int, np.ndarray] = field(default_factory=dict)  # 输入
    W: Dict[int, float] = field(default_factory=dict)  # 扰动
    Ts: float = 0.0  # 时间步长


@dataclass
class Output:
    """输出结构"""
    X: Dict[int, np.ndarray] = field(default_factory=dict)  # 下一状态
    C: Dict[int, np.ndarray] = field(default_factory=dict)  # 代价
    I: np.ndarray = field(default_factory=lambda: np.array([0]))  # 不可行性标志


@dataclass
class DynamicResult:
    """动态规划结果结构"""
    Jo: Union[Dict[int, np.ndarray], List] = field(default_factory=dict)  # 最优代价函数
    Uo: Dict = field(default_factory=dict)  # 最优输入映射


def dpm(*args, **kwargs):
    """
    DPM主函数 - 动态规划矩阵运算版本 1.1.1

    """
    try:
        varargout = []

        if len(args) == 3:
            # DPM(FUN,NX,NU) - 生成测试模型
            filename, nx, nu = args
            if not os.path.exists(f"{filename}.py"):
                _generate_test_model(filename, nx, nu)
                return
            else:
                raise ValueError(f"DPM:Internal - 文件名 {filename}.py 已存在于当前目录")

        elif len(args) == 4:
            # DPM(model, par, grd, dis) - 获取空输出结构
            model, par, grd, dis = args
            inp = dpm_get_empty_inp(grd, dis, 'zero')
            varargout.append(dpm_get_empty_out(model, inp, par, grd, 'zero'))
            return varargout[0] if len(varargout) == 1 else varargout

        elif len(args) == 2:
            raise ValueError("DPM:Internal - Grid generation is not supported anymore, grd = dpm(Xset,Uset);")

        elif len(args) == 0:
            # DPM() - 返回默认选项结构
            options = Options()
            options.Waitbar = 'off'
            options.Verbose = 'on'
            options.Warnings = 'on'
            options.SaveMap = 'on'
            options.MyInf = 1e4
            options.Minimize = 1
            options.BoundaryMethod = 'none'
            return options

        elif len(args) == 5:
            # DPM(FUN,PAR,GRD,PRB,OPTIONS) - 完整动态规划
            run_forward = True  # RunForward  = nargout > 1；nargout=2,先默认运行前向仿真
            run_backward = True
            model, par, grd, dis, options = args

        elif len(args) == 6:
            # DPM(DYN,FUN,PAR,GRD,PRB,OPTIONS) - 仅前向仿真
            run_forward = True
            run_backward = False
            dyn, model, par, grd, dis, options = args

        elif len(args) == 7:
            # DPM(N0,DYN,FUN,PAR,GRD,PRB,OPTIONS) - 从N0开始前向仿真
            run_forward = True
            run_backward = False
            t0, dyn, model, par, grd, dis, options = args

        else:
            raise ValueError("参数数量不正确")

        # 检查所有输入
        if len(args) in [4, 5, 6, 7]:
            # 如果未设置扰动向量
            if not hasattr(dis, 'W'):
                dis.W = {}
            grd = input_check_grd(grd, dis.N)

        # 处理选项
        if 'options' in locals():
            options = _process_options(options, grd)

        # 动态规划求解
        if run_backward:
            # 检查状态边界
            _check_state_boundaries(grd)

            # 检查最终状态约束
            if not hasattr(options, 'CalcLine') or not options.CalcLine:
                _check_final_constraints(grd, options)

            dyn = dpm_backward(model, par, grd, dis, options)

        # 前向仿真
        if run_forward:
            try:
                out = dpm_forward(dyn, model, par, grd, dis, options)
                varargout.append(out)
            except Exception as e:
                print("DPM:前向仿真错误\n\t 确保问题是可行的。")
                inp = dpm_get_empty_inp(grd, dis, 'zero')
                out = dpm_get_empty_out(model, inp, par, grd, 'nan')
                varargout.append(out)

        varargout.append(dyn)

        # 关闭警告
        warnings.filterwarnings('ignore', category=UserWarning, module='dpm')

        # 根据返回值数量决定返回格式
        if len(varargout) == 1:
            return varargout[0]
        elif len(varargout) == 2:
            return varargout[0], varargout[1]
        else:
            return tuple(varargout)

    except Exception as e:
        # 错误处理
        print(f"DPM错误: {str(e)}")
        notify_user_of_error(e)
        return [None] * max(1, len(args))


def _generate_test_model(filename: str, nx: int, nu: int) -> None:
    """
    生成测试模型文件

    参数:
        filename: 文件名
        nx: 状态数量
        nu: 输入数量
    """
    model_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的测试模型文件
状态数量: {nx}
输入数量: {nu}
"""

import numpy as np
from dpm import Input, Output

def {filename}(inp: Input, par=None):
    """
    测试模型函数

    参数:
        inp: 输入结构
            inp.X{{i}} 状态
            inp.U{{i}} 输入
            inp.W{{i}} 扰动 (如dis结构中定义)
            inp.Ts   时间步长
        par: 包含用户定义参数的结构

    返回:
        X: 状态更新 (必须在模型函数中设置)
        C: 代价 (必须在模型函数中设置)
        I: 不可行性 (0=可行/1=不可行，必须在模型函数中设置)
        signals: 用户定义的输出信号
    """

    # 状态更新 (out.X{{i}} 必须在模型函数中设置)
    X = {{}}
'''

    # 生成状态更新方程
    for i in range(1, nx + 1):
        x = np.random.randint(1, nx + 1)
        u = np.random.randint(1, nu + 1)
        rand_coeff = np.random.random()
        model_code += f'    X[{i}] = {rand_coeff:.6f} * (inp.X[{x}] + inp.U[{u}] + inp.W[1]) / inp.Ts + inp.X[{i}]\n'

    model_code += '''
    # 代价函数 (out.C{1} 必须在模型函数中设置)
    C = {1: -inp.Ts * inp.U[1]}

    # 不可行性检查 (out.I [0=可行/1=不可行] 必须在模型函数中设置)
    # 例如，如果状态超出网格范围或出现不可行的输入状态组合
    # 这些状态输入组合的代价将被DPM函数设置为options.MyInf
    I = 0

    # 存储信号 (在out结构中存储任何其他信号)
    signals = {}
'''

    # 生成signals输出
    for i in range(1, nu + 1):
        model_code += f'    signals["U{i}"] = inp.U[{i}]\n'

    model_code += '''
    return X, C, I, signals
'''

    with open(f"{filename}.py", 'w', encoding='utf-8') as f:
        f.write(model_code)
    
    print(f"测试模型已生成: {filename}.py")


def _process_options(options: Options, grd=None) -> Options:
    """
    处理和验证选项结构

    参数:
        options: 输入选项
        grd: 网格结构

    返回:
        处理后的options
    """
    if not hasattr(options, 'CalcLine'):

        # 向后兼容性检查
        if hasattr(options, 'InfCost'):
            warnings.warn("DPM:Internal - 选项 'InfCost' 已重命名为 'MyInf'。请考虑调整您的代码。")
            options.MyInf = options.InfCost
            delattr(options, 'InfCost')

        if hasattr(options, 'BoundaryLineMethod'):
            warnings.warn("DPM:Internal - 选项 'BoundaryLineMethod' 已重命名为 'BoundaryMethod'。请考虑调整您的代码。")
            options.BoundaryMethod = options.BoundaryLineMethod
            delattr(options, 'BoundaryLineMethod')

        if hasattr(options, 'HideWaitbar'):
            warnings.warn("DPM:Internal - 选项 'HideWaitbar' 已重命名为 'Waitbar'。请考虑调整您的代码。")
            options.Waitbar = 'off' if options.HideWaitbar else 'on'
            delattr(options, 'HideWaitbar')

        if hasattr(options, 'UseLine'):
            warnings.warn("DPM:Internal - 选项 'UseLine' 已重命名为 'BoundaryMethod'。请考虑调整您的代码。")
            options.BoundaryMethod = 'Line' if options.UseLine else 'none'
            delattr(options, 'UseLine')

        # 选项完整性检查
        valid_options = ['Waitbar', 'Verbose', 'Warnings', 'SaveMap', 'MyInf', 'Minimize',
                        'BoundaryMethod', 'Iter', 'Tol', 'FixedGrid', 'gN', 'InputType',
                        'CalcLine', 'UseUmap', 'UseLine', 'UseLevelSet']

        for attr_name in dir(options):
            if not attr_name.startswith('_') and attr_name not in valid_options:
                warnings.warn(f"DPM:Internal - 未知选项: '{attr_name}'!")

        # 设置计算线标志
        options.CalcLine = 0

        # 处理SaveMap选项
        if not hasattr(options, 'SaveMap'):
            options.SaveMap = 0
        else:
            # 向后兼容性
            if not isinstance(options.SaveMap, (int, float)):
                if options.SaveMap == 'on':
                    options.SaveMap = 1
                elif options.SaveMap == 'off':
                    options.SaveMap = 0
                else:
                    warnings.warn("DPM:Internal - 无法解释选项 'SaveMap'。使用 SaveMap = 'on'!")
                    options.SaveMap = 1
            elif options.SaveMap == 'on':
                options.SaveMap = 0

        # 解释边界方法用户输入
        if not hasattr(options, 'BoundaryMethod'):
            options.BoundaryMethod = ''

        if options.BoundaryMethod == 'none' or options.BoundaryMethod == '':
            options.UseLine = 0
            options.UseLevelSet = 0
        elif options.BoundaryMethod == 'Line':
            options.UseLine = 1
            options.UseLevelSet = 0
            options.UseUmap = 1
        elif options.BoundaryMethod == 'LevelSet':
            options.UseLine = 0
            options.UseLevelSet = 1
            options.UseUmap = 0
        else:
            print("边界方法未指定！使用 BoundaryMethod = 'none'。")
            options.BoundaryMethod = 'none'
            options.UseLine = 0
            options.UseLevelSet = 0

        if not hasattr(options, 'UseUmap'):
            options.UseUmap = 1

        # 边界线方法
        if hasattr(options, 'UseLine') and options.UseLine:
            if grd and len(grd.Nx) > 1:
                warnings.warn("DPM:Internal - 边界线方法仅适用于一维系统。请考虑使用水平集方法。")
            if not hasattr(options, 'FixedGrid'):
                warnings.warn("DPM:Internal - 网格适应未指定。使用 options.FixedGrid=1。")
                options.FixedGrid = 1
            if not hasattr(options, 'Tol'):
                warnings.warn("DPM:Internal - 模型反演需要指定容差。使用 options.Tol=1e-8。")
                options.Tol = 1e-8
            if not hasattr(options, 'Iter'):
                warnings.warn("DPM:Internal - 模型反演需要指定最大迭代次数。使用 options.Iter=10。")
                options.Iter = 10

        # 水平集方法
        if hasattr(options, 'UseLevelSet') and options.UseLevelSet:
            if grd and len(grd.Nx) == 1:
                print("对于一维系统，请考虑使用边界线方法。")
            if not options.SaveMap:
                warnings.warn("DPM:Internal - 水平集方法需要代价函数用于前向仿真。设置 SaveMap = 'on'。")
                options.SaveMap = 1
            options.UseUmap = 0

    return options


def _check_state_boundaries(grd: Grid):
    """
    检查状态边界的有效性

    参数:
        grd: 网格结构
    """
    for i in range(1, len(grd.Xn) + 1):
        if i in grd.Xn:
            # 检查每个时间步的边界
            lo_array = np.array(grd.Xn[i]['lo'])
            hi_array = np.array(grd.Xn[i]['hi'])

            # 检查是否有任何下边界大于上边界
            if np.any(lo_array > hi_array):
                if len(grd.Xn[i]['lo']) > 1:
                    # 找到第一个违反的索引
                    violation_idx = np.where(lo_array > hi_array)[0][0]
                    raise ValueError(f"DPM:Internal - 状态 {i} 在实例 {violation_idx+1} 的上边界小于下边界。")
                else:
                    raise ValueError(f"DPM:Internal - 状态 {i} 的上边界小于下边界。")


def _check_final_constraints(grd: Grid, options: Options):
    """
    检查最终状态约束

    参数:
        grd: 网格结构
        options: 选项
    """
    # 只检查第一个状态
    if (hasattr(options, 'FixedGrid') and not options.FixedGrid and
        1 in grd.XN and 1 in grd.Nx):
        constraint_width = np.min(grd.XN[1]['hi'] - grd.XN[1]['lo'])
        nx_val = grd.Nx[1]
        if hasattr(nx_val, '__len__') and not isinstance(nx_val, str):
            nx_val = int(nx_val[-1])
        else:
            nx_val = int(nx_val)
        if constraint_width / np.finfo(float).eps < nx_val:
            raise ValueError("DPM:Internal - 最终状态约束太紧，无法包含 grd.Nx{1}(end) 个点。\n\t 放宽最终约束。")


def dpm_backward(model: Union[str, Callable], par: Any, grd: Grid,
                 dis: Problem, options: Options) -> DynamicResult:
    """
    DP_BACKWARD 计算最优输入矩阵和最优代价函数

    参数:
        model: 模型函数
        par: 参数
        grd: 网格结构
        dis: 问题参数
        options: 选项

    返回:
        dyn: 动态规划结果

    """

    # 初始化最优输入和代价映射
    x_sze = np.ones(len(grd.Nx), dtype=int)
    for i in range(len(grd.Nx)):
        nx_val = grd.Nx[i+1]
        if hasattr(nx_val, '__len__') and not isinstance(nx_val, str):
            x_sze[i] = int(nx_val[-1])
        else:
            x_sze[i] = int(nx_val)

    u_sze = np.ones(len(grd.Nu), dtype=int)
    for i in range(len(grd.Nu)):
        nu_val = grd.Nu[i+1]
        if hasattr(nu_val, '__len__') and not isinstance(nu_val, str):
            u_sze[i] = int(nu_val[-1])
        else:
            u_sze[i] = int(nu_val)

    # 初始化动态规划结果
    dyn = DynamicResult()

    # 如果使用边界线方法
    if hasattr(options, 'UseLine') and options.UseLine:
        dyn.B = {'lo': dpm_boundary_line_lower(model, par, grd, dis, options),
                 'hi': dpm_boundary_line_upper(model, par, grd, dis, options)}

    # 生成当前状态网格
    current_grd = {'X': {}, 'U': {}}
    for i in range(1, len(grd.Nx) + 1):
        if (hasattr(options, 'UseLine') and options.UseLine and i == 1 and
            hasattr(options, 'FixedGrid') and not options.FixedGrid):
            if len(grd.Nx) == 1:
                nx_val = grd.Nx[i]
                if hasattr(nx_val, '__len__') and not isinstance(nx_val, str):
                    nx_val = int(nx_val[-1])
                else:
                    nx_val = int(nx_val)
                current_grd['X'][i] = np.linspace(dyn.B['lo'].Xo[-1], dyn.B['hi'].Xo[-1], nx_val)
            else:
                nx_val = grd.Nx[i]
                if hasattr(nx_val, '__len__') and not isinstance(nx_val, str):
                    nx_val = int(nx_val[-1])
                else:
                    nx_val = int(nx_val)
                current_grd['X'][i] = np.linspace(np.min(dyn.B['lo'].Xo[:, i-1]),
                                                 np.max(dyn.B['hi'].Xo[:, i-1]), nx_val)
        else:
            xn_lo = grd.Xn[i]['lo']
            if hasattr(xn_lo, '__len__') and not isinstance(xn_lo, str):
                xn_lo = float(xn_lo[-1])
            else:
                xn_lo = float(xn_lo)

            xn_hi = grd.Xn[i]['hi']
            if hasattr(xn_hi, '__len__') and not isinstance(xn_hi, str):
                xn_hi = float(xn_hi[-1])
            else:
                xn_hi = float(xn_hi)

            nx_val = grd.Nx[i]
            if hasattr(nx_val, '__len__') and not isinstance(nx_val, str):
                nx_val = int(nx_val[-1])
            else:
                nx_val = int(nx_val)

            current_grd['X'][i] = np.linspace(xn_lo, xn_hi, nx_val)
            if hasattr(options, 'CalcLine') and options.CalcLine:
                if hasattr(grd, 'Xn_true') and 1 in grd.Xn_true:
                    grd.Xn[1]['lo'] = grd.Xn_true[1]['lo']
                    grd.Xn[1]['hi'] = grd.Xn_true[1]['hi']

    # 生成当前输入网格
    for i in range(1, len(grd.Nu) + 1):
        un_lo = grd.Un[i]['lo']
        if hasattr(un_lo, '__len__') and not isinstance(un_lo, str):
            un_lo = float(un_lo[-1])
        else:
            un_lo = float(un_lo)

        un_hi = grd.Un[i]['hi']
        if hasattr(un_hi, '__len__') and not isinstance(un_hi, str):
            un_hi = float(un_hi[-1])
        else:
            un_hi = float(un_hi)

        nu_val = grd.Nu[i]
        if hasattr(nu_val, '__len__') and not isinstance(nu_val, str):
            nu_val = int(nu_val[-1])
        else:
            nu_val = int(nu_val)

        current_grd['U'][i] = np.linspace(un_lo, un_hi, nu_val)

    # 获取空的输入输出结构
    inp0 = dpm_get_empty_inp(grd, dis, 'zero')
    out0 = dpm_get_empty_out(model, inp0, par, grd, 'zero')

    # 初始化输出 dyn.Uo 和 dyn.Jo
    dyn.Jo = {}
    for i in range(1, len(out0.C) + 1):
        # 如果在选项中指定了 dyn.Jo
        if (hasattr(options, 'gN') and len(options.gN)>= i):
            if dpm_sizecmp(options.gN[i], np.zeros(get_size(current_grd))):
                # 处理标量和数组的复制
                if np.isscalar(options.gN[i]):
                    dyn.Jo[i] = np.full(get_size(current_grd), options.gN[i])
                else:
                    dyn.Jo[i] = np.array(options.gN[i])
            else:
                raise ValueError(f"DPM:Internal - options.gN[{i}] 维度不正确")
        # 如果在选项中未指定 dyn.Jo
        else:
            dyn.Jo[i] = np.zeros(get_size(current_grd))

    # 如果不使用边界线，设置可行区域外的状态代价为无穷大
    if (not (hasattr(options, 'UseLine') and options.UseLine) and
        not (hasattr(options, 'CalcLine') and options.CalcLine) and
        not (hasattr(options, 'UseLevelSet') and options.UseLevelSet)):
        for i in range(1, len(grd.Nx) + 1):
            if i in grd.XN:
                # 设置超出最终约束的状态为无穷大
                # 根据维度动态生成索引
                x_grid = current_grd['X'][i]

                mask_hi = x_grid > grd.XN[i]['hi']
                mask_lo = x_grid < grd.XN[i]['lo']

                # 根据维度应用掩码
                if len(grd.Nx) == 1:
                    dyn.Jo[1][mask_hi] = options.MyInf
                    dyn.Jo[1][mask_lo] = options.MyInf
                else:
                    # 多维情况：需要在正确的维度上应用掩码
                    # 创建完整的索引元组
                    full_shape = dyn.Jo[1].shape
                    slices = [slice(None)] * len(full_shape)

                    # 在第i维度上应用掩码
                    slices[i-1] = mask_hi
                    dyn.Jo[1][tuple(slices)] = options.MyInf

                    slices[i-1] = mask_lo
                    dyn.Jo[1][tuple(slices)] = options.MyInf

    # 水平集方法设置
    if hasattr(options, 'UseLevelSet') and options.UseLevelSet:
        # 设置水平集函数
        next_idx = len(dyn.Jo) + 1
        dyn.Jo[next_idx] = np.full(get_size(current_grd), -np.inf)

        # 通过 V_N = h(x_N) 初始化
        if len(grd.Nx) > 1:
            # 多维网格生成
            x_grids = []
            for i in range(1, len(grd.Nx) + 1):
                x_grids.append(current_grd['X'][i])
            x_meshes = np.meshgrid(*x_grids, indexing='ij')

            # 将网格存储为字典
            x = {}
            for i in range(1, len(grd.Nx) + 1):
                x[i] = x_meshes[i-1]

            # 应用约束
            for i in range(1, len(x) + 1):
                if i in grd.XN:
                    constraint_lo = grd.XN[i]['lo'] - x[i]
                    constraint_hi = x[i] - grd.XN[i]['hi']
                    dyn.Jo[next_idx] = np.maximum(dyn.Jo[next_idx],
                                                 np.maximum(constraint_lo, constraint_hi))
        else:
            # 一维情况
            x = {1: current_grd['X'][1]}
            if 1 in grd.XN:
                constraint_lo = grd.XN[1]['lo'] - x[1]
                constraint_hi = x[1] - grd.XN[1]['hi']
                dyn.Jo[next_idx] = np.maximum(constraint_lo, constraint_hi)

    # 如果需要保存整个代价函数映射，进行初始化
    V_map = None
    if hasattr(options, 'SaveMap') and options.SaveMap:
        V_map = {}
        for i in range(1, len(dyn.Jo) + 1):
            V_map[i] = {}
            for j in range(1, dis.N + 2):  # dis.N+1 个时间步
                x_sze_n = []
                for k in range(1, len(grd.Nx) + 1):
                    x_sze_n.append(grd.Nx[k][j-1] if j-1 < len(grd.Nx[k]) else grd.Nx[k][-1])

                if len(x_sze_n) == 1:
                    x_sze_n.append(1)

                V_map[i][j] = np.full(x_sze_n, np.nan)

            # 设置最终时刻的值
            V_map[i][dis.N + 1] = dyn.Jo[i].copy()

    # 初始化最优输入映射
    dyn.Uo = {}
    for i in range(1, len(grd.Nu) + 1):
        dyn.Uo[i] = {}
        for j in range(1, dis.N + 1):
            x_sze_n = []
            for k in range(1, len(grd.Nx) + 1):
                x_sze_n.append(grd.Nx[k][j-1] if j-1 < len(grd.Nx[k]) else grd.Nx[k][-1])

            if len(x_sze_n) == 1:
                x_sze_n.append(1)

            dyn.Uo[i][j] = np.full(x_sze_n, np.nan)

    # 生成网格代码（用于创建状态和输入的网格组合）
    # 在循环中生成（后续）

    # 显示动态规划向后迭代的进度条
    if hasattr(options, 'Waitbar') and options.Waitbar == 'on':
        if not (hasattr(options, 'CalcLine') and options.CalcLine):
            print("DP 向后运行中。请等待...")
        else:
            print("DP 计算边界线中。请等待...")

    if hasattr(options, 'Verbose') and options.Verbose == 'on':
        if not (hasattr(options, 'CalcLine') and options.CalcLine):
            print("DP 向后运行中:     %", end='', flush=True)
        else:
            print("DP 计算边界线中:     %", end='', flush=True)

    # 检查初始值的可行性（如果使用边界线）
    if hasattr(options, 'UseLine') and options.UseLine:
        if len(grd.Nx) == 1:
            if hasattr(grd, 'X0') and 1 in grd.X0:
                # 现在Xo是1D数组，索引第一个元素（时间步0）
                isfeas = ((dyn.B['lo'].Xo[0] > grd.X0[1]) |
                         (dyn.B['hi'].Xo[0] < grd.X0[1]))
                if isfeas:
                    warnings.warn("DPM:Backward - Initial value not feasible!")
        elif len(grd.Nx) == 2:
            if hasattr(grd, 'X0') and 1 in grd.X0 and 2 in grd.X0:
                lo_interp = dpm_interpn(current_grd['X'][2], dyn.B['lo'].Xo[:, :, 0], grd.X0[2])
                hi_interp = dpm_interpn(current_grd['X'][2], dyn.B['hi'].Xo[:, :, 0], grd.X0[2])
                isfeas = (lo_interp > grd.X0[1]) | (hi_interp < grd.X0[1])
                if np.sum(isfeas.flatten()) != 0:
                    warnings.warn("DPM:Backward - Initial value not feasible!")

    # 警告标志
    iswarned = False

    # 开始动态规划向后循环
    n = dis.N + 1
    while n > 1:
        n = n - 1

        previous_grd = copy.deepcopy(current_grd)
        x_sze_current = np.full(len(grd.Nx), np.nan)
        u_sze_current = np.full(len(grd.Nu), np.nan)

        # 更新当前时刻的状态网格
        for i in range(1, len(grd.Nx) + 1):
            if (hasattr(options, 'UseLine') and options.UseLine and i == 1 and
                hasattr(options, 'FixedGrid') and not options.FixedGrid):
                if len(grd.Nx) == 1:
                    current_grd['X'][i] = np.linspace(dyn.B['lo'].Xo[n-1],
                                                     dyn.B['hi'].Xo[n-1],
                                                     grd.Nx[i][n-1])
                else:
                    lo_min = np.min(np.min(dyn.B['lo'].Xo[0, :, n-1:n+1]))
                    hi_max = np.max(np.min(dyn.B['hi'].Xo[0, :, n-1:n+1]))
                    current_grd['X'][i] = np.linspace(lo_min, hi_max, grd.Nx[i][n-1])
            elif not (hasattr(options, 'CalcLine') and options.CalcLine) or i != 1:
                current_grd['X'][i] = np.linspace(grd.Xn[i]['lo'][n-1],
                                                 grd.Xn[i]['hi'][n-1],
                                                 grd.Nx[i][n-1])
            x_sze_current[i-1] = grd.Nx[i][n-1]

        # 更新当前时刻的输入网格
        for i in range(1, len(grd.Nu) + 1):
            current_grd['U'][i] = np.linspace(grd.Un[i]['lo'][n-1],
                                             grd.Un[i]['hi'][n-1],
                                             grd.Nu[i][n-1])
            u_sze_current[i-1] = grd.Nu[i][n-1]

        # 生成输入和状态网格组合：eval(code_generate_grid)
        X_grids = [current_grd['X'][i] for i in range(1, len(grd.Nx) + 1)]
        U_grids = [current_grd['U'][i] for i in range(1, len(grd.Nu) + 1)]
        all_grids = X_grids + U_grids

        if len(all_grids) > 1:
            meshes = np.meshgrid(*all_grids, indexing='ij')
            inp = Input()
            for i in range(len(X_grids)):
                inp.X[i+1] = meshes[i]
            for i in range(len(U_grids)):
                inp.U[i+1] = meshes[len(X_grids) + i]
        else:
            inp = Input()
            inp.X[1] = X_grids[0] if X_grids else np.array([0])
            inp.U[1] = U_grids[0] if U_grids else np.array([0])

        # 如果计算边界线，初始化第一个输入状态
        if hasattr(options, 'CalcLine') and options.CalcLine:
            # repmat(dyn.Jo{1},[ones(1,length(grd.Nx)) ...])
            if 1 in dyn.Jo:
                base_shape = [1] * len(grd.Nx)
                for i in range(1, len(grd.Nu) + 1):
                    base_shape.append(len(current_grd['U'][i]))

                # 重复dyn.Jo[1]以匹配完整网格形状
                inp.X[1] = np.broadcast_to(dyn.Jo[1], base_shape)

        # 生成扰动
        inp.W = {}
        for w in range(1, len(dis.W) + 1):
            if w in dis.W:
                inp.W[w] = dis.W[w][n-1] if hasattr(dis.W[w], '__len__') else dis.W[w]
        inp.Ts = dis.Ts

        # 调用模型函数
        try:
            # 调用模型函数
            if hasattr(options, 'Signals') and options.Signals:
                if callable(model):
                    X_next, C, I, signals = model(inp, par)
                else:
                    import importlib
                    module = importlib.import_module(model)
                    model_func = getattr(module, model)
                    X_next, C, I, signals = model_func(inp, par)
            else:
                if callable(model):
                    result = model(inp, par)
                    if len(result) == 3:
                        X_next, C, I = result
                    elif len(result) == 4:
                        X_next, C, I, out = result
                    else:
                        raise ValueError(f"模型函数返回了错误数量的值: {len(result)}")
                else:
                    import importlib
                    module = importlib.import_module(model)
                    model_func = getattr(module, model)
                    result = model_func(inp, par)
                    if len(result) == 3:
                        X_next, C, I = result
                    elif len(result) == 4:
                        X_next, C, I, out = result
                    else:
                        raise ValueError(f"模型函数返回了错误数量的值: {len(result)}")

            # 水平集方法计算
            if hasattr(options, 'UseLevelSet') and options.UseLevelSet:
                if n == dis.N:  # 解析检查
                    Vt = -np.inf * np.ones_like(X_next[1])
                    for i in range(1, len(grd.Nx) + 1):
                        if i in grd.XN:
                            constraint_lo = grd.XN[i]['lo'] - X_next[i]
                            constraint_hi = X_next[i] - grd.XN[i]['hi']
                            Vt = np.maximum(Vt, np.maximum(constraint_lo, constraint_hi))
                else:  # 通过插值检查
                    # 实现代价函数插值 ：(code_cost_to_go_interp{end})（代替动态函数）
                    xsize = [len(current_grd['X'][i]) for i in range(1, len(grd.Nx) + 1)]
                    interp_dims = [i for i, size in enumerate(xsize) if size > 1]

                    if interp_dims:
                        # 多维插值
                        grid_coords = []
                        interp_coords = []
                        for i in reversed(interp_dims):
                            grid_coords.append(previous_grd['X'][i+1])
                            if hasattr(options, 'CalcLine') and options.CalcLine:
                                interp_coords.append(inp.X[i+1])
                            else:
                                interp_coords.append(X_next[i+1])

                        # 对最后一个代价函数（水平集函数）进行插值
                        Vt = dpm_interpn(*grid_coords, dyn.Jo[len(dyn.Jo)], *interp_coords)
                    else:
                        # 如果没有活跃维度，直接使用数据
                        Vt = dyn.Jo[len(dyn.Jo)]

                Vt[I == 1] = options.MyInf
                if len(grd.Nu) > 1:
                    Vt = Vt.reshape([*x_sze_current.astype(int), np.prod(u_sze_current.astype(int))])

                # 计算最小值和对应索引
                dyn.Jo[len(dyn.Jo)], ub = np.min(Vt, axis=len(x_sze_current)), np.argmin(Vt, axis=len(x_sze_current))

            # 创建输出结构
            out = Output()
            out.X = X_next
            out.C = C
            out.I = I

            # 确定弧代价
            for i in range(1, len(grd.Nx) + 1):
                if i in out.X:
                    out.I = np.logical_or(out.I, out.X[i] > grd.Xn[i]['hi'][n])
                    out.I = np.logical_or(out.I, out.X[i] < grd.Xn[i]['lo'][n])

            # 计算弧代价
            J = np.where(out.I == 0, out.C[1], options.MyInf)

            # 计算整个网格的代价
            cost_to_go = {}

            if hasattr(options, 'UseLine') and options.UseLine:
                if len(grd.Nx) == 1:
                    # 使用边界线插值
                    cost_to_go[1] = dpm_interpf1mb(
                        previous_grd['X'][1], dyn.Jo[1], out.X[1],
                        [dyn.B['lo'].Xo[n], dyn.B['hi'].Xo[n]],
                        [dyn.B['lo'].Jo[n], dyn.B['hi'].Jo[n]],
                        options.MyInf
                    )
                else:
                    # 二维边界线插值
                    cost_to_go[1] = dpm_interpf2mb(
                        previous_grd['X'][1], previous_grd['X'][2], dyn.Jo[1],
                        out.X[1], out.X[2],
                        np.vstack([dyn.B['lo'].Xo[:, :, n], dyn.B['hi'].Xo[:, :, n]]),
                        np.vstack([dyn.B['lo'].Jo[:, :, n], dyn.B['hi'].Jo[:, :, n]]),
                        options.MyInf
                    )
            else:
                if np.isscalar(dyn.Jo[1]) or dyn.Jo[1].size == 1:
                    # 标量情况
                    cost_to_go[1] = dyn.Jo[1]
                else:
                    # 使用插值 ：eval(code_cost_to_go_interp{1})（代替动态函数）
                    # 构建插值参数
                    xsize = [len(current_grd['X'][i]) for i in range(1, len(grd.Nx) + 1)]
                    interp_dims = [i for i, size in enumerate(xsize) if size > 1]

                    if interp_dims:
                        # 多维插值
                        grid_coords = []
                        interp_coords = []
                        for i in reversed(interp_dims): 
                            grid_coords.append(previous_grd['X'][i+1])
                            if hasattr(options, 'CalcLine') and options.CalcLine:
                                interp_coords.append(inp.X[i+1])
                            else:
                                interp_coords.append(out.X[i+1])

                        cost_to_go[1] = dpm_interpn(*grid_coords, dyn.Jo[1], *interp_coords)
                    else:
                        cost_to_go[1] = dyn.Jo[1]

            # 总代价 = 弧代价 + 代价函数
            Jt = J + cost_to_go[1]

            # 处理最小化/最大化
            if hasattr(options, 'Minimize') and options.Minimize:
                Jt[Jt > options.MyInf] = options.MyInf
                if hasattr(options, 'CalcLine') and options.CalcLine:
                    Jt[Jt < grd.Xn[1]['lo'][n-1]] = options.MyInf
            else:
                Jt[Jt < options.MyInf] = options.MyInf
                if hasattr(options, 'CalcLine') and options.CalcLine:
                    Jt[Jt > grd.Xn[1]['hi'][n-1]] = options.MyInf

        except Exception as e:
            # 错误处理
            if 'out' in locals():
                if np.any(np.isnan(out.I.flatten())):
                    raise ValueError("DPM:Internal - Make sure the model does not output NaN in the variable I")
                if np.any(np.isnan(out.C[1].flatten())):
                    raise ValueError("DPM:Internal - Make sure the model does not output NaN in the variable C")
                if np.any(np.isnan(out.X[1].flatten())):
                    raise ValueError("DPM:Internal - Make sure the model does not output NaN in the variable X")

            raise ValueError(f"{str(e)} Error in dpm_backward at n={n}")

        # 重塑为优化所需的形状
        if len(grd.Nu) > 1:
            Jt = Jt.reshape([*x_sze_current.astype(int), np.prod(u_sze_current.astype(int))])

        # 最小化代价函数
        if hasattr(options, 'Minimize') and options.Minimize:
            Q = np.min(Jt, axis=len(x_sze_current))
            ui = np.argmin(Jt, axis=len(x_sze_current))
        else:
            Q = np.max(Jt, axis=len(x_sze_current))
            ui = np.argmax(Jt, axis=len(x_sze_current))

        # 处理水平集方法的不可行状态
        if hasattr(options, 'UseLevelSet') and options.UseLevelSet:
            # 处理不可行状态
            Q_inf = J + cost_to_go[1]
            Q_inf = Q_inf.reshape([*x_sze_current.astype(int), np.prod(u_sze_current.astype(int))])

            # 生成网格索引：eval(code_x_grd)（代替动态函数）
            # [x1 x2 ...] = ndgrid((1:x_sze(1))', (1:x_sze(2))', ...)
            if len(grd.Nx) > 1:
                # 生成索引网格
                x_ranges = []
                for i in range(len(x_sze_current)):
                    x_ranges.append(np.arange(x_sze_current[i]))
                x_grids = np.meshgrid(*x_ranges, indexing='ij')
            else:
                x_grids = [np.arange(x_sze_current[0])]

            # 根据维度处理
            # 使用np.ravel_multi_index替代MATLAB的sub2ind
            Q_inf_shape = [*x_sze_current.astype(int), np.prod(u_sze_current.astype(int))]

            if len(grd.Nx) == 1:
                # Qinf = Q_inf(sub2ind([x_sze prod(u_sze)],x1,ub));
                indices = np.ravel_multi_index((x_grids[0], ub), Q_inf_shape)
                Qinf = Q_inf.flat[indices]
            elif len(grd.Nx) == 2:
                # Qinf = Q_inf(sub2ind([x_sze prod(u_sze)],x1,x2,ub));
                indices = np.ravel_multi_index((x_grids[0], x_grids[1], ub), Q_inf_shape)
                Qinf = Q_inf.flat[indices]
            elif len(grd.Nx) == 3:
                #  Qinf = Q_inf(sub2ind([x_sze prod(u_sze)],x1,x2,x3,ub));
                indices = np.ravel_multi_index((x_grids[0], x_grids[1], x_grids[2], ub), Q_inf_shape)
                Qinf = Q_inf.flat[indices]
            elif len(grd.Nx) == 4:
                # Qinf = Q_inf(sub2ind([x_sze prod(u_sze)],x1,x2,x3,x4,ub));
                indices = np.ravel_multi_index((x_grids[0], x_grids[1], x_grids[2], x_grids[3], ub), Q_inf_shape)
                Qinf = Q_inf.flat[indices]
            else:
                raise NotImplementedError(f"不支持 {len(grd.Nx)} 维状态空间")

            # 更新不可行状态
            infeasible_mask = np.min(Vt, axis=len(x_sze_current)) > 0
            Q[infeasible_mask] = Qinf[infeasible_mask]
            ui[infeasible_mask] = ub[infeasible_mask]

        # 处理信号存储
        if (not (hasattr(options, 'CalcLine') and options.CalcLine) and
            hasattr(options, 'Signals') and options.Signals):
            for i in range(len(options.Signals)):
                signal_name = options.Signals[i]
                try:
                    # eval(['dyn.' options.Signals{i} '{n} = signals.' options.Signals{i} '(sub2ind([x_sze u_sze],' dpm_code('(1:x_sze(#))''',1:length(x_sze)) ',ui));']);

                    # 生成状态索引范围：dpm_code('(1:x_sze(#))''',1:length(x_sze)
                    state_indices = []
                    for j in range(len(x_sze_current)):
                        state_indices.append(np.arange(x_sze_current[j]))

                    # 生成完整的网格索引
                    if len(state_indices) > 1:
                        state_grids = np.meshgrid(*state_indices, indexing='ij')
                        # 添加输入索引
                        all_indices = state_grids + [ui]
                    else:
                        # 一维状态情况
                        all_indices = [state_indices[0], ui]

                    # 计算线性索引：sub2ind([x_sze u_sze], ...)
                    # [x_sze u_sze]，不是[x_sze prod(u_sze)]
                    full_shape = list(x_sze_current.astype(int)) + list(u_sze_current.astype(int))
                    linear_indices = np.ravel_multi_index(tuple(all_indices), full_shape)

                    # 从信号中提取值并存储
                    if hasattr(signals, signal_name):
                        signal_data = getattr(signals, signal_name)
                        if not hasattr(dyn, signal_name):
                            setattr(dyn, signal_name, {})
                        getattr(dyn, signal_name)[n] = signal_data.flat[linear_indices]

                except Exception:
                    warnings.warn("DPM:Backward - options.signals element is not found in model output.")

        # 检查是否所有解都不可行
        if np.sum(Q.flatten() == options.MyInf) == Q.size:
            if hasattr(options, 'UseLine') and options.UseLine:
                # 如果使用边界线方法，允许所有点不可行，只要最终有点在边界线内
                boundary_check_passed = False

                # 检查边界线代价是否都是无穷大
                if (dyn.B['hi'].Jo[0, 0, n-1] == options.MyInf and
                    dyn.B['lo'].Jo[0, 0, n-1] == options.MyInf):
                    boundary_check_passed = True
                else:
                    # 边界检查
                    # 检查是否有输出状态在边界线内
                    if 1 in out.X:
                        out_x1 = out.X[1]
                        lo_bound = dyn.B['lo'].Xo[n] 
                        hi_bound = dyn.B['hi'].Xo[n]

                        # 找到在边界内的输出状态
                        in_boundary_mask = (out_x1 > lo_bound) & (out_x1 < hi_bound)

                        if np.sum(in_boundary_mask) > 0:
                            # 检查对应的输入状态是否也在边界内
                            if 1 in inp.X:
                                inp_x1_subset = inp.X[1][in_boundary_mask]
                                lo_bound_current = dyn.B['lo'].Xo[n-1]  # 当前时刻边界
                                hi_bound_current = dyn.B['hi'].Xo[n-1]

                                inp_in_boundary = ((inp_x1_subset > lo_bound_current) &
                                                 (inp_x1_subset < hi_bound_current))

                                if np.sum(inp_in_boundary) > 0:
                                    boundary_check_passed = True

                if not boundary_check_passed:
                    # 调试模式
                    if hasattr(options, 'DebugMode') and options.DebugMode:
                        print("DPM:Model function error")
                        print(f"\t Model: {model}")
                        print(f"\t Time step: {n}")
                        n = n + 1
                        continue

                    warnings.warn("DPM:Backward - No feasible solution Q(i,j,..) = Inf for all i,j,...")
                    break
            else:
                # 非边界线方法处理
                if (not (hasattr(options, 'CalcLine') and options.CalcLine) and
                    hasattr(options, 'DebugMode') and options.DebugMode):
                    # 调试模式
                    print("DPM:Model function error")
                    print(f"\t Model: {model}")
                    print(f"\t Time step: {n}")
                    n = n + 1
                    continue

                warnings.warn("DPM:Backward - No feasible solution Q(i,j,..) = Inf for all i,j,...")
                break

        # 更新最优代价 dyn.Jo
        if hasattr(options, 'UseLine') and options.UseLine:
            # 边界线方法的特殊处理
            if len(grd.Nx) == 1:
                # 一维情况
                below = current_grd['X'][1] < dyn.B['lo'].Xo[n-1]
                above = current_grd['X'][1] > dyn.B['hi'].Xo[n-1]
                inside = ((current_grd['X'][1] >= dyn.B['lo'].Xo[n-1]) &
                         (current_grd['X'][1] <= dyn.B['hi'].Xo[n-1]))
            else:
                # 多维情况
                below = current_grd['X'][1] < np.min(dyn.B['lo'].Xo[:, :, n-1])
                above = current_grd['X'][1] > np.max(dyn.B['hi'].Xo[:, :, n-1])
                inside = ((current_grd['X'][1] >= np.min(dyn.B['lo'].Xo[:, :, n-1])) &
                         (current_grd['X'][1] <= np.max(dyn.B['hi'].Xo[:, :, n-1])))

            # 初始化为NaN
            dyn.Jo[1] = np.full_like(Q, np.nan)

            # 设置内部区域的值
            # eval(['dyn.Jo{1}(:' repmat(',:',1,length(grd.Nx)-1) ')     = Q;']);
            if len(grd.Nx) == 1:
                dyn.Jo[1][:] = Q
            else:
                # 多维情况：设置所有维度
                dyn.Jo[1][:] = Q

            # 单状态特殊情况：边界间插值
            if (len(grd.Nx) == 1 and
                dyn.B['lo'].Jo[n-1] < options.MyInf and
                dyn.B['hi'].Jo[n-1] < options.MyInf and
                np.sum(Q.flatten() == options.MyInf) == Q.size and
                np.sum(inside) > 0):

                # 在边界间进行插值
                boundary_mask = inside
                if np.sum(boundary_mask) > 0:
                    x_boundary = current_grd['X'][1][boundary_mask]
                    x_bounds = np.array([dyn.B['lo'].Xo[n-1], dyn.B['hi'].Xo[n-1]])
                    y_bounds = np.array([dyn.B['lo'].Jo[n-1], dyn.B['hi'].Jo[n-1]])

                    interpolated_values = dpm_interpn(x_bounds, y_bounds, x_boundary)
                    dyn.Jo[1][boundary_mask] = interpolated_values

            # 设置边界外区域为无穷大
            if len(grd.Nx) == 1:
                dyn.Jo[1][above] = options.MyInf
                dyn.Jo[1][below] = options.MyInf
            else:
                # 多维情况：在第一个维度上应用掩码
                dyn.Jo[1][above, :] = options.MyInf
                dyn.Jo[1][below, :] = options.MyInf
        else:
            # 非边界线方法
            dyn.Jo[1] = Q

        # 索引转换
        # code_ind2str = '[uo1 uo2 ...] = ind2sub(u_sze,ui);'
        if len(grd.Nu) > 1:
            # 多输入情况：将线性索引转换为多维下标
            uo_indices = np.unravel_index(ui.flatten(), u_sze_current.astype(int))
            uo = {}
            for i in range(len(grd.Nu)):
                uo[i+1] = uo_indices[i].reshape(ui.shape)
        else:
            # 单输入情况
            uo = {1: ui}

        # 处理多个代价函数
        for i in range(2, len(out.C) + 1):
            if i in out.C:
                # 初始化Xi
                Xi = {1: np.array([])}

                # 处理多维状态
                for j in range(2, len(grd.Nx) + 1):
                    if j in out.X:
                        Xi[j] = out.X[j].reshape([np.prod(x_sze_current.astype(int)),
                                                 np.prod(u_sze_current.astype(int))])

                # 计算索引（dpm_sub2ind）
                ind2 = np.ravel_multi_index(
                    (np.arange(np.prod(x_sze_current.astype(int))), ui.flatten()),
                    [np.prod(x_sze_current.astype(int)), np.prod(u_sze_current.astype(int))]
                )

                # 重塑代价数组
                Ci = out.C[i].reshape([np.prod(x_sze_current.astype(int)),
                                      np.prod(u_sze_current.astype(int))])

                if len(grd.Nx) > 1:
                    # 多维插值
                    grid_coords = []
                    query_coords = []
                    for j in range(2, len(grd.Nx) + 1):
                        if j in current_grd['X']:
                            grid_coords.append(current_grd['X'][j])
                        if j in out.X:
                            query_coords.append(out.X[j])

                    if grid_coords and query_coords and i in dyn.Jo:
                        cost_to_go_2 = dpm_interpn(*grid_coords, dyn.Jo[i], *query_coords)
                    else:
                        cost_to_go_2 = np.zeros_like(Ci)

                    # 复杂的代价计算
                    cost_to_go_reshaped = cost_to_go_2.flat[ind2].reshape(x_sze_current.astype(int))
                    out_I_reshaped = out.I.flat[ind2].reshape(x_sze_current.astype(int))
                    Ci_reshaped = Ci.flat[ind2].reshape(x_sze_current.astype(int))

                    feasible_mask = (cost_to_go_reshaped != options.MyInf) & (out_I_reshaped == 0)
                    infeasible_mask = (cost_to_go_reshaped == options.MyInf) | (out_I_reshaped != 0)

                    dyn.Jo[i] = (feasible_mask * (Ci_reshaped + cost_to_go_reshaped) +
                                infeasible_mask * options.MyInf)
                else:
                    # 一维情况
                    if i in dyn.Jo:
                        feasible_mask = (dyn.Jo[i] != options.MyInf) & (out.I.flat[ind2] == 0)
                        infeasible_mask = (dyn.Jo[i] == options.MyInf) | (out.I.flat[ind2] != 0)

                        dyn.Jo[i] = (feasible_mask * (Ci.flat[ind2] + dyn.Jo[i]) +
                                    infeasible_mask * options.MyInf)

                # 处理最小化/最大化边界
                if i in dyn.Jo:  # 检查键是否存在
                    if hasattr(options, 'Minimize') and options.Minimize:
                        dyn.Jo[i][dyn.Jo[i] > options.MyInf] = options.MyInf
                    else:
                        dyn.Jo[i][dyn.Jo[i] < options.MyInf] = options.MyInf

        # 存储最优输入
        if hasattr(options, 'UseLine') and options.UseLine:
            # 边界线方法的特殊处理
            for i in range(1, len(grd.Nu) + 1):
                if len(grd.Nx) > 1:
                    if i in uo:
                        #  dyn.Uo{i,n}(:,:,:) = current_grd.U{i}(uo1);
                        if len(grd.Nx) == 1:
                            dyn.Uo[i][n][:] = current_grd['U'][i][uo[i]]
                        elif len(grd.Nx) == 2:
                            dyn.Uo[i][n][:, :] = current_grd['U'][i][uo[i]]
                        elif len(grd.Nx) == 3:
                            dyn.Uo[i][n][:, :, :] = current_grd['U'][i][uo[i]]
                        else:
                            # 通用
                            dyn.Uo[i][n][...] = current_grd['U'][i][uo[i]]

                    # 下边界处理
                    # [ind col] = dpm_sub2indr([grd.Nx{1}(n) grd.Nx{2}(n)],ones(1,grd.Nx{2}(n)),dpm_findl(current_grd.X{1},dyn.B.lo.Xo(:,:,n)),2);
                    lo_boundary_values = dyn.B['lo'].Xo[:, :, n-1].flatten()
                    lo_indices = dpm_findl(current_grd['X'][1], lo_boundary_values)

                    ones_array = np.ones(grd.Nx[2][n-1], dtype=int)
                    ind_lo, col_lo = dpm_sub2indr([grd.Nx[1][n-1], grd.Nx[2][n-1]],
                                                  ones_array,
                                                  lo_indices, 2)

                    # 转换线性索引为多维下标
                    s1_lo, s2_lo = np.unravel_index(ind_lo-1, [grd.Nx[1][n-1], grd.Nx[2][n-1]])

                    # 设置下边界值
                    target_indices_lo = np.ravel_multi_index((s1_lo, s2_lo), dyn.Uo[i][n].shape)
                    source_indices_lo = np.ravel_multi_index((np.zeros_like(col_lo), col_lo-1, (n-1)*np.ones_like(col_lo)),
                                                            dyn.B['lo'].Uo[i].shape)
                    dyn.Uo[i][n].flat[target_indices_lo] = dyn.B['lo'].Uo[i].flat[source_indices_lo]

                    # 上边界处理
                    hi_boundary_values = dyn.B['hi'].Xo[:, :, n-1].flatten()
                    hi_indices = dpm_findu(current_grd['X'][1], hi_boundary_values)

                    max_indices = grd.Nx[1][n-1] * np.ones(grd.Nx[2][n-1], dtype=int)
                    ind_hi, col_hi = dpm_sub2indr([grd.Nx[1][n-1], grd.Nx[2][n-1]],
                                                  hi_indices,
                                                  max_indices, 2)

                    # 转换线性索引为多维下标
                    s1_hi, s2_hi = np.unravel_index(ind_hi-1, [grd.Nx[1][n-1], grd.Nx[2][n-1]])

                    # 设置上边界值
                    target_indices_hi = np.ravel_multi_index((s1_hi, s2_hi), dyn.Uo[i][n].shape)
                    source_indices_hi = np.ravel_multi_index((np.zeros_like(col_hi), col_hi-1, (n-1)*np.ones_like(col_hi)),
                                                            dyn.B['hi'].Uo[i].shape)
                    dyn.Uo[i][n].flat[target_indices_hi] = dyn.B['hi'].Uo[i].flat[source_indices_hi]

                else:
                    # 一维情况
                    if i in uo:
                        # 确保维度匹配
                        if dyn.Uo[i][n].ndim == 2 and dyn.Uo[i][n].shape[1] == 1:
                            dyn.Uo[i][n][:, 0] = current_grd['U'][i][uo[i]]
                        else:
                            dyn.Uo[i][n][:] = current_grd['U'][i][uo[i]]

                    # 边界间插值的特殊情况
                    if (dyn.B['lo'].Jo[n-1] < options.MyInf and
                        dyn.B['hi'].Jo[n-1] < options.MyInf and
                        np.sum(Q.flatten() == options.MyInf) == Q.size and
                        np.sum(inside) > 0):

                        # 在边界间进行插值
                        boundary_mask = inside
                        if np.sum(boundary_mask) > 0:
                            x_boundary = current_grd['X'][1][boundary_mask]
                            x_bounds = np.array([dyn.B['lo'].Xo[n-1], dyn.B['hi'].Xo[n-1]])
                            u_bounds = np.array([dyn.B['lo'].Uo[i][n-1], dyn.B['hi'].Uo[i][n-1]])

                            interpolated_u = dpm_interpn(x_bounds, u_bounds, x_boundary)
                            dyn.Uo[i][n][boundary_mask] = interpolated_u

                    # 设置边界外区域
                    # 检查边界数据的长度
                    if n-1 < len(dyn.B['lo'].Uo[i]) and n-1 < len(dyn.B['hi'].Uo[i]):
                        dyn.Uo[i][n][below] = dyn.B['lo'].Uo[i][n-1]
                        dyn.Uo[i][n][above] = dyn.B['hi'].Uo[i][n-1]
                    else:
                        # 如果索引超出范围，使用最后一个有效值
                        lo_val = dyn.B['lo'].Uo[i][-1] if len(dyn.B['lo'].Uo[i]) > 0 else 1
                        hi_val = dyn.B['hi'].Uo[i][-1] if len(dyn.B['hi'].Uo[i]) > 0 else 1
                        # 确保lo_val和hi_val是标量
                        if hasattr(lo_val, 'item') and lo_val.size == 1:
                            lo_val = lo_val.item()
                        elif hasattr(lo_val, '__len__') and len(lo_val) > 0:
                            lo_val = lo_val[0] if np.isscalar(lo_val[0]) else 1
                        elif not np.isscalar(lo_val):
                            lo_val = 1

                        if hasattr(hi_val, 'item') and hi_val.size == 1:
                            hi_val = hi_val.item()
                        elif hasattr(hi_val, '__len__') and len(hi_val) > 0:
                            hi_val = hi_val[0] if np.isscalar(hi_val[0]) else 1
                        elif not np.isscalar(hi_val):
                            hi_val = 1
                        if np.any(below):
                            dyn.Uo[i][n][below] = lo_val
                        if np.any(above):
                            dyn.Uo[i][n][above] = hi_val
        else:
            # 非边界线方法
            for i in range(1, len(grd.Nu) + 1):
                # eval(['dyn.Uo{i,n}(:' repmat(',:',1,length(grd.Nx)-1) ') = current_grd.U{i}(uo' num2str(i) ');']);
                if i in uo:
                    if len(grd.Nx) == 1:
                        # 处理一维情况的形状匹配
                        if dyn.Uo[i][n].ndim == 2 and dyn.Uo[i][n].shape[1] == 1:
                            dyn.Uo[i][n][:, 0] = current_grd['U'][i][uo[i]]
                        else:
                            dyn.Uo[i][n][:] = current_grd['U'][i][uo[i]]
                    elif len(grd.Nx) == 2:
                        dyn.Uo[i][n][:, :] = current_grd['U'][i][uo[i]]
                    elif len(grd.Nx) == 3:
                        dyn.Uo[i][n][:, :, :] = current_grd['U'][i][uo[i]]
                    else:
                        # 通用
                        dyn.Uo[i][n][...] = current_grd['U'][i][uo[i]]

        # 保存代价函数映射
        if hasattr(options, 'SaveMap') and options.SaveMap:
            for i in range(1, len(dyn.Jo) + 1):
                if i in dyn.Jo:
                    # eval(['V_map{i,n}(:' repmat(',:',1,length(grd.Nx)-1) ') = dyn.Jo{i};']);
                    if len(grd.Nx) == 1:
                        # 处理一维情况的形状匹配
                        if V_map[i][n].ndim == 2 and V_map[i][n].shape[1] == 1:
                            V_map[i][n][:, 0] = dyn.Jo[i]
                        else:
                            V_map[i][n][:] = dyn.Jo[i]
                    elif len(grd.Nx) == 2:
                        V_map[i][n][:, :] = dyn.Jo[i]
                    elif len(grd.Nx) == 3:
                        V_map[i][n][:, :, :] = dyn.Jo[i]
                    else:
                        # 通用方式
                        V_map[i][n][...] = dyn.Jo[i]

        # 更新进度条
        if hasattr(options, 'Waitbar') and options.Waitbar == 'on':
            progress = n / dis.N
            print(f"\rProgress: {progress:.1%}", end='', flush=True)

        # 更新详细进度
        if hasattr(options, 'Verbose') and options.Verbose == 'on':
            if (n - 1) % max(1, dis.N // 100) == 0 and round(100 * n / dis.N) < 100:
                # fprintf('%s%2d %%',ones(1,4)*8,round(100*n/dis.N))
                print(f"\b\b\b\b{round(100 * n / dis.N):2d} %", end='', flush=True)
    # 结束时间循环

    # 清除或保存代价函数映射
    if not (hasattr(options, 'SaveMap') and options.SaveMap):
        dyn.Jo = {}
    else:
        dyn.Jo = V_map

    # 关闭进度条
    if hasattr(options, 'Waitbar') and options.Waitbar == 'on':
        print("\rProgress: 100.0%")

    # 完成消息
    if hasattr(options, 'Verbose') and options.Verbose == 'on':
        print("\b\b\b\b\b Done!")

    return dyn


def dpm_boundary_line_lower(model: Union[str, Callable], par: Any, grd: Grid,
                           dis: Problem, options: Options):
    """
    计算下边界线

    参数:
        model: 模型函数
        par: 参数结构
        grd: 网格结构
        dis: 问题参数
        options: 选项结构

    返回:
        LineLower: 下边界线结果：
            - Xo: 边界状态轨迹
            - Uo: 边界输入轨迹
            - Jo: 边界代价轨迹
    """

    optb = copy.deepcopy(options)
    parb = copy.deepcopy(par) if par is not None else {}
    grdb = copy.deepcopy(grd)

    # 保存真实的网格边界
    if not hasattr(grdb, 'Xn_true'):
        grdb.Xn_true = {}
    grdb.Xn_true[1] = {'lo': grd.Xn[1]['lo'].copy(), 'hi': grd.Xn[1]['hi'].copy()}

    # 设置修改后的网格：将状态网格设置为初始状态的单点
    grdb.Nx[1] = np.ones(dis.N + 1, dtype=int)  # 每个时间步只有一个网格点
    grdb.Xn[1]['lo'] = np.array([grd.X0[1]] * (dis.N + 1))  # 下边界设为初始状态
    grdb.Xn[1]['hi'] = np.array([grd.X0[1]] * (dis.N + 1))  # 上边界设为初始状态

    # 修改选项
    optb.UseLine = False  # 不使用边界线方法（避免递归）
    optb.SaveMap = True   # 保存代价映射
    optb.CalcLine = True  # 标记为计算边界线

    # 设置参数
    if not isinstance(parb, dict):
        parb = {}
    parb['model'] = model
    parb['options'] = {'Iter': getattr(options, 'Iter', 10),
                       'Tol': getattr(options, 'Tol', 1e-8)}

    # 设置最终代价
    if hasattr(options, 'gN') and options.gN:
        warnings.warn("options.gN can only be used w/ 1 state boundary")
        optb.gN = {1: grd.XN[1]['lo'], 2: dpm_interpn(grd.X[1], options.gN[1], grdb.X[1])}
    else:
        optb.gN = {1: grd.XN[1]['lo'], 2: np.zeros_like(grd.XN[1]['lo'])}

    # 设置其他选项
    optb.MyInf = options.MyInf
    optb.Minimize = True  # 强制最小化（PEL:2012 - 这曾经是 options.Minimize）
    optb.Warnings = 'off'

    # 调用动态规划求解器（只进行后向仿真）
    dynb = dpm_backward(dpm_model_inv, parb, grdb, dis, optb)

    # 将单元数组转换为向量
    if hasattr(dynb, 'Jo') and isinstance(dynb.Jo, dict):
        vsize = (len(dynb.Jo), dis.N + 1)
        V_new = {}

        for j in range(1, vsize[0] + 1):
            if j in dynb.Jo:
                # 获取第一个时间步的长度来确定状态维度
                first_step_length = len(dynb.Jo[j][1]) if 1 in dynb.Jo[j] and hasattr(dynb.Jo[j][1], '__len__') else 1
                V_new[j] = np.full((1, first_step_length, vsize[1]), np.nan)  # 1 x length x N 数组
                for i in range(1, vsize[1] + 1):
                    if i in dynb.Jo[j]:
                        if np.isscalar(dynb.Jo[j][i]):
                            V_new[j][0, 0, i-1] = dynb.Jo[j][i]
                        else:
                            V_new[j][0, :, i-1] = np.array(dynb.Jo[j][i]).flatten()

        # 处理输入映射
        if hasattr(dynb, 'Uo') and isinstance(dynb.Uo, dict):
            usize = (len(dynb.Uo), dis.N)
            O_new = {}

            for j in range(1, usize[0] + 1):
                if j in dynb.Uo:
                    # 获取第一个时间步的长度来确定输入维度
                    first_step_length = len(dynb.Uo[j][1]) if 1 in dynb.Uo[j] and hasattr(dynb.Uo[j][1], '__len__') else 1
                    O_new[j] = np.full((1, first_step_length, usize[1]), np.nan)  # 1 x length x N 数组
                    for i in range(1, usize[1] + 1):
                        if i in dynb.Uo[j]:
                            if np.isscalar(dynb.Uo[j][i]):
                                O_new[j][0, 0, i-1] = dynb.Uo[j][i]
                            else:
                                O_new[j][0, :, i-1] = np.array(dynb.Uo[j][i]).flatten()

        # 更新结果
        dynb.Jo = V_new
        dynb.Uo = O_new

    # 将不可行点转换为最小状态边界
    for i in range(1, dis.N + 1):
        if 1 in dynb.Jo:
            # 处理超出上边界的点
            mask_hi = dynb.Jo[1][0, :, i-1] > grd.Xn[1]['hi'][i-1]
            dynb.Jo[1][0, mask_hi, i-1] = grd.Xn[1]['lo'][i-1]

            # 处理NaN值
            mask_nan = np.isnan(dynb.Jo[1][0, :, i-1])
            dynb.Jo[1][0, mask_nan, i-1] = grd.Xn[1]['lo'][i-1]

    # 处理第二个代价函数
    if 2 in dynb.Jo:
        mask_invalid = np.isnan(dynb.Jo[2]) | (dynb.Jo[2] >= optb.MyInf)
        dynb.Jo[2][mask_invalid] = options.MyInf

    # 处理输入映射中的NaN值
    for i in range(1, len(dynb.Uo) + 1):
        if i in dynb.Uo:
            mask_nan = np.isnan(dynb.Uo[i])
            dynb.Uo[i][mask_nan] = 1

    # 创建下边界线结果
    class LineLower:
        def __init__(self):
            # 在MATLAB中，Xo是一个可以用Xo(n)索引的数组
            # 这里创建一个包装器来模拟MATLAB的索引行为
            if 1 in dynb.Jo and dynb.Jo[1] is not None:
                xo_data = dynb.Jo[1]
                if xo_data.ndim == 3:  # 形状为 (1, 1, N+1)
                    self.Xo = xo_data[0, 0, :]  # 提取为1D数组
                else:
                    self.Xo = xo_data
            else:
                self.Xo = None
            # 处理Uo - 需要将3D数组转换为MATLAB风格的1D索引
            self.Uo = {}
            if dynb.Uo:
                for i in dynb.Uo:
                    if isinstance(dynb.Uo[i], np.ndarray) and dynb.Uo[i].ndim == 3:
                        # 边界线计算返回的是3D数组 (1, first_step_length, N)
                        # 提取为1D数组，每个时间步取第一个元素
                        uo_array = dynb.Uo[i][0, 0, :]  # 提取 (N,) 形状的数组
                        self.Uo[i] = uo_array
                    elif isinstance(dynb.Uo[i], dict):
                        # 如果是字典结构，转换为数组
                        max_time = max(dynb.Uo[i].keys()) if dynb.Uo[i] else 0
                        uo_array = np.full(max_time + 1, np.nan)
                        for t in dynb.Uo[i]:
                            if isinstance(dynb.Uo[i][t], np.ndarray) and dynb.Uo[i][t].size == 1:
                                uo_array[t] = dynb.Uo[i][t].item()
                            elif np.isscalar(dynb.Uo[i][t]):
                                uo_array[t] = dynb.Uo[i][t]
                        self.Uo[i] = uo_array
                    else:
                        self.Uo[i] = dynb.Uo[i]
            # 同样处理Jo
            if 2 in dynb.Jo and dynb.Jo[2] is not None:
                jo_data = dynb.Jo[2]
                if jo_data.ndim == 3:  # 形状为 (1, 1, N+1)
                    self.Jo = jo_data[0, 0, :]  # 提取为1D数组
                else:
                    self.Jo = jo_data
            else:
                self.Jo = None

    return LineLower()


def dpm_boundary_line_upper(model: Union[str, Callable], par: Any, grd: Grid,
                           dis: Problem, options: Options):
    """
    计算上边界线

    参数:
        model: 模型函数
        par: 参数结构
        grd: 网格结构
        dis: 问题参数
        options: 选项结构

    返回:
        LineUpper: 上边界线结果，包含：
            - Xo: 边界状态轨迹
            - Uo: 边界输入轨迹
            - Jo: 边界代价轨迹
    """

    optb = copy.deepcopy(options)
    parb = copy.deepcopy(par) if par is not None else {}
    grdb = copy.deepcopy(grd)

    # 保存真实的网格边界
    if not hasattr(grdb, 'Xn_true'):
        grdb.Xn_true = {}
    grdb.Xn_true[1] = {'lo': grd.Xn[1]['lo'].copy(), 'hi': grd.Xn[1]['hi'].copy()}

    # 设置修改后的网格：将状态网格设置为初始状态的单点
    grdb.Nx[1] = np.ones(dis.N + 1, dtype=int)  # 每个时间步只有一个网格点
    grdb.Xn[1]['lo'] = np.array([grd.X0[1]] * (dis.N + 1))  # 下边界设为初始状态
    grdb.Xn[1]['hi'] = np.array([grd.X0[1]] * (dis.N + 1))  # 上边界设为初始状态

    # 修改选项
    optb.UseLine = False  # 不使用边界线方法
    optb.SaveMap = True   # 保存代价映射
    optb.CalcLine = True  # 标记为计算边界线

    # 设置参数
    if not isinstance(parb, dict):
        parb = {}
    parb['model'] = model
    parb['options'] = {'Iter': getattr(options, 'Iter', 10),
                       'Tol': getattr(options, 'Tol', 1e-8)}

    # 设置最终代价
    if hasattr(options, 'gN') and options.gN:
        warnings.warn("options.gN can only be used w/ 1 state boundary")
        optb.gN = {1: grd.XN[1]['hi'], 2: dpm_interpn(grd.X[1], options.gN[1], grdb.X[1])}
    else:
        optb.gN = {1: grd.XN[1]['hi'], 2: np.zeros_like(grd.XN[1]['hi'])}

    # 设置其他选项
    optb.MyInf = -options.MyInf  # 
    optb.Minimize = False  # 强制最大化（PEL:2012 - 这曾经是 ~options.Minimize）
    optb.Warnings = 'off'

    # 调用动态规划求解器（只进行后向仿真）
    dynb = dpm_backward(dpm_model_inv, parb, grdb, dis, optb)

    # 将单元数组转换为向量
    if hasattr(dynb, 'Jo') and isinstance(dynb.Jo, dict):
        vsize = (len(dynb.Jo), dis.N + 1)
        V_new = {}

        for j in range(1, vsize[0] + 1):
            if j in dynb.Jo:
                # 获取第一个时间步的长度来确定状态维度
                first_step_length = len(dynb.Jo[j][1]) if 1 in dynb.Jo[j] and hasattr(dynb.Jo[j][1], '__len__') else 1
                V_new[j] = np.full((1, first_step_length, vsize[1]), np.nan)  # 1 x length x N 数组
                for i in range(1, vsize[1] + 1):
                    if i in dynb.Jo[j]:
                        if np.isscalar(dynb.Jo[j][i]):
                            V_new[j][0, 0, i-1] = dynb.Jo[j][i]
                        else:
                            V_new[j][0, :, i-1] = np.array(dynb.Jo[j][i]).flatten()

        # 处理输入映射
        if hasattr(dynb, 'Uo') and isinstance(dynb.Uo, dict):
            usize = (len(dynb.Uo), dis.N)
            O_new = {}

            for j in range(1, usize[0] + 1):
                if j in dynb.Uo:
                    # 获取第一个时间步的长度来确定输入维度
                    first_step_length = len(dynb.Uo[j][1]) if 1 in dynb.Uo[j] and hasattr(dynb.Uo[j][1], '__len__') else 1
                    O_new[j] = np.full((1, first_step_length, usize[1]), np.nan)  # 1 x length x N 数组
                    for i in range(1, usize[1] + 1):
                        if i in dynb.Uo[j]:
                            if np.isscalar(dynb.Uo[j][i]):
                                O_new[j][0, 0, i-1] = dynb.Uo[j][i]
                            else:
                                O_new[j][0, :, i-1] = np.array(dynb.Uo[j][i]).flatten()

        # 更新结果
        dynb.Jo = V_new
        dynb.Uo = O_new

    # 将不可行点转换为最大状态边界
    for i in range(1, dis.N + 1):
        if 1 in dynb.Jo:
            # 处理低于下边界的点
            mask_lo = dynb.Jo[1][0, :, i-1] < grd.Xn[1]['lo'][i-1]
            dynb.Jo[1][0, mask_lo, i-1] = grd.Xn[1]['hi'][i-1]

            # 处理NaN值
            mask_nan = np.isnan(dynb.Jo[1][0, :, i-1])
            dynb.Jo[1][0, mask_nan, i-1] = grd.Xn[1]['hi'][i-1]

    # 处理第二个代价函数
    if 2 in dynb.Jo:
        mask_invalid = np.isnan(dynb.Jo[2]) | (dynb.Jo[2] <= optb.MyInf)
        dynb.Jo[2][mask_invalid] = options.MyInf

    # 处理输入映射中的NaN值
    for i in range(1, len(dynb.Uo) + 1):
        if i in dynb.Uo:
            mask_nan = np.isnan(dynb.Uo[i])
            dynb.Uo[i][mask_nan] = 1

    # 创建上边界线结果
    class LineUpper:
        def __init__(self):
            # 在MATLAB中，Xo是一个可以用Xo(n)索引的数组
            # 这里创建一个包装器来模拟MATLAB的索引行为
            if 1 in dynb.Jo and dynb.Jo[1] is not None:
                xo_data = dynb.Jo[1]
                if xo_data.ndim == 3:  # 形状为 (1, 1, N+1)
                    self.Xo = xo_data[0, 0, :]  # 提取为1D数组
                else:
                    self.Xo = xo_data
            else:
                self.Xo = None
            # 处理Uo - 需要将3D数组转换为MATLAB风格的1D索引
            self.Uo = {}
            if dynb.Uo:
                for i in dynb.Uo:
                    if isinstance(dynb.Uo[i], np.ndarray) and dynb.Uo[i].ndim == 3:
                        # 边界线计算返回的是3D数组 (1, first_step_length, N)
                        # 提取为1D数组，每个时间步取第一个元素
                        uo_array = dynb.Uo[i][0, 0, :]  # 提取 (N,) 形状的数组
                        self.Uo[i] = uo_array
                    elif isinstance(dynb.Uo[i], dict):
                        # 如果是字典结构，转换为数组
                        max_time = max(dynb.Uo[i].keys()) if dynb.Uo[i] else 0
                        uo_array = np.full(max_time + 1, np.nan)
                        for t in dynb.Uo[i]:
                            if isinstance(dynb.Uo[i][t], np.ndarray) and dynb.Uo[i][t].size == 1:
                                uo_array[t] = dynb.Uo[i][t].item()
                            elif np.isscalar(dynb.Uo[i][t]):
                                uo_array[t] = dynb.Uo[i][t]
                        self.Uo[i] = uo_array
                    else:
                        self.Uo[i] = dynb.Uo[i]
            # 同样处理Jo
            if 2 in dynb.Jo and dynb.Jo[2] is not None:
                jo_data = dynb.Jo[2]
                if jo_data.ndim == 3:  # 形状为 (1, 1, N+1)
                    self.Jo = jo_data[0, 0, :]  # 提取为1D数组
                else:
                    self.Jo = jo_data
            else:
                self.Jo = None

    return LineUpper()


def dpm_forward(dyn: DynamicResult, model: Union[str, Callable], par: Any,
                grd: Grid, dis: Problem, options: Options) -> Dict:
    """
    前向仿真

    参数:
        dyn: 动态规划结果
        model: 模型函数
        par: 参数
        grd: 网格结构
        dis: 问题参数
        options: 选项

    返回:
        out: 前向仿真结果
    """

    # 检查输入类型选项
    if not hasattr(options, 'InputType') or len(options.InputType) != len(grd.Nu):
        options.InputType = 'c' * len(grd.Nu)  # 默认为连续输入

    # 获取空的输入输出结构
    inp = dpm_get_empty_inp(grd, dis, 'zero')
    outn = dpm_get_empty_out(model, inp, par, grd, 'zero')

    # 初始化状态
    for i in range(1, len(grd.Nx) + 1):
        inp.X[i] = grd.X0[i]
        outn.X[i] = grd.X0[i]

    # 动态规划前向迭代的进度
    if hasattr(options, 'Waitbar') and options.Waitbar == 'on':
        print("DP 前向运行中。请等待...")

    if hasattr(options, 'Verbose') and options.Verbose == 'on':
        print("DP 前向运行中:     0 %", end='', flush=True)

    # 向后兼容性检查 dis.N0
    if not hasattr(dis, 'N0'):
        dis.N0 = 1

    # 检查是否使用输入映射
    use_umap = getattr(options, 'UseUmap', True)

    # 警告标志（仅警告一次）
    iswarned = False

    # 前向仿真循环
    for n in range(dis.N0, dis.N + 1):
        # 生成当前时刻的状态网格
        current_grd = {'X': {}, 'U': {}}

        for i in range(1, len(grd.Nx) + 1):
            if (hasattr(options, 'UseLine') and options.UseLine and
                len(grd.Nx) == 1 and i == 1 and
                hasattr(options, 'FixedGrid') and not options.FixedGrid):
                current_grd['X'][i] = np.linspace(dyn.B['lo'].Xo[n-1],
                                                 dyn.B['hi'].Xo[n-1],
                                                 grd.Nx[i][n-1])
            elif (hasattr(options, 'UseLine') and options.UseLine and
                  hasattr(options, 'FixedGrid') and not options.FixedGrid):
                lo_min = np.min(dyn.B['lo'].Xo[:, n-1])
                hi_max = np.max(dyn.B['hi'].Xo[:, n-1])
                current_grd['X'][i] = np.linspace(lo_min, hi_max, grd.Nx[i][n-1])
            else:
                current_grd['X'][i] = np.linspace(grd.Xn[i]['lo'][n-1],
                                                 grd.Xn[i]['hi'][n-1],
                                                 grd.Nx[i][n-1])

        for i in range(1, len(grd.Nu) + 1):
            current_grd['U'][i] = np.linspace(grd.Un[i]['lo'][n-1],
                                             grd.Un[i]['hi'][n-1],
                                             grd.Nu[i][n-1])

        # 构建输入结构
        for w in range(1, len(dis.W) + 1):
            if w in dis.W:
                inp.W[w] = dis.W[w][n-1] if hasattr(dis.W[w], '__len__') else dis.W[w]
        inp.Ts = dis.Ts

        # 如果不使用输入映射，尝试所有可能的输入候选
        if not use_umap:
            # 生成下一时刻的状态网格
            next_grd = {'X': {}}
            for i in range(1, len(grd.Nx) + 1):
                next_grd['X'][i] = np.linspace(grd.Xn[i]['lo'][n],
                                              grd.Xn[i]['hi'][n],
                                              grd.Nx[i][n])

            # 设置所有可能输入候选的网格
            inpt = Input()
            for w in range(1, len(dis.W) + 1):
                if w in dis.W:
                    inpt.W[w] = dis.W[w][n-1] if hasattr(dis.W[w], '__len__') else dis.W[w]
            inpt.Ts = dis.Ts

            # 生成输入网格
            if len(grd.Nu) > 1:
                U_grids = [current_grd['U'][i] for i in range(1, len(grd.Nu) + 1)]
                U_meshes = np.meshgrid(*U_grids, indexing='ij')
                for i in range(len(grd.Nu)):
                    inpt.U[i+1] = U_meshes[i]
            else:
                inpt.U[1] = current_grd['U'][1]

            # 设置状态（所有输入候选使用相同的当前状态）
            for i in range(1, len(grd.Nx) + 1):
                if len(grd.Nu) > 1:
                    inpt.X[i] = inp.X[i] * np.ones_like(inpt.U[1])
                else:
                    inpt.X[i] = inp.X[i] * np.ones_like(inpt.U[1])

            # 调用模型函数
            try:
                if callable(model):
                    X, C, I = model(inpt, par)
                else:
                    import importlib
                    module = importlib.import_module(model)
                    model_func = getattr(module, model)
                    X, C, I = model_func(inpt, par)

                # 处理边界约束
                for i in range(1, len(grd.Nx) + 1):
                    if i in X:
                        I = np.logical_or(I, X[i] > grd.Xn[i]['hi'][n])
                        I = np.logical_or(I, X[i] < grd.Xn[i]['lo'][n])

                # 如果计算了边界线，再次处理边界
                if hasattr(options, 'UseLine') and options.UseLine:
                    I = np.logical_or(I, X[i] > dyn.B['hi'].Xo[n])
                    I = np.logical_or(I, X[i] < dyn.B['lo'].Xo[n])

                # 弧代价
                J = np.where(I == 0, C[1], options.MyInf)

                # 水平集方法
                if hasattr(options, 'UseLevelSet') and options.UseLevelSet:
                    # 获取可行性信息
                    cost_to_go_levelset = dpm_interpn(
                        next_grd['X'][1], dyn.Jo[len(dyn.Jo)][n], X[1])
                    J[cost_to_go_levelset > 0] = options.MyInf

                # 计算整个网格的代价
                if hasattr(dyn.Jo[1], '__len__') and len(dyn.Jo[1]) > 1:
                    # 从代价函数映射插值
                    cost_to_go_1 = dpm_interpn(next_grd['X'][1], dyn.Jo[1][n], X[1])
                else:
                    cost_to_go_1 = dyn.Jo[1] if hasattr(dyn, 'Jo') and 1 in dyn.Jo else 0

                Jt = J + cost_to_go_1

                # 如果没有找到有效的输入信号（水平集方法）
                if (hasattr(options, 'UseLevelSet') and options.UseLevelSet and
                    not np.any((cost_to_go_levelset <= 0) & (I == 0))):
                    # 获取第二个代价函数
                    if len(dyn.Jo) > 1 and 2 in dyn.Jo:
                        if hasattr(dyn.Jo[2], '__len__') and len(dyn.Jo[2]) > 1:
                            cost_to_go_2 = dpm_interpn(next_grd['X'][1], dyn.Jo[2][n], X[1])
                        else:
                            cost_to_go_2 = dyn.Jo[2]
                        Jt = cost_to_go_2.copy()
                    else:
                        Jt = cost_to_go_1.copy()
                    Jt[I != 0] = options.MyInf

                # 处理最小化/最大化
                if options.Minimize:
                    Jt[Jt > options.MyInf] = options.MyInf
                else:
                    Jt[Jt < options.MyInf] = options.MyInf

                if len(grd.Nu) > 1:
                    Jt = Jt.reshape((1, Jt.size))

                # 最小化代价函数
                if options.Minimize:
                    Q = np.min(Jt)
                    ui = np.argmin(Jt)
                else:
                    Q = np.max(Jt)
                    ui = np.argmax(Jt)

                # 使用最小化总代价的输入
                for i in range(1, len(grd.Nu) + 1):
                    if len(grd.Nu) > 1:
                        inpt_U_flat = inpt.U[i].reshape((1, inpt.U[i].size))
                        inp.U[i] = inpt_U_flat[0, ui]
                    else:
                        inp.U[i] = inpt.U[i].flat[ui]

            except Exception as e:
                warnings.warn(f"前向仿真模型调用错误 at n={n}: {str(e)}")
                # 设置默认输入
                for i in range(1, len(grd.Nu) + 1):
                    inp.U[i] = 0.0

        else:
            # 使用输入映射 (UseUmap = True)
            if hasattr(options, 'UseLine') and options.UseLine:
                # 边界线方法的输入插值
                for i in range(1, len(grd.Nu) + 1):
                    # xi = cell(1,length(grd.Nx))
                    xi = {}
                    Xin = {}
                    # for j=length(grd.Nx):-1:1 - 从最后一维倒序到第一维
                    for j in range(len(grd.Nx), 0, -1):
                        if options.InputType[i-1] == 'd':  # 离散输入
                            if j == 1:
                                # 构建多维边界访问（暂代）
                                # eval(['x1vec = [dyn.B.lo.Xo(1,' xistr 'n); current_grd.X{j}(...); dyn.B.hi.Xo(1,' xistr 'n)];'])
                                if len(grd.Nx) == 1:
                                    # 一维情况：dyn.B.lo.Xo(1,n)
                                    lo_bound = dyn.B['lo'].Xo[n-1]
                                    hi_bound = dyn.B['hi'].Xo[n-1]
                                else:
                                    # 多维情况：dyn.B.lo.Xo(1,xi{2},...,n)
                                    # 构建多维索引
                                    lo_indices = [0] 
                                    hi_indices = [0]

                                    for k in range(2, len(grd.Nx) + 1):
                                        if k in xi:
                                            lo_indices.append(xi[k])
                                            hi_indices.append(xi[k])
                                        else:
                                            lo_indices.append(0)
                                            hi_indices.append(0)

                                    lo_indices.append(n-1)  # 时间索引
                                    hi_indices.append(n-1)

                                    # 访问多维边界数组
                                    if hasattr(dyn.B['lo'].Xo, 'shape') and len(dyn.B['lo'].Xo.shape) > 1:
                                        lo_bound = dyn.B['lo'].Xo[tuple(lo_indices)]
                                        hi_bound = dyn.B['hi'].Xo[tuple(hi_indices)]
                                    else:
                                        # 如果边界数据是一维的，使用时间索引
                                        lo_bound = dyn.B['lo'].Xo[n-1]
                                        hi_bound = dyn.B['hi'].Xo[n-1]

                                # 构建x1vec
                                x1vec = np.concatenate([
                                    [lo_bound],
                                    current_grd['X'][j][(current_grd['X'][j] > lo_bound) &
                                                       (current_grd['X'][j] < hi_bound)],
                                    [hi_bound]
                                ])

                                # [temp xi{j}] = min(abs(x1vec-inp.X{j}))
                                xi_idx = np.argmin(np.abs(x1vec - inp.X[j]))
                                Xin[j] = x1vec[xi_idx]
                            else:
                                # xi{j} = round((inp.X{j}-current_grd.X{j}(1))/(current_grd.X{j}(2)-current_grd.X{j}(1))) + 1;
                                xi_j = round((inp.X[j] - current_grd['X'][j][0]) /
                                           (current_grd['X'][j][1] - current_grd['X'][j][0])) + 1
                                xi_j = max(xi_j, 1)
                                xi_j = min(xi_j, len(current_grd['X'][j]))
                                xi[j] = xi_j  # 存储到xi字典中，供j==1时使用
                                Xin[j] = current_grd['X'][j][xi_j - 1]
                        else:  # 连续输入
                            Xin[j] = inp.X[j]

                    # 插值获取最优输入
                    if len(grd.Nx) > 1:
                        inp.U[i] = dpm_interpf2sbh(
                            current_grd['X'][1], current_grd['X'][2],
                            dyn.Uo[i][n], Xin[1], Xin[2],
                            np.array([dyn.B['lo'].Xo[n-1], dyn.B['hi'].Xo[n-1]]))
                    else:
                        inp.U[i] = dpm_interpf1sbh(
                            current_grd['X'][1], dyn.Uo[i][n], Xin[1],
                            np.array([dyn.B['lo'].Xo[n-1], dyn.B['hi'].Xo[n-1]]))

            elif grd.Nu:  # 标准输入映射
                for i in range(1, len(grd.Nu) + 1):
                    if options.InputType[i-1] == 'c':  # 连续输入
                        # 多维插值（暂代）
                        if len(grd.Nx) == 1:
                            # 一维插值
                            inp.U[i] = dpm_interpn(
                                current_grd['X'][1], dyn.Uo[i][n], inp.X[1])
                        elif len(grd.Nx) == 2:
                            # 二维插值
                            inp.U[i] = dpm_interpn(
                                current_grd['X'][2], current_grd['X'][1], dyn.Uo[i][n],
                                inp.X[2], inp.X[1])
                        elif len(grd.Nx) == 3:
                            # 三维插值
                            inp.U[i] = dpm_interpn(
                                current_grd['X'][3], current_grd['X'][2], current_grd['X'][1],
                                dyn.Uo[i][n], inp.X[3], inp.X[2], inp.X[1])
                        else:
                            # 超过3维的情况，暂不处理
                            print(f"警告:当前仅支持1-3维插值")

                            if np.isscalar(dyn.Uo[i][n]):
                                inp.U[i] = dyn.Uo[i][n]
                            else:
                                if hasattr(dyn.Uo[i][n], 'flat') and len(dyn.Uo[i][n].flat) > 1:
                                    inp.U[i] = np.interp(inp.X[1], current_grd['X'][1], dyn.Uo[i][n].flat)
                                else:
                                    inp.U[i] = dyn.Uo[i][n] if np.isscalar(dyn.Uo[i][n]) else dyn.Uo[i][n].item()
                    else:  # 离散输入
                        # 离散输入的最近邻方法
                        indices = []
                        for j in range(1, len(grd.Nx) + 1):
                            if j in current_grd['X'] and len(current_grd['X'][j]) > 1:
                                idx = round((inp.X[j] - current_grd['X'][j][0]) /
                                          (current_grd['X'][j][1] - current_grd['X'][j][0])) + 1
                                idx = max(1, min(idx, len(current_grd['X'][j])))
                                indices.append(idx - 1)  # Python索引从0开始
                            else:
                                indices.append(0)

                        # 获取离散输入值
                        if len(indices) == 1:
                            inp.U[i] = dyn.Uo[i][n][indices[0]] if hasattr(dyn.Uo[i][n], '__getitem__') else dyn.Uo[i][n]
                        else:
                            # 多维索引
                            inp.U[i] = dyn.Uo[i][n][tuple(indices)] if hasattr(dyn.Uo[i][n], '__getitem__') else dyn.Uo[i][n]

        # 使用最优输入调用模型
        try:
            if callable(model):
                X, C, I, *extra_outputs = model(inp, par)
                if extra_outputs:
                    outn = extra_outputs[0]  # 如果模型返回额外的输出结构
                else:
                    outn = Output()
            else:
                import importlib
                module = importlib.import_module(model)
                model_func = getattr(module, model)
                X, C, I, *extra_outputs = model_func(inp, par)
                if extra_outputs:
                    outn = extra_outputs[0]
                else:
                    outn = Output()

            # 设置输出结构
            if isinstance(outn, dict):
                # 如果outn是字典，创建一个Output对象
                output_obj = Output()
                output_obj.X = inp.X.copy()
                output_obj.C = C
                output_obj.I = I
                # 复制字典中的其他属性
                for key, value in outn.items():
                    setattr(output_obj, key, value)
                outn = output_obj
            else:
                outn.X = inp.X.copy()
                outn.C = C
                outn.I = I

            # 检查不可行解
            if (hasattr(I, '__len__') and np.any(I != 0)) or (not hasattr(I, '__len__') and I != 0):
                if not iswarned:
                    warnings.warn("DPM:Forward - 不可行解!")
                    iswarned = True

            # 更新状态
            inp.X = X

        except Exception as e:
            warnings.warn(f"前向仿真模型调用错误 at n={n}: {str(e)}")
            # 设置默认输出
            outn = Output()
            outn.X = inp.X.copy()
            outn.C = {1: options.MyInf}
            outn.I = np.array([1])

        # 合并输出结构
        if n > dis.N0:
            out = dpm_mergestruct(out, outn)
        else:
            out = outn

        # 更新进度条
        if hasattr(options, 'Waitbar') and options.Waitbar == 'on':
            progress = (n - dis.N0) / (dis.N - dis.N0)
            # 这里可以添加实际的进度条更新

        if (hasattr(options, 'Verbose') and options.Verbose == 'on' and
            (n - 1) % max(1, dis.N // 100) == 0 and round(100 * n / dis.N) < 100):
            print(f"\b\b\b\b{round(100 * n / dis.N):2d} %", end='', flush=True)

    # 添加最终状态
    for i in range(1, len(outn.X) + 1):
        if i in out.X and i in inp.X:
            if hasattr(out.X[i], '__len__'):
                out.X[i] = np.append(out.X[i], inp.X[i])
            else:
                out.X[i] = [out.X[i], inp.X[i]]

    # 关闭进度条
    if hasattr(options, 'Waitbar') and options.Waitbar == 'on':
        print(" 完成!")

    if hasattr(options, 'Verbose') and options.Verbose == 'on':
        print("\b\b\b\b\b 完成!")

    return out


def dpm_interpf1mb(xx: np.ndarray, yy: np.ndarray, A: np.ndarray,
                   xlim: np.ndarray, ylim: np.ndarray, myInf: float) -> np.ndarray:
    """
    计算给定集合A的一维插值

    参数:
        xx: 值矩阵YY的轴向量
        yy: 值矩阵
        A: 要插值的XX值集合
        xlim: X轴边界 [下边界, 上边界]
        ylim: Y轴边界 [下边界值, 上边界值]
        myInf: 无穷大值

    返回:
        y: 插值后的值集合（与A相同结构）

    """

    # 确保输入为numpy数组
    xx = np.asarray(xx).flatten()
    yy = np.asarray(yy).flatten()
    A = np.asarray(A)
    xlim = np.asarray(xlim)
    ylim = np.asarray(ylim)

    # 保存原始形状
    original_shape = A.shape
    A_flat = A.flatten()

    # 找到刚好在下边界内的网格点
    Iinl_indices = np.where(xx > xlim[0])[0]
    Iinl = Iinl_indices[0] if len(Iinl_indices) > 0 else 0

    # 找到刚好在上边界内的网格点
    Iinu_indices = np.where(xx < xlim[1])[0]
    Iinu = Iinu_indices[-1] if len(Iinu_indices) > 0 else len(xx) - 1

    # 找到下边界和最近网格点之间的插值点
    Ibel = np.where((A_flat >= xlim[0]) & (A_flat < xx[Iinl]))[0]

    # 找到上边界和最近网格点之间的插值点
    Ibeu = np.where((A_flat <= xlim[1]) & (A_flat > xx[Iinu]))[0]

    # 找到边界外的插值点
    Iout = np.where((A_flat < xlim[0]) | (A_flat > xlim[1]))[0]

    # 找到边界内的插值点
    Iin = np.where((A_flat >= xx[Iinl]) & (A_flat <= xx[Iinu]))[0]

    # 找到下边界和上边界之间的插值点
    Iinx = np.where((A_flat >= xlim[0]) & (A_flat <= xlim[1]))[0]

    # 初始化输出
    y = np.zeros_like(A_flat)

    # 用内部点进行常规插值
    if len(Iin) > 0:
        y[Iin] = dpm_interpn(xx, yy, A_flat[Iin])

    # 将外部点设置为无穷大
    if len(Iout) > 0:
        y[Iout] = myInf

    # 如果边界之间有网格点
    boundary_grid_points = np.where((xx < xlim[1]) & (xx > xlim[0]))[0]
    if len(boundary_grid_points) > 0:
        # 在下边界和最近可行网格点之间插值
        if len(Ibel) > 0:
            x_interp = np.array([xlim[0], xx[Iinl]])
            y_interp = np.array([ylim[0], yy[Iinl]])
            y[Ibel] = dpm_interpn(x_interp, y_interp, A_flat[Ibel])

        # 在上边界和最近可行网格点之间插值
        if len(Ibeu) > 0:
            x_interp = np.array([xx[Iinu], xlim[1]])
            y_interp = np.array([yy[Iinu], ylim[1]])
            y[Ibeu] = dpm_interpn(x_interp, y_interp, A_flat[Ibeu])
    else:
        # 如果边界之间没有网格点
        if len(Iinx) > 0:
            y[Iinx] = dpm_interpn(xlim, ylim, A_flat[Iinx])

    # 恢复原始形状
    return y.reshape(original_shape)


def dpm_interpf2mb(xx1: np.ndarray, xx2: np.ndarray, YY: np.ndarray,
                   A1: np.ndarray, A2: np.ndarray, xlim: np.ndarray,
                   ylim: np.ndarray, myInf: float) -> np.ndarray:
    """
    计算给定集合A1,A2的二维插值

    参数:
        xx1: 第一维轴向量
        xx2: 第二维轴向量
        YY: 二维值矩阵
        A1: 第一维要插值的值集合
        A2: 第二维要插值的值集合
        xlim: 边界矩阵 [下边界; 上边界] (2 x length(xx2))
        ylim: 边界值矩阵 [下边界值; 上边界值] (2 x length(xx2))
        myInf: 无穷大值

    返回:
        y: 插值后的值集合（与A1相同结构）
        
    """

    # 确保输入为numpy数组
    xx1 = np.asarray(xx1).flatten()
    xx2 = np.asarray(xx2).flatten()
    YY = np.asarray(YY)
    A1 = np.asarray(A1)
    A2 = np.asarray(A2)
    xlim = np.asarray(xlim)
    ylim = np.asarray(ylim)

    # 保存原始形状
    original_shape = A1.shape
    A1_flat = A1.flatten()
    A2_flat = A2.flatten()

    # 创建重复矩阵用于边界检查
    XX1 = np.tile(xx1.reshape(1, -1), (len(xx2), 1))  # repmat(xx1', length(xx2), 1)
    XLIMl = np.tile(xlim[0, :].reshape(-1, 1), (1, len(xx1)))  # repmat(xlim(1,:)', 1, length(xx1))
    XLIMu = np.tile(xlim[1, :].reshape(-1, 1), (1, len(xx1)))  # repmat(xlim(2,:)', 1, length(xx1))

    # 找到刚好在下边界内的网格点
    r, c = np.where(XX1 > XLIMl)
    if len(r) > 0:
        _, unique_indices = np.unique(r, return_index=True)
        Iinl = c[unique_indices]
    else:
        Iinl = np.zeros(len(xx2), dtype=int)

    # 找到刚好在上边界内的网格点
    r, c = np.where(XX1 < XLIMu)
    if len(r) > 0:
        _, last_indices = np.unique(r[::-1], return_index=True)
        last_indices = len(r) - 1 - last_indices
        Iinu = c[last_indices]
    else:
        Iinu = np.full(len(xx2), len(xx1) - 1, dtype=int)

    # 提取边界值
    xliml = xlim[0, :]  # 下边界
    xlimu = xlim[1, :]  # 上边界
    yliml = ylim[0, :]  # 下边界值
    ylimu = ylim[1, :]  # 上边界值
    xx1l = xx1[Iinl]    # 下边界内的网格点
    xx1u = xx1[Iinu]    # 上边界内的网格点

    # 找到不同区域的插值点
    # 下边界和最近网格点之间的插值点
    xliml_interp = dpm_interpn(xx2, xliml, A2_flat)
    xx1l_interp = dpm_interpn(xx2, xx1l, A2_flat)
    Ibel = np.where((A1_flat >= xliml_interp) & (A1_flat < xx1l_interp))[0]

    # 上边界和最近网格点之间的插值点
    xlimu_interp = dpm_interpn(xx2, xlimu, A2_flat)
    xx1u_interp = dpm_interpn(xx2, xx1u, A2_flat)
    Ibeu = np.where((A1_flat <= xlimu_interp) & (A1_flat > xx1u_interp))[0]

    # 边界外的插值点
    Iout = np.where((A1_flat < xliml_interp) | (A1_flat > xlimu_interp))[0]

    # 边界内的插值点
    Iin = np.where((A1_flat >= xx1l_interp) & (A1_flat <= xx1u_interp))[0]

    # 下边界和上边界之间的插值点
    Iinx = np.where((A1_flat >= xliml_interp) & (A1_flat <= xlimu_interp))[0]

    # 初始化输出
    y = np.full_like(A1_flat, np.nan)

    # 用内部点进行常规插值
    if len(Iin) > 0:
        # 二维插值：使用简化的双线性插值
        y[Iin] = dpm_interpn(xx2, xx1, YY, A2_flat[Iin], A1_flat[Iin])

    # 将外部点设置为无穷大
    if len(Iout) > 0:
        y[Iout] = myInf

    # 如果边界之间有网格点
    if np.any(Iinu - Iinl > 0):
        # 在下边界和最近可行网格点之间插值
        if len(Ibel) > 0:
            Xl = dpm_interpn(xx2, xliml, A2_flat[Ibel])
            Xu = dpm_interpn(xx2, xx1l, A2_flat[Ibel])
            Yl = dpm_interpn(xx2, yliml, Xl)

            # Yu = dpm_interpn(xx2,YY(dpm_sub2ind(size(YY),Iinl,(1:length(xx2))')),Xu);
            YY_extracted = YY[:, Iinl]  # 形状: (len(xx2), len(Iinl))

            # 对每个Ibel点进行插值
            Yu = np.zeros(len(Ibel))
            for i, idx in enumerate(Ibel):
                col_values = YY_extracted[:, i % len(Iinl)]  # 取对应列的值
                Yu[i] = dpm_interpn(xx2, col_values, np.array([Xu[i]]))[0]

            # 线性插值
            with np.errstate(divide='ignore', invalid='ignore'):
                y[Ibel] = Yl + (A1_flat[Ibel] - Xl) / (Xu - Xl) * (Yu - Yl)
                # 处理除零情况
                mask_zero = (Xu - Xl) == 0
                y[Ibel[mask_zero]] = Yl[mask_zero]

        # 在上边界和最近可行网格点之间插值
        if len(Ibeu) > 0:
            Xu = dpm_interpn(xx2, xlimu, A2_flat[Ibeu])
            Xl = dpm_interpn(xx2, xx1u, A2_flat[Ibeu])
            Yu = dpm_interpn(xx2, ylimu, Xu)

            # Yl = dpm_interpn(xx2,YY(dpm_sub2ind(size(YY),Iinu,(1:length(xx2))')),Xl);
            YY_extracted = YY[:, Iinu]  # 形状: (len(xx2), len(Iinu))

            # 对每个Ibeu点进行插值
            Yl = np.zeros(len(Ibeu))
            for i, idx in enumerate(Ibeu):
                col_values = YY_extracted[:, i % len(Iinu)]  # 取对应列的值
                Yl[i] = dpm_interpn(xx2, col_values, np.array([Xl[i]]))[0]

            # 线性插值
            with np.errstate(divide='ignore', invalid='ignore'):
                y[Ibeu] = Yl + (A1_flat[Ibeu] - Xl) / (Xu - Xl) * (Yu - Yl)
                # 处理除零情况
                mask_zero = (Xu - Xl) == 0
                y[Ibeu[mask_zero]] = Yl[mask_zero]
    else:
        # 如果边界之间没有网格点
        if len(Iinx) > 0:
            # 直接在边界之间插值: y(Iinx) = dpm_interpn(xlim,ylim,A(Iinx));
            y[Iinx] = dpm_interpn(xx2, xlim.T, ylim.T, A2_flat[Iinx], A1_flat[Iinx])

    # 恢复原始形状
    return y.reshape(original_shape)


def dpm_interpf1sbh(xx: np.ndarray, yy: np.ndarray, a: float, lim: np.ndarray) -> float:
    """
    计算给定点a的一维插值，使用函数YY(xx)，带边界处理

    参数:
        xx: 值矩阵yy的轴向量
        yy: 值矩阵
        a: 要插值的xx值
        lim: 边界限制 [下边界, 上边界]

    返回:
        y: 插值后的值

    """

    # 确保输入为numpy数组
    xx = np.asarray(xx).flatten()
    yy = np.asarray(yy).flatten()
    lim = np.asarray(lim)

    # 找到边界相关的索引
    xlu_indices = np.where(xx > lim[0])[0]
    xlu = xlu_indices[0] if len(xlu_indices) > 0 else None

    xll_indices = np.where(xx <= lim[0])[0]
    xll = xll_indices[-1] if len(xll_indices) > 0 else None

    xuu_indices = np.where(xx >= lim[1])[0]
    xuu = xuu_indices[0] if len(xuu_indices) > 0 else None

    xul_indices = np.where(xx < lim[1])[0]
    xul = xul_indices[-1] if len(xul_indices) > 0 else None

    # 根据a的位置进行不同的插值处理
    if a <= lim[0]:
        # if a is between lower limit and regular grid (下边界内插值)
        y = yy[xll] if xll is not None else 0
    elif a >= lim[1]:
        # if a is outside upper limit (上边界外插值)
        y = yy[xuu] if xuu is not None else 0
    elif xlu is not None and a < xx[xlu] and a > lim[0]:
        # if a is inside limits and within regular grid (下边界内插值)
        dy = yy[xlu] - yy[xll]
        dx = xx[xlu] - lim[0]
        y = (a - lim[0]) * dy / dx + yy[xll]
    elif xul is not None and a < lim[1] and a > xx[xul]:
        # if a is between upper limit and regular grid (上边界内插值)
        dy = yy[xuu] - yy[xul]
        dx = lim[1] - xx[xul]
        y = (a - xx[xul]) * dy / dx + yy[xul]
    else:
        # if a is outside lower limit (下边界外插值)
        y = dpm_interpn(xx, yy, np.array([a]))[0]

    # 确保返回标量
    try:
        y = float(y)
        return y
    except (ValueError, TypeError):
        if hasattr(y, 'flatten'):
            y_flat = y.flatten()
            return float(y_flat[0]) if len(y_flat) > 0 else 0.0
        else:
            return 0.0


def dpm_interpf2sbh(xx1: np.ndarray, xx2: np.ndarray, YY: np.ndarray,
                    a1: float, a2: float, lim: np.ndarray) -> float:
    """
    计算给定点(a1,a2)的二维边界处理插值，使用函数YY(xx1,xx2)

    参数:
        xx1: 第一维轴向量
        xx2: 第二维轴向量
        YY: 二维值矩阵
        a1: 第一维要插值的值
        a2: 第二维要插值的值
        lim: 边界矩阵 [下边界; 上边界] (2 x length(xx2))

    返回:
        y: 插值后的值

    """

    # 确保输入为numpy数组
    xx1 = np.asarray(xx1).flatten()
    xx2 = np.asarray(xx2).flatten()
    YY = np.asarray(YY)
    lim = np.asarray(lim)

    # 步骤1: 根据a2插值得到对应的边界限制
    lim2_lower = dpm_interpn(xx2, lim[0, :], np.array([a2]))[0]

    lim2_upper = dpm_interpn(xx2, lim[1, :], np.array([a2]))[0]

    lim2 = np.array([lim2_lower, lim2_upper])

    # 步骤2: 在xx2=a2处提取一维切片
    a2_array = np.full_like(xx1, a2)

    yy = dpm_interpn(xx2, xx1, YY, a2_array, xx1)

    # 步骤3: 使用一维边界处理插值
    y = dpm_interpf1sbh(xx1, yy, a1, lim2)

    return float(y)


def dpm_interpn(*args) -> np.ndarray:
    """
    多维插值函数

    参数:
        xx, xx1, xx2, ...: 各维度的网格向量
        YY: 多维值矩阵
        A, A1, A2, ...: 各维度的查询点

    返回:
        y: 插值结果

    """

    if len(args) == 3:
        # 1维插值: dpm_interpn(xx, yy, A)
        return _dpm_interpn_1d(args[0], args[1], args[2])
    elif len(args) == 5:
        # 2维插值: dpm_interpn(xx2, xx1, YY, A2, A1)
        return _dpm_interpn_2d(args[0], args[1], args[2], args[3], args[4])
    elif len(args) == 7:
        # 3维插值: dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
        return _dpm_interpn_3d(args[0], args[1], args[2], args[3], args[4], args[5], args[6])
    elif len(args) == 9:
        # 4维插值: dpm_interpn(xx4, xx3, xx2, xx1, YY, A4, A3, A2, A1)
        return _dpm_interpn_4d(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7], args[8])
    elif len(args) == 11:
        # 5维插值: dpm_interpn(xx5, xx4, xx3, xx2, xx1, YY, A5, A4, A3, A2, A1)
        return _dpm_interpn_5d(args[0], args[1], args[2], args[3], args[4], args[5],
                              args[6], args[7], args[8], args[9], args[10])
    elif len(args) == 13:
        # 6维插值: dpm_interpn(xx6, xx5, xx4, xx3, xx2, xx1, YY, A6, A5, A4, A3, A2, A1)
        return _dpm_interpn_6d(args[0], args[1], args[2], args[3], args[4], args[5], args[6],
                              args[7], args[8], args[9], args[10], args[11], args[12])
    else:
        raise ValueError("DPM:Internal - 状态或输入异常")


def _dpm_interpn_1d(xx: np.ndarray, yy: np.ndarray, A: np.ndarray) -> np.ndarray:
    """
    一维插值实现

    参数:
        xx: 网格向量
        yy: 值向量
        A: 查询点

    返回:
        插值结果，形状与A相同

    """
    # 确保输入为numpy数组并保存原始形状
    A = np.asarray(A)
    original_shape = A.shape

    # 重塑查询点为列向量
    Ars = A.flatten().reshape(-1, 1)

    # 重塑网格向量为列向量
    xx_reshaped = np.asarray(xx).flatten().reshape(-1, 1)
    yy = np.asarray(yy).flatten()

    # 计算网格间距
    h = max(np.max(np.diff(xx_reshaped.flatten())), np.finfo(float).eps)

    # 边界处理：将超出范围的查询点限制在网格边界内
    Ars[Ars[:, 0] < np.min(xx_reshaped), 0] = np.min(xx_reshaped)
    Ars[Ars[:, 0] > np.max(xx_reshaped), 0] = np.max(xx_reshaped)

    # 下界索引
    ind_lower = 1 + np.floor(np.round((Ars[:, 0] - xx_reshaped[0, 0]) / h * 1e8) * 1e-8).astype(int)

    # 上界索引
    ind_upper = 1 + np.ceil(np.round((Ars[:, 0] - xx_reshaped[0, 0]) / h * 1e8) * 1e-8).astype(int)

    # 确保索引在有效范围内
    ind_lower = np.clip(ind_lower, 1, len(yy))
    ind_upper = np.clip(ind_upper, 1, len(yy))

    # 提取两个端点的值
    # 一维情况，dpm_sub2ind只是返回索引本身，yy_lower = yy[dpm_sub2ind(yy.shape, ind_lower) - 1]
    yy_lower = yy[ind_lower - 1]
    yy_upper = yy[ind_upper - 1]


    # 计算插值权重
    da = (Ars[:, 0] - xx_reshaped[ind_lower - 1, 0]) / h

    # 线性插值
    yi = yy_lower + da * (yy_upper - yy_lower)

    # 重塑为原始形状
    return yi.reshape(original_shape)


def _dpm_interpn_2d(xx2: np.ndarray, xx1: np.ndarray, YY: np.ndarray,
                    A2: np.ndarray, A1: np.ndarray) -> np.ndarray:
    """
    返回:
        插值结果，形状与A1相同

    """
    # 确保输入为numpy数组并保存原始形状
    A1 = np.asarray(A1)
    A2 = np.asarray(A2)
    original_shape = A1.shape

    # 重塑查询点为列向量
    Ars = np.column_stack([A1.flatten(), A2.flatten()])

    # 重塑网格向量为列向量
    xx = {}
    xx[1] = np.asarray(xx1).flatten().reshape(-1, 1)
    xx[2] = np.asarray(xx2).flatten().reshape(-1, 1)

    YY = np.asarray(YY)

    # 计算网格间距
    h = np.array([
        max(np.max(np.diff(xx[1].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[2].flatten())), np.finfo(float).eps)
    ])

    # 边界处理：将超出范围的查询点限制在网格边界内
    Ars[:, 0] = np.clip(Ars[:, 0], np.min(xx[1]), np.max(xx[1]))
    Ars[:, 1] = np.clip(Ars[:, 1], np.min(xx[2]), np.max(xx[2]))

    # 计算索引
    num_points = len(Ars)
    ind = np.zeros((num_points, 2, 2), dtype=int)

    # 下界和上界索引计算
    ind[:, 0, 0] = 1 + np.floor(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 0] = 1 + np.floor(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 0, 1] = 1 + np.ceil(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 1] = 1 + np.ceil(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)

    # 确保索引在有效范围内
    ind[:, 0, :] = np.clip(ind[:, 0, :], 1, len(xx[1]))
    ind[:, 1, :] = np.clip(ind[:, 1, :], 1, len(xx[2]))

    # 提取四个角点的值
    yy = np.zeros((num_points, 2, 2))

    # 在MATLAB中，dpm_sub2ind(size(YY), row_idx, col_idx) 返回线性索引
    # 对于2D矩阵，这等价于 YY[row_idx-1, col_idx-1]（转换为0基索引）
    # 重要：在MATLAB中，ind(:,1,x)对应xx1（列索引），ind(:,2,x)对应xx2（行索引）
    # 所以 YY[行索引-1, 列索引-1] = YY[ind(:,2,x)-1, ind(:,1,x)-1]
    yy[:, 0, 0] = YY[ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1) = YY[xx2下界, xx1下界]
    yy[:, 1, 0] = YY[ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1) = YY[xx2下界, xx1上界]
    yy[:, 0, 1] = YY[ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2) = YY[xx2上界, xx1下界]
    yy[:, 1, 1] = YY[ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2) = YY[xx2上界, xx1上界]

    # 计算插值权重
    da = np.zeros((num_points, 2))
    da[:, 0] = (Ars[:, 0] - xx[1][ind[:, 0, 0] - 1].flatten()) / h[0]
    da[:, 1] = (Ars[:, 1] - xx[2][ind[:, 1, 0] - 1].flatten()) / h[1]

    # 双线性插值
    v1 = np.zeros((num_points, 2))
    v1[:, 0] = yy[:, 0, 0] + da[:, 0] * (yy[:, 1, 0] - yy[:, 0, 0])
    v1[:, 1] = yy[:, 0, 1] + da[:, 0] * (yy[:, 1, 1] - yy[:, 0, 1])

    yi = da[:, 1] * (v1[:, 1] - v1[:, 0]) + v1[:, 0]

    # 重塑为原始形状
    return yi.reshape(original_shape)


def _dpm_interpn_3d(xx3: np.ndarray, xx2: np.ndarray, xx1: np.ndarray, YY: np.ndarray,
                    A3: np.ndarray, A2: np.ndarray, A1: np.ndarray) -> np.ndarray:
    """
    三维插值实现

    参数：
        xx3: 第三维网格向量
        xx2: 第二维网格向量
        xx1: 第一维网格向量
        YY: 三维值矩阵
        A3: 第三维查询点
        A2: 第二维查询点
        A1: 第一维查询点

    返回:
        插值结果，形状与A1相同

    """
    # 确保输入为numpy数组并保存原始形状
    A1 = np.asarray(A1)
    A2 = np.asarray(A2)
    A3 = np.asarray(A3)
    original_shape = A1.shape

    # 重塑查询点为列向量
    Ars = np.column_stack([A1.flatten(), A2.flatten(), A3.flatten()])

    # 重塑网格向量为列向量
    xx = {}
    xx[1] = np.asarray(xx1).flatten().reshape(-1, 1)  # xx1 -> YY的第三个维度
    xx[2] = np.asarray(xx2).flatten().reshape(-1, 1)  # xx2 -> YY的第二个维度
    xx[3] = np.asarray(xx3).flatten().reshape(-1, 1)  # xx3 -> YY的第一个维度

    YY = np.asarray(YY)

    # 计算网格间距
    h = np.array([
        max(np.max(np.diff(xx[1].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[2].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[3].flatten())), np.finfo(float).eps)
    ])

    # 边界处理：将超出范围的查询点限制在网格边界内
    Ars[:, 0] = np.clip(Ars[:, 0], np.min(xx[1]), np.max(xx[1]))
    Ars[:, 1] = np.clip(Ars[:, 1], np.min(xx[2]), np.max(xx[2]))
    Ars[:, 2] = np.clip(Ars[:, 2], np.min(xx[3]), np.max(xx[3]))

    # 计算索引
    num_points = len(Ars)
    ind = np.zeros((num_points, 3, 2), dtype=int)

    # 下界和上界索引计算
    ind[:, 0, 0] = 1 + np.floor(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 0] = 1 + np.floor(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 0] = 1 + np.floor(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)

    ind[:, 0, 1] = 1 + np.ceil(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 1] = 1 + np.ceil(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 1] = 1 + np.ceil(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)

    # 确保索引在有效范围内
    ind[:, 0, :] = np.clip(ind[:, 0, :], 1, len(xx[1]))
    ind[:, 1, :] = np.clip(ind[:, 1, :], 1, len(xx[2]))
    ind[:, 2, :] = np.clip(ind[:, 2, :], 1, len(xx[3]))

    # 提取8个角点的值
    yy = np.zeros((num_points, 2, 2, 2))

    # 在MATLAB中，dpm_sub2ind(size(YY), dim1_idx, dim2_idx, dim3_idx) 返回线性索引
    # 对于3D矩阵，这等价于 YY[dim1_idx-1, dim2_idx-1, dim3_idx-1]（转换为0基索引）
    # 重要：在MATLAB中，ind(:,1,x)对应xx1（第三维索引），ind(:,2,x)对应xx2（第二维索引），ind(:,3,x)对应xx3（第一维索引）
    # 所以 YY[第一维索引-1, 第二维索引-1, 第三维索引-1] = YY[ind(:,3,x)-1, ind(:,2,x)-1, ind(:,1,x)-1]
    yy[:, 0, 0, 0] = YY[ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1) = YY[xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0] = YY[ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1) = YY[xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0] = YY[ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1) = YY[xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0] = YY[ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1) = YY[xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1] = YY[ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2) = YY[xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1] = YY[ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2) = YY[xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1] = YY[ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2) = YY[xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1] = YY[ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2) = YY[xx3上, xx2上, xx1上]

    # 计算插值权重
    da = np.zeros((num_points, 3))
    da[:, 0] = (Ars[:, 0] - xx[1][ind[:, 0, 0] - 1].flatten()) / h[0]
    da[:, 1] = (Ars[:, 1] - xx[2][ind[:, 1, 0] - 1].flatten()) / h[1]
    da[:, 2] = (Ars[:, 2] - xx[3][ind[:, 2, 0] - 1].flatten()) / h[2]

    # 确保插值权重在[0,1]范围内
    da = np.clip(da, 0, 1)

    # 三线性插值 
    # 第一步：沿第三维插值
    v2 = np.zeros((num_points, 2, 2))
    v2[:, 0, 0] = yy[:, 0, 0, 0] + da[:, 2] * (yy[:, 0, 0, 1] - yy[:, 0, 0, 0])
    v2[:, 1, 0] = yy[:, 1, 0, 0] + da[:, 2] * (yy[:, 1, 0, 1] - yy[:, 1, 0, 0])
    v2[:, 0, 1] = yy[:, 0, 1, 0] + da[:, 2] * (yy[:, 0, 1, 1] - yy[:, 0, 1, 0])
    v2[:, 1, 1] = yy[:, 1, 1, 0] + da[:, 2] * (yy[:, 1, 1, 1] - yy[:, 1, 1, 0])

    # 第二步：沿第二维插值
    v1 = np.zeros((num_points, 2))
    v1[:, 0] = v2[:, 0, 0] + da[:, 1] * (v2[:, 0, 1] - v2[:, 0, 0])
    v1[:, 1] = v2[:, 1, 0] + da[:, 1] * (v2[:, 1, 1] - v2[:, 1, 0])

    # 第三步：沿第一维插值
    yi = da[:, 0] * (v1[:, 1] - v1[:, 0]) + v1[:, 0]

    # 重塑为原始形状
    return yi.reshape(original_shape)


def _dpm_interpn_4d(xx4: np.ndarray, xx3: np.ndarray, xx2: np.ndarray, xx1: np.ndarray,
                    YY: np.ndarray, A4: np.ndarray, A3: np.ndarray, A2: np.ndarray, A1: np.ndarray) -> np.ndarray:
    """
    四维插值实现

    参数：
        xx4: 第四维网格向量
        xx3: 第三维网格向量
        xx2: 第二维网格向量
        xx1: 第一维网格向量
        YY: 四维值矩阵
        A4: 第四维查询点
        A3: 第三维查询点
        A2: 第二维查询点
        A1: 第一维查询点

    返回:
        插值结果，形状与A1相同

    """
    # 确保输入为numpy数组并保存原始形状
    A1 = np.asarray(A1)
    A2 = np.asarray(A2)
    A3 = np.asarray(A3)
    A4 = np.asarray(A4)
    original_shape = A1.shape

    # 重塑查询点为列向量
    Ars = np.column_stack([A1.flatten(), A2.flatten(), A3.flatten(), A4.flatten()])

    # 重塑网格向量为列向量
    xx = {}
    xx[1] = np.asarray(xx1).flatten().reshape(-1, 1)  # xx1 -> YY的第四个维度
    xx[2] = np.asarray(xx2).flatten().reshape(-1, 1)  # xx2 -> YY的第三个维度
    xx[3] = np.asarray(xx3).flatten().reshape(-1, 1)  # xx3 -> YY的第二个维度
    xx[4] = np.asarray(xx4).flatten().reshape(-1, 1)  # xx4 -> YY的第一个维度

    YY = np.asarray(YY)

    # 计算网格间距
    h = np.array([
        max(np.max(np.diff(xx[1].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[2].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[3].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[4].flatten())), np.finfo(float).eps)
    ])

    # 边界处理
    Ars[:, 0] = np.clip(Ars[:, 0], np.min(xx[1]), np.max(xx[1]))
    Ars[:, 1] = np.clip(Ars[:, 1], np.min(xx[2]), np.max(xx[2]))
    Ars[:, 2] = np.clip(Ars[:, 2], np.min(xx[3]), np.max(xx[3]))
    Ars[:, 3] = np.clip(Ars[:, 3], np.min(xx[4]), np.max(xx[4]))

    # 计算索引
    num_points = len(Ars)
    ind = np.zeros((num_points, 4, 2), dtype=int)

    # 下界和上界索引计算
    ind[:, 0, 0] = 1 + np.floor(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 0] = 1 + np.floor(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 0] = 1 + np.floor(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)
    ind[:, 3, 0] = 1 + np.floor(np.round((Ars[:, 3] - xx[4][0]) / h[3] * 1e8) * 1e-8).astype(int)

    ind[:, 0, 1] = 1 + np.ceil(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 1] = 1 + np.ceil(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 1] = 1 + np.ceil(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)
    ind[:, 3, 1] = 1 + np.ceil(np.round((Ars[:, 3] - xx[4][0]) / h[3] * 1e8) * 1e-8).astype(int)

    # 确保索引在有效范围内
    ind[:, 0, :] = np.clip(ind[:, 0, :], 1, len(xx[1]))
    ind[:, 1, :] = np.clip(ind[:, 1, :], 1, len(xx[2]))
    ind[:, 2, :] = np.clip(ind[:, 2, :], 1, len(xx[3]))
    ind[:, 3, :] = np.clip(ind[:, 3, :], 1, len(xx[4]))

    # 提取16个超立方体顶点的值
    yy = np.zeros((num_points, 2, 2, 2, 2))

    yy[:, 0, 0, 0, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1) = YY[xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1) = YY[xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1) = YY[xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1) = YY[xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1) = YY[xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1) = YY[xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1) = YY[xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0] = YY[ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1) = YY[xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2) = YY[xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2) = YY[xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2) = YY[xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2) = YY[xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2) = YY[xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2) = YY[xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2) = YY[xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1] = YY[ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2) = YY[xx4上, xx3上, xx2上, xx1上]

    # 计算插值权重
    da = np.zeros((num_points, 4))
    da[:, 0] = (Ars[:, 0] - xx[1][ind[:, 0, 0] - 1].flatten()) / h[0]
    da[:, 1] = (Ars[:, 1] - xx[2][ind[:, 1, 0] - 1].flatten()) / h[1]
    da[:, 2] = (Ars[:, 2] - xx[3][ind[:, 2, 0] - 1].flatten()) / h[2]
    da[:, 3] = (Ars[:, 3] - xx[4][ind[:, 3, 0] - 1].flatten()) / h[3]

    # 四线性插值
    # 第一步：沿第四维插值
    v3 = np.zeros((num_points, 2, 2, 2))
    v3[:, 0, 0, 0] = yy[:, 0, 0, 0, 0] + da[:, 3] * (yy[:, 0, 0, 0, 1] - yy[:, 0, 0, 0, 0])
    v3[:, 1, 0, 0] = yy[:, 1, 0, 0, 0] + da[:, 3] * (yy[:, 1, 0, 0, 1] - yy[:, 1, 0, 0, 0])
    v3[:, 0, 1, 0] = yy[:, 0, 1, 0, 0] + da[:, 3] * (yy[:, 0, 1, 0, 1] - yy[:, 0, 1, 0, 0])
    v3[:, 1, 1, 0] = yy[:, 1, 1, 0, 0] + da[:, 3] * (yy[:, 1, 1, 0, 1] - yy[:, 1, 1, 0, 0])
    v3[:, 0, 0, 1] = yy[:, 0, 0, 1, 0] + da[:, 3] * (yy[:, 0, 0, 1, 1] - yy[:, 0, 0, 1, 0])
    v3[:, 1, 0, 1] = yy[:, 1, 0, 1, 0] + da[:, 3] * (yy[:, 1, 0, 1, 1] - yy[:, 1, 0, 1, 0])
    v3[:, 0, 1, 1] = yy[:, 0, 1, 1, 0] + da[:, 3] * (yy[:, 0, 1, 1, 1] - yy[:, 0, 1, 1, 0])
    v3[:, 1, 1, 1] = yy[:, 1, 1, 1, 0] + da[:, 3] * (yy[:, 1, 1, 1, 1] - yy[:, 1, 1, 1, 0])

    # 第二步：沿第三维插值
    v2 = np.zeros((num_points, 2, 2))
    v2[:, 0, 0] = v3[:, 0, 0, 0] + da[:, 2] * (v3[:, 0, 0, 1] - v3[:, 0, 0, 0])
    v2[:, 1, 0] = v3[:, 1, 0, 0] + da[:, 2] * (v3[:, 1, 0, 1] - v3[:, 1, 0, 0])
    v2[:, 0, 1] = v3[:, 0, 1, 0] + da[:, 2] * (v3[:, 0, 1, 1] - v3[:, 0, 1, 0])
    v2[:, 1, 1] = v3[:, 1, 1, 0] + da[:, 2] * (v3[:, 1, 1, 1] - v3[:, 1, 1, 0])

    # 第三步：沿第二维插值
    v1 = np.zeros((num_points, 2))
    v1[:, 0] = v2[:, 0, 0] + da[:, 1] * (v2[:, 0, 1] - v2[:, 0, 0])
    v1[:, 1] = v2[:, 1, 0] + da[:, 1] * (v2[:, 1, 1] - v2[:, 1, 0])

    # 第四步：沿第一维插值
    yi = da[:, 0] * (v1[:, 1] - v1[:, 0]) + v1[:, 0]

    # 重塑为原始形状
    return yi.reshape(original_shape)


def _dpm_interpn_5d(xx5: np.ndarray, xx4: np.ndarray, xx3: np.ndarray, xx2: np.ndarray, xx1: np.ndarray,
                    YY: np.ndarray, A5: np.ndarray, A4: np.ndarray, A3: np.ndarray, A2: np.ndarray, A1: np.ndarray) -> np.ndarray:
    """
    五维插值实现

    参数：
        xx5: 第五维网格向量
        xx4: 第四维网格向量
        xx3: 第三维网格向量
        xx2: 第二维网格向量
        xx1: 第一维网格向量
        YY: 五维值矩阵
        A5: 第五维查询点
        A4: 第四维查询点
        A3: 第三维查询点
        A2: 第二维查询点
        A1: 第一维查询点

    返回:
        插值结果，形状与A1相同

    """
    # 确保输入为numpy数组并保存原始形状
    A1 = np.asarray(A1)
    A2 = np.asarray(A2)
    A3 = np.asarray(A3)
    A4 = np.asarray(A4)
    A5 = np.asarray(A5)
    original_shape = A1.shape

    # 重塑查询点为列向量
    Ars = np.column_stack([A1.flatten(), A2.flatten(), A3.flatten(), A4.flatten(), A5.flatten()])

    # 重塑网格向量为列向量
    xx = {}
    xx[1] = np.asarray(xx1).flatten().reshape(-1, 1)  # xx1 -> YY的第五个维度
    xx[2] = np.asarray(xx2).flatten().reshape(-1, 1)  # xx2 -> YY的第四个维度
    xx[3] = np.asarray(xx3).flatten().reshape(-1, 1)  # xx3 -> YY的第三个维度
    xx[4] = np.asarray(xx4).flatten().reshape(-1, 1)  # xx4 -> YY的第二个维度
    xx[5] = np.asarray(xx5).flatten().reshape(-1, 1)  # xx5 -> YY的第一个维度

    YY = np.asarray(YY)

    # 计算网格间距
    h = np.array([
        max(np.max(np.diff(xx[1].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[2].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[3].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[4].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[5].flatten())), np.finfo(float).eps)
    ])

    # 边界处理：将超出范围的查询点限制在网格边界内
    Ars[:, 0] = np.clip(Ars[:, 0], np.min(xx[1]), np.max(xx[1]))
    Ars[:, 1] = np.clip(Ars[:, 1], np.min(xx[2]), np.max(xx[2]))
    Ars[:, 2] = np.clip(Ars[:, 2], np.min(xx[3]), np.max(xx[3]))
    Ars[:, 3] = np.clip(Ars[:, 3], np.min(xx[4]), np.max(xx[4]))
    Ars[:, 4] = np.clip(Ars[:, 4], np.min(xx[5]), np.max(xx[5]))

    # 计算索引
    num_points = len(Ars)
    ind = np.zeros((num_points, 5, 2), dtype=int)

    # 下界和上界索引计算
    ind[:, 0, 0] = 1 + np.floor(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 0] = 1 + np.floor(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 0] = 1 + np.floor(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)
    ind[:, 3, 0] = 1 + np.floor(np.round((Ars[:, 3] - xx[4][0]) / h[3] * 1e8) * 1e-8).astype(int)
    ind[:, 4, 0] = 1 + np.floor(np.round((Ars[:, 4] - xx[5][0]) / h[4] * 1e8) * 1e-8).astype(int)

    ind[:, 0, 1] = 1 + np.ceil(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 1] = 1 + np.ceil(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 1] = 1 + np.ceil(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)
    ind[:, 3, 1] = 1 + np.ceil(np.round((Ars[:, 3] - xx[4][0]) / h[3] * 1e8) * 1e-8).astype(int)
    ind[:, 4, 1] = 1 + np.ceil(np.round((Ars[:, 4] - xx[5][0]) / h[4] * 1e8) * 1e-8).astype(int)

    # 确保索引在有效范围内
    ind[:, 0, :] = np.clip(ind[:, 0, :], 1, len(xx[1]))
    ind[:, 1, :] = np.clip(ind[:, 1, :], 1, len(xx[2]))
    ind[:, 2, :] = np.clip(ind[:, 2, :], 1, len(xx[3]))
    ind[:, 3, :] = np.clip(ind[:, 3, :], 1, len(xx[4]))
    ind[:, 4, :] = np.clip(ind[:, 4, :], 1, len(xx[5]))

    # 提取32个超立方体顶点的值
    yy = np.zeros((num_points, 2, 2, 2, 2, 2))

    # 第一组：第5维=下界的16个顶点
    yy[:, 0, 0, 0, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1,1) = YY[xx5下, xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1,1) = YY[xx5下, xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1,1) = YY[xx5下, xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1,1) = YY[xx5下, xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1,1) = YY[xx5下, xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1,1) = YY[xx5下, xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1,1) = YY[xx5下, xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1,1) = YY[xx5下, xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2,1) = YY[xx5下, xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2,1) = YY[xx5下, xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2,1) = YY[xx5下, xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2,1) = YY[xx5下, xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2,1) = YY[xx5下, xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2,1) = YY[xx5下, xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2,1) = YY[xx5下, xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1, 0] = YY[ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2,1) = YY[xx5下, xx4上, xx3上, xx2上, xx1上]

    # 第二组：第5维=上界的16个顶点
    yy[:, 0, 0, 0, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1,2) = YY[xx5上, xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1,2) = YY[xx5上, xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1,2) = YY[xx5上, xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1,2) = YY[xx5上, xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1,2) = YY[xx5上, xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1,2) = YY[xx5上, xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1,2) = YY[xx5上, xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1,2) = YY[xx5上, xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2,2) = YY[xx5上, xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2,2) = YY[xx5上, xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2,2) = YY[xx5上, xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2,2) = YY[xx5上, xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2,2) = YY[xx5上, xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2,2) = YY[xx5上, xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2,2) = YY[xx5上, xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1, 1] = YY[ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2,2) = YY[xx5上, xx4上, xx3上, xx2上, xx1上]

    # 计算插值权重
    da = np.zeros((num_points, 5))
    da[:, 0] = (Ars[:, 0] - xx[1][ind[:, 0, 0] - 1].flatten()) / h[0]
    da[:, 1] = (Ars[:, 1] - xx[2][ind[:, 1, 0] - 1].flatten()) / h[1]
    da[:, 2] = (Ars[:, 2] - xx[3][ind[:, 2, 0] - 1].flatten()) / h[2]
    da[:, 3] = (Ars[:, 3] - xx[4][ind[:, 3, 0] - 1].flatten()) / h[3]
    da[:, 4] = (Ars[:, 4] - xx[5][ind[:, 4, 0] - 1].flatten()) / h[4]

    # 五线性插值
    # 第一步：沿第五维插值
    v4 = np.zeros((num_points, 2, 2, 2, 2))
    v4[:, 0, 0, 0, 0] = yy[:, 0, 0, 0, 0, 0] + da[:, 4] * (yy[:, 0, 0, 0, 0, 1] - yy[:, 0, 0, 0, 0, 0])
    v4[:, 1, 0, 0, 0] = yy[:, 1, 0, 0, 0, 0] + da[:, 4] * (yy[:, 1, 0, 0, 0, 1] - yy[:, 1, 0, 0, 0, 0])
    v4[:, 0, 1, 0, 0] = yy[:, 0, 1, 0, 0, 0] + da[:, 4] * (yy[:, 0, 1, 0, 0, 1] - yy[:, 0, 1, 0, 0, 0])
    v4[:, 1, 1, 0, 0] = yy[:, 1, 1, 0, 0, 0] + da[:, 4] * (yy[:, 1, 1, 0, 0, 1] - yy[:, 1, 1, 0, 0, 0])
    v4[:, 0, 0, 1, 0] = yy[:, 0, 0, 1, 0, 0] + da[:, 4] * (yy[:, 0, 0, 1, 0, 1] - yy[:, 0, 0, 1, 0, 0])
    v4[:, 1, 0, 1, 0] = yy[:, 1, 0, 1, 0, 0] + da[:, 4] * (yy[:, 1, 0, 1, 0, 1] - yy[:, 1, 0, 1, 0, 0])
    v4[:, 0, 1, 1, 0] = yy[:, 0, 1, 1, 0, 0] + da[:, 4] * (yy[:, 0, 1, 1, 0, 1] - yy[:, 0, 1, 1, 0, 0])
    v4[:, 1, 1, 1, 0] = yy[:, 1, 1, 1, 0, 0] + da[:, 4] * (yy[:, 1, 1, 1, 0, 1] - yy[:, 1, 1, 1, 0, 0])
    v4[:, 0, 0, 0, 1] = yy[:, 0, 0, 0, 1, 0] + da[:, 4] * (yy[:, 0, 0, 0, 1, 1] - yy[:, 0, 0, 0, 1, 0])
    v4[:, 1, 0, 0, 1] = yy[:, 1, 0, 0, 1, 0] + da[:, 4] * (yy[:, 1, 0, 0, 1, 1] - yy[:, 1, 0, 0, 1, 0])
    v4[:, 0, 1, 0, 1] = yy[:, 0, 1, 0, 1, 0] + da[:, 4] * (yy[:, 0, 1, 0, 1, 1] - yy[:, 0, 1, 0, 1, 0])
    v4[:, 1, 1, 0, 1] = yy[:, 1, 1, 0, 1, 0] + da[:, 4] * (yy[:, 1, 1, 0, 1, 1] - yy[:, 1, 1, 0, 1, 0])
    v4[:, 0, 0, 1, 1] = yy[:, 0, 0, 1, 1, 0] + da[:, 4] * (yy[:, 0, 0, 1, 1, 1] - yy[:, 0, 0, 1, 1, 0])
    v4[:, 1, 0, 1, 1] = yy[:, 1, 0, 1, 1, 0] + da[:, 4] * (yy[:, 1, 0, 1, 1, 1] - yy[:, 1, 0, 1, 1, 0])
    v4[:, 0, 1, 1, 1] = yy[:, 0, 1, 1, 1, 0] + da[:, 4] * (yy[:, 0, 1, 1, 1, 1] - yy[:, 0, 1, 1, 1, 0])
    v4[:, 1, 1, 1, 1] = yy[:, 1, 1, 1, 1, 0] + da[:, 4] * (yy[:, 1, 1, 1, 1, 1] - yy[:, 1, 1, 1, 1, 0])

    # 第二步：沿第四维插值
    v3 = np.zeros((num_points, 2, 2, 2))
    v3[:, 0, 0, 0] = v4[:, 0, 0, 0, 0] + da[:, 3] * (v4[:, 0, 0, 0, 1] - v4[:, 0, 0, 0, 0])
    v3[:, 1, 0, 0] = v4[:, 1, 0, 0, 0] + da[:, 3] * (v4[:, 1, 0, 0, 1] - v4[:, 1, 0, 0, 0])
    v3[:, 0, 1, 0] = v4[:, 0, 1, 0, 0] + da[:, 3] * (v4[:, 0, 1, 0, 1] - v4[:, 0, 1, 0, 0])
    v3[:, 1, 1, 0] = v4[:, 1, 1, 0, 0] + da[:, 3] * (v4[:, 1, 1, 0, 1] - v4[:, 1, 1, 0, 0])
    v3[:, 0, 0, 1] = v4[:, 0, 0, 1, 0] + da[:, 3] * (v4[:, 0, 0, 1, 1] - v4[:, 0, 0, 1, 0])
    v3[:, 1, 0, 1] = v4[:, 1, 0, 1, 0] + da[:, 3] * (v4[:, 1, 0, 1, 1] - v4[:, 1, 0, 1, 0])
    v3[:, 0, 1, 1] = v4[:, 0, 1, 1, 0] + da[:, 3] * (v4[:, 0, 1, 1, 1] - v4[:, 0, 1, 1, 0])
    v3[:, 1, 1, 1] = v4[:, 1, 1, 1, 0] + da[:, 3] * (v4[:, 1, 1, 1, 1] - v4[:, 1, 1, 1, 0])

    # 第三步：沿第三维插值
    v2 = np.zeros((num_points, 2, 2))
    v2[:, 0, 0] = v3[:, 0, 0, 0] + da[:, 2] * (v3[:, 0, 0, 1] - v3[:, 0, 0, 0])
    v2[:, 1, 0] = v3[:, 1, 0, 0] + da[:, 2] * (v3[:, 1, 0, 1] - v3[:, 1, 0, 0])
    v2[:, 0, 1] = v3[:, 0, 1, 0] + da[:, 2] * (v3[:, 0, 1, 1] - v3[:, 0, 1, 0])
    v2[:, 1, 1] = v3[:, 1, 1, 0] + da[:, 2] * (v3[:, 1, 1, 1] - v3[:, 1, 1, 0])

    # 第四步：沿第二维插值
    v1 = np.zeros((num_points, 2))
    v1[:, 0] = v2[:, 0, 0] + da[:, 1] * (v2[:, 0, 1] - v2[:, 0, 0])
    v1[:, 1] = v2[:, 1, 0] + da[:, 1] * (v2[:, 1, 1] - v2[:, 1, 0])

    # 第五步：沿第一维插值
    yi = da[:, 0] * (v1[:, 1] - v1[:, 0]) + v1[:, 0]

    # 重塑为原始形状
    return yi.reshape(original_shape)


def _dpm_interpn_6d(xx6: np.ndarray, xx5: np.ndarray, xx4: np.ndarray, xx3: np.ndarray, xx2: np.ndarray, xx1: np.ndarray,
                    YY: np.ndarray, A6: np.ndarray, A5: np.ndarray, A4: np.ndarray, A3: np.ndarray, A2: np.ndarray, A1: np.ndarray) -> np.ndarray:
    """
    六维插值实现

    参数：
        xx6: 第六维网格向量
        xx5: 第五维网格向量
        xx4: 第四维网格向量
        xx3: 第三维网格向量
        xx2: 第二维网格向量
        xx1: 第一维网格向量
        YY: 六维值矩阵
        A6: 第六维查询点
        A5: 第五维查询点
        A4: 第四维查询点
        A3: 第三维查询点
        A2: 第二维查询点
        A1: 第一维查询点

    返回:
        插值结果，形状与A1相同
    """
    # 确保输入为numpy数组并保存原始形状
    A1 = np.asarray(A1)
    A2 = np.asarray(A2)
    A3 = np.asarray(A3)
    A4 = np.asarray(A4)
    A5 = np.asarray(A5)
    A6 = np.asarray(A6)
    original_shape = A1.shape

    # 重塑查询点为列向量
    Ars = np.column_stack([A1.flatten(), A2.flatten(), A3.flatten(), A4.flatten(), A5.flatten(), A6.flatten()])

    # 重塑网格向量为列向量
    xx = {}
    xx[1] = np.asarray(xx1).flatten().reshape(-1, 1)  # xx1 -> YY的第六个维度
    xx[2] = np.asarray(xx2).flatten().reshape(-1, 1)  # xx2 -> YY的第五个维度
    xx[3] = np.asarray(xx3).flatten().reshape(-1, 1)  # xx3 -> YY的第四个维度
    xx[4] = np.asarray(xx4).flatten().reshape(-1, 1)  # xx4 -> YY的第三个维度
    xx[5] = np.asarray(xx5).flatten().reshape(-1, 1)  # xx5 -> YY的第二个维度
    xx[6] = np.asarray(xx6).flatten().reshape(-1, 1)  # xx6 -> YY的第一个维度

    YY = np.asarray(YY)

    # 计算网格间距
    h = np.array([
        max(np.max(np.diff(xx[1].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[2].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[3].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[4].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[5].flatten())), np.finfo(float).eps),
        max(np.max(np.diff(xx[6].flatten())), np.finfo(float).eps)
    ])

    # 边界处理
    Ars[:, 0] = np.clip(Ars[:, 0], np.min(xx[1]), np.max(xx[1]))
    Ars[:, 1] = np.clip(Ars[:, 1], np.min(xx[2]), np.max(xx[2]))
    Ars[:, 2] = np.clip(Ars[:, 2], np.min(xx[3]), np.max(xx[3]))
    Ars[:, 3] = np.clip(Ars[:, 3], np.min(xx[4]), np.max(xx[4]))
    Ars[:, 4] = np.clip(Ars[:, 4], np.min(xx[5]), np.max(xx[5]))
    Ars[:, 5] = np.clip(Ars[:, 5], np.min(xx[6]), np.max(xx[6]))

    # 计算索引
    num_points = len(Ars)
    ind = np.zeros((num_points, 6, 2), dtype=int)

    # 下界和上界索引计算
    ind[:, 0, 0] = 1 + np.floor(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 0] = 1 + np.floor(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 0] = 1 + np.floor(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)
    ind[:, 3, 0] = 1 + np.floor(np.round((Ars[:, 3] - xx[4][0]) / h[3] * 1e8) * 1e-8).astype(int)
    ind[:, 4, 0] = 1 + np.floor(np.round((Ars[:, 4] - xx[5][0]) / h[4] * 1e8) * 1e-8).astype(int)
    ind[:, 5, 0] = 1 + np.floor(np.round((Ars[:, 5] - xx[6][0]) / h[5] * 1e8) * 1e-8).astype(int)

    ind[:, 0, 1] = 1 + np.ceil(np.round((Ars[:, 0] - xx[1][0]) / h[0] * 1e8) * 1e-8).astype(int)
    ind[:, 1, 1] = 1 + np.ceil(np.round((Ars[:, 1] - xx[2][0]) / h[1] * 1e8) * 1e-8).astype(int)
    ind[:, 2, 1] = 1 + np.ceil(np.round((Ars[:, 2] - xx[3][0]) / h[2] * 1e8) * 1e-8).astype(int)
    ind[:, 3, 1] = 1 + np.ceil(np.round((Ars[:, 3] - xx[4][0]) / h[3] * 1e8) * 1e-8).astype(int)
    ind[:, 4, 1] = 1 + np.ceil(np.round((Ars[:, 4] - xx[5][0]) / h[4] * 1e8) * 1e-8).astype(int)
    ind[:, 5, 1] = 1 + np.ceil(np.round((Ars[:, 5] - xx[6][0]) / h[5] * 1e8) * 1e-8).astype(int)

    # 确保索引在有效范围内
    ind[:, 0, :] = np.clip(ind[:, 0, :], 1, len(xx[1]))
    ind[:, 1, :] = np.clip(ind[:, 1, :], 1, len(xx[2]))
    ind[:, 2, :] = np.clip(ind[:, 2, :], 1, len(xx[3]))
    ind[:, 3, :] = np.clip(ind[:, 3, :], 1, len(xx[4]))
    ind[:, 4, :] = np.clip(ind[:, 4, :], 1, len(xx[5]))
    ind[:, 5, :] = np.clip(ind[:, 5, :], 1, len(xx[6]))

    # 提取64个超立方体顶点的值
    yy = np.zeros((num_points, 2, 2, 2, 2, 2, 2))

    # 第一组：第6维=下界的32个顶点
    yy[:, 0, 0, 0, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1,1,1) = YY[xx6下, xx5下, xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1,1,1) = YY[xx6下, xx5下, xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1,1,1) = YY[xx6下, xx5下, xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1,1,1) = YY[xx6下, xx5下, xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1,1,1) = YY[xx6下, xx5下, xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1,1,1) = YY[xx6下, xx5下, xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1,1,1) = YY[xx6下, xx5下, xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1,1,1) = YY[xx6下, xx5下, xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2,1,1) = YY[xx6下, xx5下, xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2,1,1) = YY[xx6下, xx5下, xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2,1,1) = YY[xx6下, xx5下, xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2,1,1) = YY[xx6下, xx5下, xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2,1,1) = YY[xx6下, xx5下, xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2,1,1) = YY[xx6下, xx5下, xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2,1,1) = YY[xx6下, xx5下, xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1, 0, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2,1,1) = YY[xx6下, xx5下, xx4上, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1,2,1) = YY[xx6下, xx5上, xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1,2,1) = YY[xx6下, xx5上, xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1,2,1) = YY[xx6下, xx5上, xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1,2,1) = YY[xx6下, xx5上, xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1,2,1) = YY[xx6下, xx5上, xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1,2,1) = YY[xx6下, xx5上, xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1,2,1) = YY[xx6下, xx5上, xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1,2,1) = YY[xx6下, xx5上, xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2,2,1) = YY[xx6下, xx5上, xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2,2,1) = YY[xx6下, xx5上, xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2,2,1) = YY[xx6下, xx5上, xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2,2,1) = YY[xx6下, xx5上, xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2,2,1) = YY[xx6下, xx5上, xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2,2,1) = YY[xx6下, xx5上, xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2,2,1) = YY[xx6下, xx5上, xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1, 1, 0] = YY[ind[:, 5, 0] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2,2,1) = YY[xx6下, xx5上, xx4上, xx3上, xx2上, xx1上]

    # 第二组：第6维=上界的32个顶点
    yy[:, 0, 0, 0, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1,1,2) = YY[xx6上, xx5下, xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1,1,2) = YY[xx6上, xx5下, xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1,1,2) = YY[xx6上, xx5下, xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1,1,2) = YY[xx6上, xx5下, xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1,1,2) = YY[xx6上, xx5下, xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1,1,2) = YY[xx6上, xx5下, xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1,1,2) = YY[xx6上, xx5下, xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1,1,2) = YY[xx6上, xx5下, xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2,1,2) = YY[xx6上, xx5下, xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2,1,2) = YY[xx6上, xx5下, xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2,1,2) = YY[xx6上, xx5下, xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2,1,2) = YY[xx6上, xx5下, xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2,1,2) = YY[xx6上, xx5下, xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2,1,2) = YY[xx6上, xx5下, xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2,1,2) = YY[xx6上, xx5下, xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1, 0, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 0] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2,1,2) = YY[xx6上, xx5下, xx4上, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,1,2,2) = YY[xx6上, xx5上, xx4下, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,1,2,2) = YY[xx6上, xx5上, xx4下, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,1,2,2) = YY[xx6上, xx5上, xx4下, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,1,2,2) = YY[xx6上, xx5上, xx4下, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,1,2,2) = YY[xx6上, xx5上, xx4下, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,1,2,2) = YY[xx6上, xx5上, xx4下, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,1,2,2) = YY[xx6上, xx5上, xx4下, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 0, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 0] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,1,2,2) = YY[xx6上, xx5上, xx4下, xx3上, xx2上, xx1上]
    yy[:, 0, 0, 0, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,1,2,2,2) = YY[xx6上, xx5上, xx4上, xx3下, xx2下, xx1下]
    yy[:, 1, 0, 0, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,1,2,2,2) = YY[xx6上, xx5上, xx4上, xx3下, xx2下, xx1上]
    yy[:, 0, 1, 0, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,1,2,2,2) = YY[xx6上, xx5上, xx4上, xx3下, xx2上, xx1下]
    yy[:, 1, 1, 0, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 0] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,1,2,2,2) = YY[xx6上, xx5上, xx4上, xx3下, xx2上, xx1上]
    yy[:, 0, 0, 1, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 0] - 1]  # yy(:,1,1,2,2,2,2) = YY[xx6上, xx5上, xx4上, xx3上, xx2下, xx1下]
    yy[:, 1, 0, 1, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 0] - 1, ind[:, 0, 1] - 1]  # yy(:,2,1,2,2,2,2) = YY[xx6上, xx5上, xx4上, xx3上, xx2下, xx1上]
    yy[:, 0, 1, 1, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 0] - 1]  # yy(:,1,2,2,2,2,2) = YY[xx6上, xx5上, xx4上, xx3上, xx2上, xx1下]
    yy[:, 1, 1, 1, 1, 1, 1] = YY[ind[:, 5, 1] - 1, ind[:, 4, 1] - 1, ind[:, 3, 1] - 1, ind[:, 2, 1] - 1, ind[:, 1, 1] - 1, ind[:, 0, 1] - 1]  # yy(:,2,2,2,2,2,2) = YY[xx6上, xx5上, xx4上, xx3上, xx2上, xx1上]

    # 计算插值权重
    da = np.zeros((num_points, 6))
    da[:, 0] = (Ars[:, 0] - xx[1][ind[:, 0, 0] - 1].flatten()) / h[0]
    da[:, 1] = (Ars[:, 1] - xx[2][ind[:, 1, 0] - 1].flatten()) / h[1]
    da[:, 2] = (Ars[:, 2] - xx[3][ind[:, 2, 0] - 1].flatten()) / h[2]
    da[:, 3] = (Ars[:, 3] - xx[4][ind[:, 3, 0] - 1].flatten()) / h[3]
    da[:, 4] = (Ars[:, 4] - xx[5][ind[:, 4, 0] - 1].flatten()) / h[4]
    da[:, 5] = (Ars[:, 5] - xx[6][ind[:, 5, 0] - 1].flatten()) / h[5]

    # 六线性插值
    # 第一步：沿第六维插值
    v5 = np.zeros((num_points, 2, 2, 2, 2, 2))
    v5[:, 0, 0, 0, 0, 0] = yy[:, 0, 0, 0, 0, 0, 0] + da[:, 5] * (yy[:, 0, 0, 0, 0, 0, 1] - yy[:, 0, 0, 0, 0, 0, 0])
    v5[:, 1, 0, 0, 0, 0] = yy[:, 1, 0, 0, 0, 0, 0] + da[:, 5] * (yy[:, 1, 0, 0, 0, 0, 1] - yy[:, 1, 0, 0, 0, 0, 0])
    v5[:, 0, 1, 0, 0, 0] = yy[:, 0, 1, 0, 0, 0, 0] + da[:, 5] * (yy[:, 0, 1, 0, 0, 0, 1] - yy[:, 0, 1, 0, 0, 0, 0])
    v5[:, 1, 1, 0, 0, 0] = yy[:, 1, 1, 0, 0, 0, 0] + da[:, 5] * (yy[:, 1, 1, 0, 0, 0, 1] - yy[:, 1, 1, 0, 0, 0, 0])
    v5[:, 0, 0, 1, 0, 0] = yy[:, 0, 0, 1, 0, 0, 0] + da[:, 5] * (yy[:, 0, 0, 1, 0, 0, 1] - yy[:, 0, 0, 1, 0, 0, 0])
    v5[:, 1, 0, 1, 0, 0] = yy[:, 1, 0, 1, 0, 0, 0] + da[:, 5] * (yy[:, 1, 0, 1, 0, 0, 1] - yy[:, 1, 0, 1, 0, 0, 0])
    v5[:, 0, 1, 1, 0, 0] = yy[:, 0, 1, 1, 0, 0, 0] + da[:, 5] * (yy[:, 0, 1, 1, 0, 0, 1] - yy[:, 0, 1, 1, 0, 0, 0])
    v5[:, 1, 1, 1, 0, 0] = yy[:, 1, 1, 1, 0, 0, 0] + da[:, 5] * (yy[:, 1, 1, 1, 0, 0, 1] - yy[:, 1, 1, 1, 0, 0, 0])
    v5[:, 0, 0, 0, 1, 0] = yy[:, 0, 0, 0, 1, 0, 0] + da[:, 5] * (yy[:, 0, 0, 0, 1, 0, 1] - yy[:, 0, 0, 0, 1, 0, 0])
    v5[:, 1, 0, 0, 1, 0] = yy[:, 1, 0, 0, 1, 0, 0] + da[:, 5] * (yy[:, 1, 0, 0, 1, 0, 1] - yy[:, 1, 0, 0, 1, 0, 0])
    v5[:, 0, 1, 0, 1, 0] = yy[:, 0, 1, 0, 1, 0, 0] + da[:, 5] * (yy[:, 0, 1, 0, 1, 0, 1] - yy[:, 0, 1, 0, 1, 0, 0])
    v5[:, 1, 1, 0, 1, 0] = yy[:, 1, 1, 0, 1, 0, 0] + da[:, 5] * (yy[:, 1, 1, 0, 1, 0, 1] - yy[:, 1, 1, 0, 1, 0, 0])
    v5[:, 0, 0, 1, 1, 0] = yy[:, 0, 0, 1, 1, 0, 0] + da[:, 5] * (yy[:, 0, 0, 1, 1, 0, 1] - yy[:, 0, 0, 1, 1, 0, 0])
    v5[:, 1, 0, 1, 1, 0] = yy[:, 1, 0, 1, 1, 0, 0] + da[:, 5] * (yy[:, 1, 0, 1, 1, 0, 1] - yy[:, 1, 0, 1, 1, 0, 0])
    v5[:, 0, 1, 1, 1, 0] = yy[:, 0, 1, 1, 1, 0, 0] + da[:, 5] * (yy[:, 0, 1, 1, 1, 0, 1] - yy[:, 0, 1, 1, 1, 0, 0])
    v5[:, 1, 1, 1, 1, 0] = yy[:, 1, 1, 1, 1, 0, 0] + da[:, 5] * (yy[:, 1, 1, 1, 1, 0, 1] - yy[:, 1, 1, 1, 1, 0, 0])
    v5[:, 0, 0, 0, 0, 1] = yy[:, 0, 0, 0, 0, 1, 0] + da[:, 5] * (yy[:, 0, 0, 0, 0, 1, 1] - yy[:, 0, 0, 0, 0, 1, 0])
    v5[:, 1, 0, 0, 0, 1] = yy[:, 1, 0, 0, 0, 1, 0] + da[:, 5] * (yy[:, 1, 0, 0, 0, 1, 1] - yy[:, 1, 0, 0, 0, 1, 0])
    v5[:, 0, 1, 0, 0, 1] = yy[:, 0, 1, 0, 0, 1, 0] + da[:, 5] * (yy[:, 0, 1, 0, 0, 1, 1] - yy[:, 0, 1, 0, 0, 1, 0])
    v5[:, 1, 1, 0, 0, 1] = yy[:, 1, 1, 0, 0, 1, 0] + da[:, 5] * (yy[:, 1, 1, 0, 0, 1, 1] - yy[:, 1, 1, 0, 0, 1, 0])
    v5[:, 0, 0, 1, 0, 1] = yy[:, 0, 0, 1, 0, 1, 0] + da[:, 5] * (yy[:, 0, 0, 1, 0, 1, 1] - yy[:, 0, 0, 1, 0, 1, 0])
    v5[:, 1, 0, 1, 0, 1] = yy[:, 1, 0, 1, 0, 1, 0] + da[:, 5] * (yy[:, 1, 0, 1, 0, 1, 1] - yy[:, 1, 0, 1, 0, 1, 0])
    v5[:, 0, 1, 1, 0, 1] = yy[:, 0, 1, 1, 0, 1, 0] + da[:, 5] * (yy[:, 0, 1, 1, 0, 1, 1] - yy[:, 0, 1, 1, 0, 1, 0])
    v5[:, 1, 1, 1, 0, 1] = yy[:, 1, 1, 1, 0, 1, 0] + da[:, 5] * (yy[:, 1, 1, 1, 0, 1, 1] - yy[:, 1, 1, 1, 0, 1, 0])
    v5[:, 0, 0, 0, 1, 1] = yy[:, 0, 0, 0, 1, 1, 0] + da[:, 5] * (yy[:, 0, 0, 0, 1, 1, 1] - yy[:, 0, 0, 0, 1, 1, 0])
    v5[:, 1, 0, 0, 1, 1] = yy[:, 1, 0, 0, 1, 1, 0] + da[:, 5] * (yy[:, 1, 0, 0, 1, 1, 1] - yy[:, 1, 0, 0, 1, 1, 0])
    v5[:, 0, 1, 0, 1, 1] = yy[:, 0, 1, 0, 1, 1, 0] + da[:, 5] * (yy[:, 0, 1, 0, 1, 1, 1] - yy[:, 0, 1, 0, 1, 1, 0])
    v5[:, 1, 1, 0, 1, 1] = yy[:, 1, 1, 0, 1, 1, 0] + da[:, 5] * (yy[:, 1, 1, 0, 1, 1, 1] - yy[:, 1, 1, 0, 1, 1, 0])
    v5[:, 0, 0, 1, 1, 1] = yy[:, 0, 0, 1, 1, 1, 0] + da[:, 5] * (yy[:, 0, 0, 1, 1, 1, 1] - yy[:, 0, 0, 1, 1, 1, 0])
    v5[:, 1, 0, 1, 1, 1] = yy[:, 1, 0, 1, 1, 1, 0] + da[:, 5] * (yy[:, 1, 0, 1, 1, 1, 1] - yy[:, 1, 0, 1, 1, 1, 0])
    v5[:, 0, 1, 1, 1, 1] = yy[:, 0, 1, 1, 1, 1, 0] + da[:, 5] * (yy[:, 0, 1, 1, 1, 1, 1] - yy[:, 0, 1, 1, 1, 1, 0])
    v5[:, 1, 1, 1, 1, 1] = yy[:, 1, 1, 1, 1, 1, 0] + da[:, 5] * (yy[:, 1, 1, 1, 1, 1, 1] - yy[:, 1, 1, 1, 1, 1, 0])

    # 第二步：沿第五维插值
    v4 = np.zeros((num_points, 2, 2, 2, 2))
    v4[:, 0, 0, 0, 0] = v5[:, 0, 0, 0, 0, 0] + da[:, 4] * (v5[:, 0, 0, 0, 0, 1] - v5[:, 0, 0, 0, 0, 0])
    v4[:, 1, 0, 0, 0] = v5[:, 1, 0, 0, 0, 0] + da[:, 4] * (v5[:, 1, 0, 0, 0, 1] - v5[:, 1, 0, 0, 0, 0])
    v4[:, 0, 1, 0, 0] = v5[:, 0, 1, 0, 0, 0] + da[:, 4] * (v5[:, 0, 1, 0, 0, 1] - v5[:, 0, 1, 0, 0, 0])
    v4[:, 1, 1, 0, 0] = v5[:, 1, 1, 0, 0, 0] + da[:, 4] * (v5[:, 1, 1, 0, 0, 1] - v5[:, 1, 1, 0, 0, 0])
    v4[:, 0, 0, 1, 0] = v5[:, 0, 0, 1, 0, 0] + da[:, 4] * (v5[:, 0, 0, 1, 0, 1] - v5[:, 0, 0, 1, 0, 0])
    v4[:, 1, 0, 1, 0] = v5[:, 1, 0, 1, 0, 0] + da[:, 4] * (v5[:, 1, 0, 1, 0, 1] - v5[:, 1, 0, 1, 0, 0])
    v4[:, 0, 1, 1, 0] = v5[:, 0, 1, 1, 0, 0] + da[:, 4] * (v5[:, 0, 1, 1, 0, 1] - v5[:, 0, 1, 1, 0, 0])
    v4[:, 1, 1, 1, 0] = v5[:, 1, 1, 1, 0, 0] + da[:, 4] * (v5[:, 1, 1, 1, 0, 1] - v5[:, 1, 1, 1, 0, 0])
    v4[:, 0, 0, 0, 1] = v5[:, 0, 0, 0, 1, 0] + da[:, 4] * (v5[:, 0, 0, 0, 1, 1] - v5[:, 0, 0, 0, 1, 0])
    v4[:, 1, 0, 0, 1] = v5[:, 1, 0, 0, 1, 0] + da[:, 4] * (v5[:, 1, 0, 0, 1, 1] - v5[:, 1, 0, 0, 1, 0])
    v4[:, 0, 1, 0, 1] = v5[:, 0, 1, 0, 1, 0] + da[:, 4] * (v5[:, 0, 1, 0, 1, 1] - v5[:, 0, 1, 0, 1, 0])
    v4[:, 1, 1, 0, 1] = v5[:, 1, 1, 0, 1, 0] + da[:, 4] * (v5[:, 1, 1, 0, 1, 1] - v5[:, 1, 1, 0, 1, 0])
    v4[:, 0, 0, 1, 1] = v5[:, 0, 0, 1, 1, 0] + da[:, 4] * (v5[:, 0, 0, 1, 1, 1] - v5[:, 0, 0, 1, 1, 0])
    v4[:, 1, 0, 1, 1] = v5[:, 1, 0, 1, 1, 0] + da[:, 4] * (v5[:, 1, 0, 1, 1, 1] - v5[:, 1, 0, 1, 1, 0])
    v4[:, 0, 1, 1, 1] = v5[:, 0, 1, 1, 1, 0] + da[:, 4] * (v5[:, 0, 1, 1, 1, 1] - v5[:, 0, 1, 1, 1, 0])
    v4[:, 1, 1, 1, 1] = v5[:, 1, 1, 1, 1, 0] + da[:, 4] * (v5[:, 1, 1, 1, 1, 1] - v5[:, 1, 1, 1, 1, 0])

    # 第三步：沿第四维插值
    v3 = np.zeros((num_points, 2, 2, 2))
    v3[:, 0, 0, 0] = v4[:, 0, 0, 0, 0] + da[:, 3] * (v4[:, 0, 0, 0, 1] - v4[:, 0, 0, 0, 0])
    v3[:, 1, 0, 0] = v4[:, 1, 0, 0, 0] + da[:, 3] * (v4[:, 1, 0, 0, 1] - v4[:, 1, 0, 0, 0])
    v3[:, 0, 1, 0] = v4[:, 0, 1, 0, 0] + da[:, 3] * (v4[:, 0, 1, 0, 1] - v4[:, 0, 1, 0, 0])
    v3[:, 1, 1, 0] = v4[:, 1, 1, 0, 0] + da[:, 3] * (v4[:, 1, 1, 0, 1] - v4[:, 1, 1, 0, 0])
    v3[:, 0, 0, 1] = v4[:, 0, 0, 1, 0] + da[:, 3] * (v4[:, 0, 0, 1, 1] - v4[:, 0, 0, 1, 0])
    v3[:, 1, 0, 1] = v4[:, 1, 0, 1, 0] + da[:, 3] * (v4[:, 1, 0, 1, 1] - v4[:, 1, 0, 1, 0])
    v3[:, 0, 1, 1] = v4[:, 0, 1, 1, 0] + da[:, 3] * (v4[:, 0, 1, 1, 1] - v4[:, 0, 1, 1, 0])
    v3[:, 1, 1, 1] = v4[:, 1, 1, 1, 0] + da[:, 3] * (v4[:, 1, 1, 1, 1] - v4[:, 1, 1, 1, 0])

    # 第四步：沿第三维插值
    v2 = np.zeros((num_points, 2, 2))
    v2[:, 0, 0] = v3[:, 0, 0, 0] + da[:, 2] * (v3[:, 0, 0, 1] - v3[:, 0, 0, 0])
    v2[:, 1, 0] = v3[:, 1, 0, 0] + da[:, 2] * (v3[:, 1, 0, 1] - v3[:, 1, 0, 0])
    v2[:, 0, 1] = v3[:, 0, 1, 0] + da[:, 2] * (v3[:, 0, 1, 1] - v3[:, 0, 1, 0])
    v2[:, 1, 1] = v3[:, 1, 1, 0] + da[:, 2] * (v3[:, 1, 1, 1] - v3[:, 1, 1, 0])

    # 第五步：沿第二维插值
    v1 = np.zeros((num_points, 2))
    v1[:, 0] = v2[:, 0, 0] + da[:, 1] * (v2[:, 0, 1] - v2[:, 0, 0])
    v1[:, 1] = v2[:, 1, 0] + da[:, 1] * (v2[:, 1, 1] - v2[:, 1, 0])

    # 第六步：沿第一维插值
    yi = da[:, 0] * (v1[:, 1] - v1[:, 0]) + v1[:, 0]

    # 重塑为原始形状
    return yi.reshape(original_shape)


def dpm_mergestruct(S1: Union[Dict, object], S2: Union[Dict, object]) -> Union[Dict, object]:
    """
    合并两个结构体

    参数:
        S1: 第一个结构体（字典或对象）
        S2: 第二个结构体（字典或对象）

    返回:
        S: 合并后的结构体

    警告:
        可能导致内存问题！
    """
    try:
        if isinstance(S1, dict):
            S = S1.copy()
            names = list(S1.keys())
        else:
            S = copy.deepcopy(S1)
            names = [attr for attr in dir(S1) if not attr.startswith('_') and not callable(getattr(S1, attr))]

        for name in names:
            if isinstance(S1, dict):
                s1_val = S1[name]
                if name not in S2:
                    continue
                s2_val = S2[name]
            else:
                s1_val = getattr(S1, name)
                if not hasattr(S2, name):
                    continue
                s2_val = getattr(S2, name)

            # 根据字段类型进行不同的合并操作
            if isinstance(s1_val, dict) and isinstance(s2_val, dict):
                # 结构体，递归合并
                merged_val = dpm_mergestruct(s1_val, s2_val)
                if isinstance(S, dict):
                    S[name] = merged_val
                else:
                    setattr(S, name, merged_val)

            elif isinstance(s1_val, (list, tuple)) and isinstance(s2_val, (list, tuple)):
                # 如果是cell数组，对每个元素进行水平连接
                merged_list = []
                max_len = max(len(s1_val), len(s2_val))

                for j in range(max_len):
                    if j < len(s1_val) and j < len(s2_val):
                        if isinstance(s1_val[j], np.ndarray) and isinstance(s2_val[j], np.ndarray):
                            merged_list.append(np.concatenate([s1_val[j], s2_val[j]]))
                        elif np.isscalar(s1_val[j]) and np.isscalar(s2_val[j]):
                            merged_list.append(np.array([s1_val[j], s2_val[j]]))
                        else:
                            # 尝试水平连接
                            try:
                                merged_list.append(np.concatenate([np.atleast_1d(s1_val[j]), np.atleast_1d(s2_val[j])]))
                            except:
                                merged_list.append([s1_val[j], s2_val[j]])
                    elif j < len(s1_val):
                        merged_list.append(s1_val[j])
                    else:
                        merged_list.append(s2_val[j])

                if isinstance(S, dict):
                    S[name] = merged_list
                else:
                    setattr(S, name, merged_list)

            elif (isinstance(s1_val, (int, float, complex, bool, np.number)) or isinstance(s1_val, np.ndarray)) and \
                 (isinstance(s2_val, (int, float, complex, bool, np.number)) or isinstance(s2_val, np.ndarray)):
                # (elseif isnumeric || islogical)
                # S.(names{i}) = [S1.(names{i}) S2.(names{i})]
                try:
                    if np.isscalar(s1_val) and np.isscalar(s2_val):
                        merged_val = np.array([s1_val, s2_val])
                    else:
                        merged_val = np.concatenate([np.atleast_1d(s1_val), np.atleast_1d(s2_val)])

                    if isinstance(S, dict):
                        S[name] = merged_val
                    else:
                        setattr(S, name, merged_val)
                except:
                    if isinstance(S, dict):
                        S[name] = s2_val
                    else:
                        setattr(S, name, s2_val)
            else:
                if isinstance(S, dict):
                    S[name] = s2_val
                else:
                    setattr(S, name, s2_val)

        return S

    except Exception as e:
        raise ValueError('mergestruct: S1 and S2 have different structures.')


def dpm_get_empty_inp(grd: Grid, dis: Problem, options: str = 'zero') -> Input:
    """
    获取空的输入结构

    参数:
        grd: 网格结构，包含状态和输入的网格定义
        dis: 问题结构，包含扰动和时间步长信息
        options: 填充选项

    返回:
        inp: 输入结构，包含X（状态）、U（输入）、W（扰动）和Ts（时间步长）
    """
    # 确定填充值
    if options == 'nan':
        value = np.nan
    elif options == 'zero':
        value = 0.0
    elif options == 'inf':
        value = np.inf
    else:
        value = 0.0

    # 创建输入结构
    inp = Input()

    # 初始化状态 X{i}
    for i in range(1, len(grd.Nx) + 1):
        inp.X[i] = value

    # 初始化输入 U{i}
    for i in range(1, len(grd.Nu) + 1):
        inp.U[i] = value

    # 初始化扰动 W{i}
    for i in range(1, len(dis.W) + 1):
        inp.W[i] = value

    # 设置时间步长
    inp.Ts = dis.Ts

    return inp


def dpm_get_empty_out(model: Union[str, Callable], inp: Input, par: Any,
                      grd: Grid, options: str = 'nan') -> Output:
    """
    获取空的输出结构

    参数:
        model: 模型函数（字符串名称或可调用对象）
        inp: 输入结构，用于调用模型函数
        par: 参数结构，传递给模型函数
        grd: 网格结构（未使用，保持接口兼容）
        options: 填充选项

    返回:
        out: 输出结构，包含X（状态）、C（代价）、I（不可行性）等字段
    """
    if not options:
        options = 'nan'

    try:
        if callable(model):
            X, C, I, out = model(inp, par)
        elif isinstance(model, str):
            raise NotImplementedError("字符串模型函数需要进一步实现")
        else:
            raise ValueError("无效的模型函数类型")

        # 创建 Output 对象
        output = Output()
        output.X = X
        output.C = C
        output.I = I

        # 如果模型返回了额外的输出字典，将其存储为属性
        if out is not None and isinstance(out, dict):
            for key, value in out.items():
                setattr(output, key, value)

        output = dpm_setallfield(output, options)

        return output

    except Exception as e:
        warnings.warn(f"dpm_get_empty_out: 模型调用失败，使用默认结构 - {str(e)}")

        out = Output()
        out.X = {1: 0.0}
        out.C = {1: 0.0}
        out.I = np.array([0])

        # 应用填充选项
        out = dpm_setallfield(out, options)

        return out


def dpm_setallfield(S1: Union[Dict, object], options: str) -> Union[Dict, object]:
    """
    设置结构体中所有字段的值

    """
    try:
        if options == 'nan':
            value = np.nan
        elif options == 'zero':
            value = 0.0
        elif options == 'inf':
            value = np.inf
        else:
            raise ValueError(f"不支持的选项: {options}")

        # 复制输入结构
        S = S1.copy() if isinstance(S1, dict) else copy.deepcopy(S1)

        # 获取字段名
        if isinstance(S1, dict):
            field_names = list(S1.keys())
        else:
            # 对象，获取非私有属性
            field_names = [attr for attr in dir(S1)
                          if not attr.startswith('_') and not callable(getattr(S1, attr))]

        # 遍历每个字段
        for name in field_names:
            if isinstance(S1, dict):
                field_val = S1[name]
            else:
                field_val = getattr(S1, name)

            # 检查字段类型并处理
            if isinstance(field_val, dict) or (hasattr(field_val, '__dict__') and not isinstance(field_val, (list, tuple, np.ndarray))):
                # 结构体，递归调用
                new_val = dpm_setallfield(field_val, options)
                if isinstance(S, dict):
                    S[name] = new_val
                else:
                    setattr(S, name, new_val)

            elif isinstance(field_val, (list, tuple)):
                # 如果是cell数组，设置所有元素
                if isinstance(field_val, list):
                    new_val = [value for _ in field_val]
                else:
                    new_val = tuple(value for _ in field_val)
                if isinstance(S, dict):
                    S[name] = new_val
                else:
                    setattr(S, name, new_val)

            elif isinstance(field_val, (int, float, complex, bool, np.number)) or isinstance(field_val, np.ndarray):
                # 如果是数值或逻辑类型 
                if isinstance(field_val, np.ndarray):
                    new_val = np.full_like(field_val, value, dtype=float)
                else:
                    new_val = value
                if isinstance(S, dict):
                    S[name] = new_val
                else:
                    setattr(S, name, new_val)
            else:
                # 其他类型保持不变
                if isinstance(S, dict):
                    S[name] = field_val
                else:
                    setattr(S, name, field_val)

        return S

    except Exception as e:
        raise ValueError('dpm_setallfield: S1 and S2 have different structures.')


def dpm_model_inv(inp: Input, par: dict) -> tuple:
    """
    反演模型函数，用于边界线计算

    参数:
        inp: 输入结构，包含状态和输入信息
        par: 参数字典，包含模型函数和选项

    返回:
        X: 状态更新字典
        C: 代价字典
        I: 不可行性标志
        out: 输出结构（空列表）

    """
    inpo = copy.deepcopy(inp)
    iterations = 0
    dSOC = np.inf

    options = par.get('options', {})
    tolerance = getattr(options, 'Tol', 1e-6) if hasattr(options, 'Tol') else options.get('Tol', 1e-6)
    max_iter = getattr(options, 'Iter', 100) if hasattr(options, 'Iter') else options.get('Iter', 100)

    while (iterations == 0 or np.max(np.abs(np.array(dSOC).flatten())) > tolerance) and iterations < max_iter:
        # [X C I] = feval(par.model,inp,par)
        model_func = par.get('model')
        if callable(model_func):
            result = model_func(inp, par)
            if len(result) == 3:
                X, C, I = result
            elif len(result) == 4:
                X, C, I, out = result
            else:
                raise ValueError(f"模型函数返回了错误数量的值: {len(result)}")
        elif isinstance(model_func, str):
            # 如果是字符串，使用feval等价操作
            raise NotImplementedError("字符串模型函数需要进一步实现")
        else:
            raise ValueError("无效的模型函数")

        # 计算状态变化量 (dSOC = X{1} - inpo.X{1})
        dSOC = np.asarray(X[1]) - np.asarray(inpo.X[1])

        # 更新输入状态 (inp.X{1} = inp.X{1} - dSOC)
        inp.X[1] = np.asarray(inp.X[1]) - dSOC

        iterations += 1

    # X = inp.X
    X = inp.X.copy()

    # 修改代价函数 (C{2} = C{1}; C{1} = (X{1}-inpo.X{1}))
    C_new = {}
    C_new[2] = C[1]  # 保存原始代价
    C_new[1] = np.asarray(X[1]) - np.asarray(inpo.X[1])  # 新代价为状态变化量

    out = []

    return X, C_new, I, out


# 暂未引用
def dpm_code(s: str, v: List[int], m: str = '') -> str:
    """
    生成代码字符串，用于动态创建变量访问代码

    参数:
        s: 模板字符串，包含'#'作为占位符
        v: 索引值列表
        m: 分隔符字符串（可选）

    返回:
        str: 生成的代码字符串

    示例:
        dpm_code('inp.U{#}', [1, 2, 3], ',')
        返回: 'inp.U{1},inp.U{2},inp.U{3}'
    """
    if not m:
        m = ''

    # 将v重塑为一维列表
    v = np.asarray(v).flatten().tolist()

    # 为每个值生成对应的字符串
    str_parts = []
    for val in v:
        # 为每个值单独替换'#'
        current_str = s.replace('#', str(val))
        str_parts.append(current_str)

    # 用分隔符连接所有部分
    return m.join(str_parts)


# 暂未引用
def dpm_findu(A: np.ndarray, vec: Union[float, np.ndarray]) -> Union[int, np.ndarray]:
    """
    在等间距数组A中找到vec的上界索引

    参数:
        A: 等间距数组
        vec: 要查找的值或值数组

    返回:
        in: 上界索引

    """
    A = np.asarray(A)
    vec = np.asarray(vec)

    # 计算间距
    da = A[1] - A[0]

    # 计算上界索引（1基索引）
    in_val = 1 + np.ceil((vec - A[0]) / da)

    # 限制在有效范围内
    in_val = np.maximum(in_val, 1)
    in_val = np.minimum(in_val, len(A))

    # 转换为整数
    in_val = in_val.astype(int)

    # 如果输入是标量，返回标量
    if np.isscalar(vec):
        return int(in_val)
    else:
        return in_val


# 暂未引用
def dpm_findl(A: np.ndarray, vec: Union[float, np.ndarray]) -> Union[int, np.ndarray]:
    """
    在等间距数组A中找到vec的下界索引

    参数:
        A: 等间距数组
        vec: 要查找的值或值数组

    返回:
        in: 下界索引

    """
    A = np.asarray(A)
    vec = np.asarray(vec)

    # 计算间距
    da = A[1] - A[0]

    # 计算下界索引（1基索引）
    in_val = 1 + np.floor((vec - A[0]) / da)

    # 限制在有效范围内
    in_val = np.maximum(in_val, 1)
    in_val = np.minimum(in_val, len(A))

    # 转换为整数
    in_val = in_val.astype(int)

    # 如果输入是标量，返回标量
    if np.isscalar(vec):
        return int(in_val)
    else:
        return in_val


# 暂未引用
def dpm_sub2indr(sze: List[int], vl: np.ndarray, vu: np.ndarray, dim: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    将下标转换为范围线性索引

    参数:
        sze: 矩阵大小列表
        vl: 下界索引数组
        vu: 上界索引数组
        dim: 维度索引

    返回:
        ind: 线性索引数组
        col: 列索引数组
    """
    sze = np.asarray(sze)
    vl = np.asarray(vl).flatten()
    vu = np.asarray(vu).flatten()

    # ind0 = [0:sze(dim)-1].*sze(1)
    ind0 = np.arange(sze[dim-1]) * sze[0]

    # ind = reshape([(vl+ind0); (vu+ind0)],1,numel(ind0)*2)
    # 将vl+ind0和vu+ind0垂直堆叠后重塑为行向量
    vl_plus_ind0 = vl[:, np.newaxis] + ind0[np.newaxis, :]  # 广播相加
    vu_plus_ind0 = vu[:, np.newaxis] + ind0[np.newaxis, :]  # 广播相加

    # 垂直堆叠 [(vl+ind0); (vu+ind0)]，然后重塑
    stacked = np.vstack([vl_plus_ind0, vu_plus_ind0])  # shape: (2*len(vl), len(ind0))
    ind_pairs = stacked.reshape(1, -1, order='F').flatten()  # 使用Fortran顺序重塑

    # indstr = num2str(ind');
    # ind = eval(['[' reshape([indstr repmat(': ',1,length(vl))']',1,numel([indstr repmat(': ',1,length(vl))']')) ']']);
    # 这实际上是生成形如 [start1:end1 start2:end2 ...] 的范围
    # (MATLAB语法错误：eval 试图执行 [1:1 2:3 ...]，但缺少逗号分隔，实际语法应为 [1:1, 2:3, ...])
    indices = []
    for i in range(0, len(ind_pairs), 2):
        start_idx = int(ind_pairs[i])
        end_idx = int(ind_pairs[i+1])
        indices.extend(range(start_idx, end_idx + 1))

    ind = np.array(indices)

    # col = ceil((ind)/sze(1))
    col = np.ceil(ind / sze[0]).astype(int)

    return ind, col


def dpm_sub2ind(siz: Tuple[int, ...], *indices) -> np.ndarray:
    """
    将多维下标转换为线性索引

    参数:
        siz: 数组大小元组
        *indices: 各维度的索引数组

    返回:
        ndx: 线性索引数组

    """
    siz = np.array(siz, dtype=float)

    # 调整输入维度
    if len(siz) != len(indices):
        if len(siz) < len(indices):
            # 为尾随单例维度调整
            siz = np.concatenate([siz, np.ones(len(indices) - len(siz))])
        else:
            # 为最后元素的线性索引调整
            if len(indices) >= 2:
                siz = np.concatenate([siz[:len(indices)-1], [np.prod(siz[len(indices)-1:])]])
            else:
                siz = siz[:len(indices)]

    # 计算线性索引
    k = np.concatenate([[1], np.cumprod(siz[:-1])])
    ndx = np.ones_like(indices[0], dtype=float)

    for i in range(len(siz)):
        v = np.asarray(indices[i])
        ndx = ndx + (v - 1) * k[i]

    return ndx.astype(int)


def dpm_sizecmp(arr1: np.ndarray, arr2: np.ndarray) -> bool:
    """
    比较两个数组的大小是否相同（MATLAB兼容版本）

    在MATLAB中，标量的size是[1,1]，但在Python中标量的shape是()
    这个函数模拟MATLAB的size比较行为

    参数:
        arr1: 第一个数组
        arr2: 第二个数组

    返回:
        大小是否相同
    """
    # 转换为numpy数组以确保有shape属性
    arr1 = np.asarray(arr1)
    arr2 = np.asarray(arr2)

    def matlab_size(arr):
        """获取MATLAB风格的size"""
        if arr.ndim == 0:  # 标量
            return (1, 1)
        elif arr.ndim == 1:  # 一维数组
            return (1, arr.shape[0]) if arr.shape[0] > 1 else (1, 1)
        else:  # 多维数组
            return arr.shape

    size1 = matlab_size(arr1)
    size2 = matlab_size(arr2)

    return size1 == size2


def get_size(current_grd: Dict) -> List[int]:
    """
    获取当前网格的大小

    """
    sze = []

    # 检查是否有状态网格
    if 'X' not in current_grd:
        return sze

    for i in range(1, len(current_grd['X']) + 1):
        if i in current_grd['X']:
            # sze = [sze length(current_grd.X{i})]
            sze.append(len(current_grd['X'][i]))

    if len(sze) == 1:
        sze.append(1)

    return sze


def input_check_grd(grd: Grid, T: int) -> Grid:
    """
    检查并修正网格结构，确保所有数组都具有正确的长度。

    参数:
        grd: Grid对象
        T: 时间步数

    返回:
        修正后的Grid对象

    """

    # 检查T必须大于0
    if T < 1:
        raise ValueError('DPM:Internal - prb.N must be greater than 0')

    # 检查状态变量
    if hasattr(grd, 'Nx') and grd.Nx:
        for i in grd.Nx.keys():
            # 检查grd.Nx{i}必须大于等于1
            # 检查grd.Nx[i]的值
            if isinstance(grd.Nx[i], list):
                if any(nx < 1 for nx in grd.Nx[i]):
                    raise ValueError(f'DPM:Internal - grd.Nx[{i}] must be equal or greater than 1')
            elif hasattr(grd.Nx[i], '__iter__'):
                # 处理numpy数组或其他可迭代对象
                if any(nx < 1 for nx in grd.Nx[i]):
                    raise ValueError(f'DPM:Internal - grd.Nx[{i}] must be equal or greater than 1')
            else:
                # 标量情况
                if grd.Nx[i] < 1:
                    raise ValueError(f'DPM:Internal - grd.Nx[{i}] must be equal or greater than 1')

            # 检查并修正grd.Xn{i}.lo 
            if hasattr(grd, 'Xn') and grd.Xn and i in grd.Xn and 'lo' in grd.Xn[i]:
                if isinstance(grd.Xn[i]['lo'], list):
                    if len(grd.Xn[i]['lo']) == 1:
                        # repmat逻辑：复制标量到T+1个元素
                        grd.Xn[i]['lo'] = grd.Xn[i]['lo'] * (T + 1)
                    elif len(grd.Xn[i]['lo']) != T + 1:
                        raise ValueError(f'DPM:Internal - grd.Xn[{i}].lo must be a scalar OR have the same length as the problem')
                else:
                    # 标量情况，转换为列表并复制
                    grd.Xn[i]['lo'] = [grd.Xn[i]['lo']] * (T + 1)

            # 检查并修正grd.Xn{i}.hi
            if hasattr(grd, 'Xn') and grd.Xn and i in grd.Xn and 'hi' in grd.Xn[i]:
                if isinstance(grd.Xn[i]['hi'], list):
                    if len(grd.Xn[i]['hi']) == 1:
                        # repmat逻辑：复制标量到T+1个元素
                        grd.Xn[i]['hi'] = grd.Xn[i]['hi'] * (T + 1)
                    elif len(grd.Xn[i]['hi']) != T + 1:
                        raise ValueError(f'DPM:Internal - grd.Xn[{i}].hi must be a scalar OR have the same length as the problem')
                else:
                    # 标量情况，转换为列表并复制
                    grd.Xn[i]['hi'] = [grd.Xn[i]['hi']] * (T + 1)

            # 检查并修正grd.Nx{i}
            if isinstance(grd.Nx[i], list):
                if len(grd.Nx[i]) == 1:
                    # repmat逻辑：复制标量到T+1个元素
                    grd.Nx[i] = grd.Nx[i] * (T + 1)
                elif len(grd.Nx[i]) != T + 1:
                    raise ValueError(f'DPM:Internal - grd.Nx[{i}] must be a scalar OR have the same length as the problem')
            else:
                # 标量情况，转换为列表并复制
                grd.Nx[i] = [grd.Nx[i]] * (T + 1)

    # 检查控制变量
    if hasattr(grd, 'Nu') and grd.Nu:
        for i in grd.Nu.keys():
            # 检查grd.Nu{i}必须大于等于1
            if isinstance(grd.Nu[i], list):
                if any(nu < 1 for nu in grd.Nu[i]):
                    raise ValueError(f'DPM:Internal - grd.Nu[{i}] must be equal or greater than 1')
            else:
                if grd.Nu[i] < 1:
                    raise ValueError(f'DPM:Internal - grd.Nu[{i}] must be equal or greater than 1')

            # 检查并修正grd.Un{i}.lo
            if hasattr(grd, 'Un') and grd.Un and i in grd.Un and 'lo' in grd.Un[i]:
                if isinstance(grd.Un[i]['lo'], list):
                    if len(grd.Un[i]['lo']) == 1:
                        # repmat逻辑：复制标量到T个元素
                        grd.Un[i]['lo'] = grd.Un[i]['lo'] * T
                    elif len(grd.Un[i]['lo']) != T:
                        raise ValueError(f'DPM:Internal - grd.Un[{i}].lo must be a scalar OR have the same length as the problem')
                else:
                    # 标量情况，转换为列表并复制
                    grd.Un[i]['lo'] = [grd.Un[i]['lo']] * T

            # 检查并修正grd.Un{i}.hi
            if hasattr(grd, 'Un') and grd.Un and i in grd.Un and 'hi' in grd.Un[i]:
                if isinstance(grd.Un[i]['hi'], list):
                    if len(grd.Un[i]['hi']) == 1:
                        # repmat逻辑：复制标量到T个元素
                        grd.Un[i]['hi'] = grd.Un[i]['hi'] * T
                    elif len(grd.Un[i]['hi']) != T:
                        raise ValueError(f'DPM:Internal - grd.Un[{i}].hi must be a scalar OR have the same length as the problem')
                else:
                    # 标量情况，转换为列表并复制
                    grd.Un[i]['hi'] = [grd.Un[i]['hi']] * T

            # 检查并修正grd.Nu{i}
            if isinstance(grd.Nu[i], list):
                if len(grd.Nu[i]) == 1:
                    # repmat逻辑：复制标量到T个元素
                    grd.Nu[i] = grd.Nu[i] * T
                elif len(grd.Nu[i]) != T:
                    raise ValueError(f'DPM:Internal - grd.Nu[{i}] must be a scalar OR have the same length as the problem')
            else:
                # 标量情况，转换为列表并复制
                grd.Nu[i] = [grd.Nu[i]] * T

    return grd


def notify_user_of_error(err: Exception):
    """
    通知用户错误信息

    """
    print("=" * 60)
    print("DPM 错误通知")
    print("=" * 60)

    # 显示错误类型和消息
    print(f"错误类型: {type(err).__name__}")
    print(f"错误消息: {str(err)}")

    # 显示详细的堆栈跟踪
    print("\n详细错误信息:")
    print("-" * 40)
    import traceback
    traceback.print_exc()


def clear_waitbars():
    """
    清除等待条

    """
    try:
        print()
    except:
        pass


if __name__ == "__main__":

    print("=" * 50)
    print("DPM Python版本")
    print("=" * 50)
