#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 DPM 结构体操作函数的脚本

这个脚本快速验证结构体操作函数的基本功能
"""

import numpy as np
from dpm import dpm_mergestruct, dpm_get_empty_inp, Grid, Problem


def main():
    """主验证函数"""
    print("快速验证 DPM 结构体操作函数")
    print("=" * 40)
    
    try:
        # 测试 dpm_mergestruct
        print("1. 测试 dpm_mergestruct 函数:")
        S1 = {
            'a': 1.0,
            'b': np.array([1, 2]),
            'c': {'x': 10}
        }
        
        S2 = {
            'a': 2.0,
            'b': np.array([3, 4]),
            'c': {'x': 20}
        }
        
        merged = dpm_mergestruct(S1, S2)
        print(f"   S1: {S1}")
        print(f"   S2: {S2}")
        print(f"   合并: {merged}")
        
        # 验证合并结果
        expected_a = [1.0, 2.0]
        expected_b = [1, 2, 3, 4]
        a_correct = np.allclose(merged['a'], expected_a)
        b_correct = np.allclose(merged['b'], expected_b)
        
        print(f"   ✓ 字段'a'正确" if a_correct else f"   ✗ 字段'a'错误")
        print(f"   ✓ 字段'b'正确" if b_correct else f"   ✗ 字段'b'错误")
        
        # 测试 dpm_get_empty_inp
        print("\n2. 测试 dpm_get_empty_inp 函数:")
        grd = Grid()
        grd.Nx = {1: [10], 2: [5]}  # 两个状态
        grd.Nu = {1: [3]}           # 一个输入
        
        dis = Problem(Ts=0.1, N=10)  # 正确初始化Problem
        dis.W = {1: [2]}            # 一个扰动
        
        # 零填充测试
        inp_zero = dpm_get_empty_inp(grd, dis, 'zero')
        print(f"   零填充结果:")
        print(f"     X: {inp_zero.X}")
        print(f"     U: {inp_zero.U}")
        print(f"     W: {inp_zero.W}")
        print(f"     Ts: {inp_zero.Ts}")
        
        # 验证
        x_correct = inp_zero.X[1] == 0.0 and inp_zero.X[2] == 0.0
        u_correct = inp_zero.U[1] == 0.0
        w_correct = inp_zero.W[1] == 0.0
        ts_correct = inp_zero.Ts == 0.1
        
        print(f"   ✓ 状态正确" if x_correct else f"   ✗ 状态错误")
        print(f"   ✓ 输入正确" if u_correct else f"   ✗ 输入错误")
        print(f"   ✓ 扰动正确" if w_correct else f"   ✗ 扰动错误")
        print(f"   ✓ 时间步长正确" if ts_correct else f"   ✗ 时间步长错误")
        
        # NaN填充测试
        inp_nan = dpm_get_empty_inp(grd, dis, 'nan')
        print(f"\n   NaN填充结果:")
        print(f"     X: {inp_nan.X}")
        print(f"     U: {inp_nan.U}")
        print(f"     W: {inp_nan.W}")
        
        # 验证NaN
        x_nan_correct = np.isnan(inp_nan.X[1]) and np.isnan(inp_nan.X[2])
        u_nan_correct = np.isnan(inp_nan.U[1])
        w_nan_correct = np.isnan(inp_nan.W[1])
        
        print(f"   ✓ 状态NaN正确" if x_nan_correct else f"   ✗ 状态NaN错误")
        print(f"   ✓ 输入NaN正确" if u_nan_correct else f"   ✗ 输入NaN错误")
        print(f"   ✓ 扰动NaN正确" if w_nan_correct else f"   ✗ 扰动NaN错误")
        
        print("\n验证完成!")
        print("✓ 所有结构体操作函数基本功能正常")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
