#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 dpm_interpn 函数的脚本

这个脚本快速验证 dpm_interpn 函数的基本功能
"""

import numpy as np
from dpm import dpm_interpn


def main():
    """主验证函数"""
    print("快速验证 dpm_interpn 函数")
    print("=" * 40)
    
    try:
        # 测试1: 一维插值
        print("测试1: 一维插值")
        xx = np.array([0, 1, 2, 3, 4])
        yy = np.array([0, 1, 4, 9, 16])  # y = x^2
        A = np.array([1.5, 2.5])
        
        y1d = dpm_interpn(xx, yy, A)
        expected1d = A**2
        
        print(f"  输入: xx={xx}, yy={yy}")
        print(f"  查询点: A={A}")
        print(f"  结果: {y1d}")
        print(f"  期望: {expected1d}")
        print(f"  误差: {np.abs(y1d - expected1d)}")
        
        # 测试2: 二维插值
        print("\n测试2: 二维插值")
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        YY = np.array([[0, 1, 4],
                       [1, 2, 5]])  # 2x3矩阵
        
        A1 = np.array([1.0])
        A2 = np.array([0.5])
        
        y2d = dpm_interpn(xx1, xx2, YY, A1, A2)
        
        print(f"  输入: xx1={xx1}, xx2={xx2}")
        print(f"  YY矩阵:\n{YY}")
        print(f"  查询点: A1={A1}, A2={A2}")
        print(f"  结果: {y2d}")
        
        # 测试3: 三维插值
        print("\n测试3: 三维插值")
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        YY3 = np.array([[[1, 2], [3, 4]], [[5, 6], [7, 8]]])  # 2x2x2矩阵
        
        A1 = np.array([0.5])
        A2 = np.array([0.5])
        A3 = np.array([0.5])
        
        y3d = dpm_interpn(xx1, xx2, xx3, YY3, A1, A2, A3)
        
        print(f"  输入: xx1={xx1}, xx2={xx2}, xx3={xx3}")
        print(f"  YY3矩阵形状: {YY3.shape}")
        print(f"  查询点: A1={A1}, A2={A2}, A3={A3}")
        print(f"  结果: {y3d}")
        
        print("\n验证完成!")
        
        # 简单的正确性检查
        print(f"\n正确性检查:")
        
        # 检查一维插值的精度
        error_1d = np.max(np.abs(y1d - expected1d))
        print(f"一维插值最大误差: {error_1d:.6f}")
        
        # 检查边界情况
        y_boundary = dpm_interpn(xx, yy, np.array([0.0, 4.0]))  # 边界点
        print(f"边界点插值: {y_boundary} (应该是 [0, 16])")
        
        # 检查超出边界的情况
        y_outside = dpm_interpn(xx, yy, np.array([-1.0, 5.0]))  # 边界外
        print(f"边界外插值: {y_outside}")
        
        print("✓ 基本功能验证完成")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
