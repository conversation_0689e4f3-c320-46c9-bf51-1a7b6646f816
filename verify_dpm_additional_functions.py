#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 DPM 新增函数的脚本

这个脚本快速验证新翻译函数的基本功能
"""

import numpy as np
from dpm import dpm_setallfield, dpm_code, dpm_findu, dpm_findl, dpm_sub2ind


def main():
    """主验证函数"""
    print("快速验证 DPM 新增函数")
    print("=" * 40)
    
    try:
        # 测试 dpm_setallfield
        print("1. 测试 dpm_setallfield 函数:")
        test_dict = {'a': 1.0, 'b': [1, 2], 'c': {'x': 5.0}}
        result = dpm_setallfield(test_dict, 'zero')
        print(f"   原始: {test_dict}")
        print(f"   设零: {result}")
        print(f"   ✓ 正确" if result['a'] == 0.0 else f"   ✗ 错误")
        
        # 测试 dpm_code
        print("\n2. 测试 dpm_code 函数:")
        code_result = dpm_code('U{#}', [1, 2, 3], ',')
        expected_code = 'U{1},U{2},U{3}'
        print(f"   结果: {code_result}")
        print(f"   期望: {expected_code}")
        print(f"   ✓ 正确" if code_result == expected_code else f"   ✗ 错误")
        
        # 测试 dpm_findu 和 dpm_findl
        print("\n3. 测试 dpm_findu 和 dpm_findl 函数:")
        A = np.array([0, 1, 2, 3, 4])
        val = 2.3
        upper_idx = dpm_findu(A, val)
        lower_idx = dpm_findl(A, val)
        print(f"   数组: {A}")
        print(f"   查询值: {val}")
        print(f"   上界索引: {upper_idx}")
        print(f"   下界索引: {lower_idx}")
        print(f"   ✓ 上界正确" if upper_idx == 4 else f"   ✗ 上界错误")  # ceil((2.3-0)/1) + 1 = 4
        print(f"   ✓ 下界正确" if lower_idx == 3 else f"   ✗ 下界错误")  # floor((2.3-0)/1) + 1 = 3
        
        # 测试 dpm_sub2ind
        print("\n4. 测试 dpm_sub2ind 函数:")
        siz = (3, 4)  # 3行4列
        row_idx = 2   # 第2行
        col_idx = 3   # 第3列
        linear_idx = dpm_sub2ind(siz, row_idx, col_idx)
        expected_linear = 8  # MATLAB列优先: (2-1) + (3-1)*3 + 1 = 1 + 6 + 1 = 8
        print(f"   矩阵大小: {siz}")
        print(f"   行索引: {row_idx}, 列索引: {col_idx}")
        print(f"   线性索引: {linear_idx}")
        print(f"   期望: {expected_linear}")
        print(f"   ✓ 正确" if linear_idx == expected_linear else f"   ✗ 错误")
        
        print("\n验证完成!")
        print("✓ 所有新增函数基本功能正常")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
