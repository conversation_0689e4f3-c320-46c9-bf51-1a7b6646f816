#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：验证修改后的dpm_interpn三维插值函数与MATLAB的一致性

修改内容：
1. 参数顺序从 dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3) 
   改为 dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)，与MATLAB保持一致
2. 内部逻辑相应调整以保持功能正确性
"""

import numpy as np
from dpm import dpm_interpn


def test_parameter_order_consistency_3d():
    """测试三维插值参数顺序与MATLAB的一致性"""
    print("测试三维插值参数顺序与MATLAB的一致性")
    print("=" * 60)
    
    # 创建测试数据，模拟MATLAB中的调用
    xx1 = np.array([0, 1, 2])  # 第一维网格（MATLAB中的第三个参数）
    xx2 = np.array([0, 1])     # 第二维网格（MATLAB中的第二个参数）
    xx3 = np.array([0, 1])     # 第三维网格（MATLAB中的第一个参数）
    
    # 创建值矩阵 YY[i,j,k] 对应 (xx3[i], xx2[j], xx1[k])
    YY = np.zeros((len(xx3), len(xx2), len(xx1)))
    for i in range(len(xx3)):
        for j in range(len(xx2)):
            for k in range(len(xx1)):
                YY[i, j, k] = xx3[i] + xx2[j] + xx1[k]  # 简单的线性函数
    
    print(f"xx1 (第一维): {xx1}")
    print(f"xx2 (第二维): {xx2}")
    print(f"xx3 (第三维): {xx3}")
    print(f"YY 矩阵形状: {YY.shape}")
    print(f"YY 矩阵:\n{YY}")
    
    # 测试点
    A1 = np.array([1.0])  # 第一维查询点
    A2 = np.array([0.5])  # 第二维查询点
    A3 = np.array([0.5])  # 第三维查询点
    
    print(f"\n查询点: A1={A1[0]} (第一维), A2={A2[0]} (第二维), A3={A3[0]} (第三维)")
    
    # 使用新的参数顺序调用：dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    # 这与MATLAB的 dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1) 完全一致
    result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    
    print(f"Python结果: {result[0]}")
    
    # 期望值：A3 + A2 + A1 = 0.5 + 0.5 + 1.0 = 2.0
    expected = A3[0] + A2[0] + A1[0]
    
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ 三维参数顺序一致性测试通过")
        return True
    else:
        print("✗ 三维参数顺序一致性测试失败")
        return False


def test_corner_points_3d():
    """测试三维角点"""
    print("\n测试三维角点")
    print("=" * 60)
    
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    
    # 创建简单的值矩阵
    YY = np.array([[[0, 1],    # xx3=0, xx2=0时：(0,0,0)=0, (0,0,1)=1
                    [2, 3]],   # xx3=0, xx2=1时：(0,1,0)=2, (0,1,1)=3
                   [[4, 5],    # xx3=1, xx2=0时：(1,0,0)=4, (1,0,1)=5
                    [6, 7]]])  # xx3=1, xx2=1时：(1,1,0)=6, (1,1,1)=7
    
    # 测试8个角点
    test_points = [
        (np.array([0]), np.array([0]), np.array([0]), 0),  # (A3=0, A2=0, A1=0) -> YY[0,0,0] = 0
        (np.array([1]), np.array([0]), np.array([0]), 1),  # (A3=0, A2=0, A1=1) -> YY[0,0,1] = 1
        (np.array([0]), np.array([1]), np.array([0]), 2),  # (A3=0, A2=1, A1=0) -> YY[0,1,0] = 2
        (np.array([1]), np.array([1]), np.array([0]), 3),  # (A3=0, A2=1, A1=1) -> YY[0,1,1] = 3
        (np.array([0]), np.array([0]), np.array([1]), 4),  # (A3=1, A2=0, A1=0) -> YY[1,0,0] = 4
        (np.array([1]), np.array([0]), np.array([1]), 5),  # (A3=1, A2=0, A1=1) -> YY[1,0,1] = 5
        (np.array([0]), np.array([1]), np.array([1]), 6),  # (A3=1, A2=1, A1=0) -> YY[1,1,0] = 6
        (np.array([1]), np.array([1]), np.array([1]), 7),  # (A3=1, A2=1, A1=1) -> YY[1,1,1] = 7
    ]
    
    all_passed = True
    for i, (A1, A2, A3, expected) in enumerate(test_points):
        result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
        error = abs(result[0] - expected)
        print(f"角点 {i+1}: A1={A1[0]}, A2={A2[0]}, A3={A3[0]} -> 结果={result[0]}, 期望={expected}, 误差={error}")
        if error > 1e-10:
            all_passed = False
    
    if all_passed:
        print("✓ 三维角点测试通过")
        return True
    else:
        print("✗ 三维角点测试失败")
        return False


def test_center_point_3d():
    """测试三维中心点插值"""
    print("\n测试三维中心点插值")
    print("=" * 60)
    
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    
    # 使用简单的值矩阵
    YY = np.array([[[0, 1],
                    [2, 3]],
                   [[4, 5],
                    [6, 7]]])
    
    # 测试中心点
    A1 = np.array([0.5])
    A2 = np.array([0.5])
    A3 = np.array([0.5])
    
    result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    
    # 手动计算三线性插值
    # 8个角点的值：0, 1, 2, 3, 4, 5, 6, 7
    # 在中心点(0.5, 0.5, 0.5)处，每个角点的权重都是0.125
    expected = 0.125 * (0 + 1 + 2 + 3 + 4 + 5 + 6 + 7)
    
    print(f"中心点: A1={A1[0]}, A2={A2[0]}, A3={A3[0]}")
    print(f"结果: {result[0]}")
    print(f"期望: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ 三维中心点测试通过")
        return True
    else:
        print("✗ 三维中心点测试失败")
        return False


def test_multiple_points_3d():
    """测试三维多点插值"""
    print("\n测试三维多点插值")
    print("=" * 60)
    
    xx1 = np.array([0, 1, 2])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    
    # 创建线性函数：f(x3, x2, x1) = x3 + x2 + x1
    YY = np.zeros((len(xx3), len(xx2), len(xx1)))
    for i in range(len(xx3)):
        for j in range(len(xx2)):
            for k in range(len(xx1)):
                YY[i, j, k] = xx3[i] + xx2[j] + xx1[k]
    
    # 多个测试点
    A1 = np.array([0.5, 1.0, 1.5])
    A2 = np.array([0.3, 0.5, 0.7])
    A3 = np.array([0.2, 0.5, 0.8])
    
    result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    expected = A3 + A2 + A1  # 对于线性函数，插值结果应该精确
    
    print(f"多点查询: A1={A1}, A2={A2}, A3={A3}")
    print(f"结果: {result}")
    print(f"期望: {expected}")
    
    error = np.abs(result - expected)
    print(f"误差: {error}")
    
    if np.all(error < 1e-10):
        print("✓ 三维多点插值测试通过")
        return True
    else:
        print("✗ 三维多点插值测试失败")
        return False


def main():
    """主测试函数"""
    print("最终测试：验证修改后的dpm_interpn三维插值函数")
    print("参数顺序：dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1) - 与MATLAB一致")
    print()
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 参数顺序一致性
    if test_parameter_order_consistency_3d():
        success_count += 1
    
    # 测试2: 角点测试
    if test_corner_points_3d():
        success_count += 1
    
    # 测试3: 中心点测试
    if test_center_point_3d():
        success_count += 1
    
    # 测试4: 多点插值
    if test_multiple_points_3d():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！")
        print("✓ 三维插值参数顺序已成功修改为与MATLAB一致")
        print("✓ 函数逻辑正确，插值结果准确")
        print("✓ 边界情况处理正常")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
