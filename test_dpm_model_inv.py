#!/usr/bin/env python3
"""
测试 dpm_model_inv 函数
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, '.')

print("开始导入...")
from dpm import dpm_model_inv, Input
print("导入成功")

def simple_test_model(inp, par):
    """
    简单的测试模型
    x[k+1] = 0.9*x[k] + 0.1*u[k]
    """
    X = {1: 0.9 * inp.X[1] + 0.1 * inp.U[1]}
    C = {1: inp.U[1]**2}  # 二次代价
    I = np.zeros_like(inp.X[1])  # 无约束
    return X, C, I

def test_dpm_model_inv():
    """测试 dpm_model_inv 函数"""
    print("测试 dpm_model_inv 函数")
    print("=" * 40)
    
    try:
        # 创建输入结构
        inp = Input()
        inp.X = {1: np.array([0.5])}
        inp.U = {1: np.array([0.1])}
        inp.W = {}
        inp.Ts = 0.1
        
        # 创建参数结构
        par = {
            'model': simple_test_model,
            'options': {'Tol': 1e-6, 'Iter': 10}
        }
        
        print(f"初始状态: {inp.X[1]}")
        print(f"初始输入: {inp.U[1]}")
        
        # 调用反演模型
        X, C, I, out = dpm_model_inv(inp, par)
        
        print(f"最终状态: {X[1]}")
        print(f"代价 C[1] (状态变化): {C[1]}")
        print(f"代价 C[2] (原始代价): {C[2]}")
        print(f"不可行性标志: {I}")
        print(f"输出结构: {out}")
        
        # 验证结果
        if isinstance(X, dict) and 1 in X:
            print("✓ 状态输出格式正确")
        else:
            print("✗ 状态输出格式错误")
            
        if isinstance(C, dict) and 1 in C and 2 in C:
            print("✓ 代价输出格式正确")
        else:
            print("✗ 代价输出格式错误")
            
        if out == []:
            print("✓ 输出结构正确（空列表）")
        else:
            print("✗ 输出结构错误")
            
        print("\n✓ dpm_model_inv 测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_dpm_model_inv()
