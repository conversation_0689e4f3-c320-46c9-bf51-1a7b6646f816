#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证六维插值功能的脚本

这个脚本快速验证根据MATLAB版本实现的六维插值功能
"""

import numpy as np
from dpm import dpm_interpn


def main():
    """主验证函数"""
    print("快速验证六维插值功能")
    print("=" * 40)
    
    try:
        # 创建简单的6D测试数据 (2x2x2x2x2x2)
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        xx4 = np.array([0, 1])
        xx5 = np.array([0, 1])
        xx6 = np.array([0, 1])
        
        # 创建2x2x2x2x2x2的测试矩阵，使用线性函数 f(x1,x2,x3,x4,x5,x6) = x1 + x2 + x3 + x4 + x5 + x6
        YY = np.zeros((2, 2, 2, 2, 2, 2))  # (xx6, xx5, xx4, xx3, xx2, xx1) 顺序
        for i6, x6 in enumerate(xx6):
            for i5, x5 in enumerate(xx5):
                for i4, x4 in enumerate(xx4):
                    for i3, x3 in enumerate(xx3):
                        for i2, x2 in enumerate(xx2):
                            for i1, x1 in enumerate(xx1):
                                YY[i6, i5, i4, i3, i2, i1] = x1 + x2 + x3 + x4 + x5 + x6
        
        print(f"输入网格: xx1={xx1}, xx2={xx2}, xx3={xx3}, xx4={xx4}, xx5={xx5}, xx6={xx6}")
        print(f"值矩阵形状: {YY.shape}")
        print(f"YY[0,0,0,0,:,:] (x3=0,x4=0,x5=0,x6=0):\n{YY[0,0,0,0,:,:]}")
        print(f"YY[1,1,1,1,:,:] (x3=1,x4=1,x5=1,x6=1):\n{YY[1,1,1,1,:,:]}")
        
        # 测试角点（应该精确匹配）
        print("\n角点测试:")
        corner_tests = [
            ((0, 0, 0, 0, 0, 0), YY[0, 0, 0, 0, 0, 0]),  # 0
            ((1, 0, 0, 0, 0, 0), YY[0, 0, 0, 0, 0, 1]),  # 1
            ((0, 1, 0, 0, 0, 0), YY[0, 0, 0, 0, 1, 0]),  # 1
            ((1, 1, 0, 0, 0, 0), YY[0, 0, 0, 0, 1, 1]),  # 2
            ((1, 1, 1, 1, 1, 1), YY[1, 1, 1, 1, 1, 1]),  # 6
        ]
        
        max_error = 0
        for (a1, a2, a3, a4, a5, a6), expected in corner_tests:
            A1, A2, A3, A4, A5, A6 = np.array([a1]), np.array([a2]), np.array([a3]), np.array([a4]), np.array([a5]), np.array([a6])
            y = dpm_interpn(xx1, xx2, xx3, xx4, xx5, xx6, YY, A1, A2, A3, A4, A5, A6)
            error = abs(y[0] - expected)
            max_error = max(max_error, error)
            print(f"  ({a1}, {a2}, {a3}, {a4}, {a5}, {a6}): y = {y[0]:.6f}, 期望 = {expected:.6f}, 误差 = {error:.8f}")
        
        print(f"最大角点误差: {max_error:.8f}")
        
        # 测试中心点
        print("\n中心点测试:")
        A1, A2, A3, A4, A5, A6 = np.array([0.5]), np.array([0.5]), np.array([0.5]), np.array([0.5]), np.array([0.5]), np.array([0.5])
        y_center = dpm_interpn(xx1, xx2, xx3, xx4, xx5, xx6, YY, A1, A2, A3, A4, A5, A6)
        expected_center = 0.5 + 0.5 + 0.5 + 0.5 + 0.5 + 0.5  # 线性函数的期望值
        
        print(f"  中心点 (0.5, 0.5, 0.5, 0.5, 0.5, 0.5): y = {y_center[0]:.6f}")
        print(f"  期望 (线性函数): {expected_center:.6f}")
        print(f"  误差: {abs(y_center[0] - expected_center):.8f}")
        
        # 测试边界情况
        print("\n边界测试:")
        edge_tests = [
            (0.5, 0, 0, 0, 0, 0),          # 边上
            (0, 0.5, 0, 0, 0, 0),          # 边上
            (0, 0, 0.5, 0, 0, 0),          # 边上
            (0, 0, 0, 0.5, 0, 0),          # 边上
            (0, 0, 0, 0, 0.5, 0),          # 边上
            (0, 0, 0, 0, 0, 0.5),          # 边上
            (0.5, 0.5, 0, 0, 0, 0),        # 面上
            (0.5, 0.5, 0.5, 0, 0, 0),      # 体上
            (0.5, 0.5, 0.5, 0.5, 0, 0),    # 4D体上
            (0.5, 0.5, 0.5, 0.5, 0.5, 0),  # 5D体上
        ]
        
        for a1, a2, a3, a4, a5, a6 in edge_tests:
            A1, A2, A3, A4, A5, A6 = np.array([a1]), np.array([a2]), np.array([a3]), np.array([a4]), np.array([a5]), np.array([a6])
            y = dpm_interpn(xx1, xx2, xx3, xx4, xx5, xx6, YY, A1, A2, A3, A4, A5, A6)
            expected = a1 + a2 + a3 + a4 + a5 + a6  # 线性函数的期望值
            error = abs(y[0] - expected)
            print(f"  ({a1:.1f}, {a2:.1f}, {a3:.1f}, {a4:.1f}, {a5:.1f}, {a6:.1f}): y = {y[0]:.6f}, 期望 = {expected:.6f}, 误差 = {error:.8f}")
        
        # 测试多点插值
        print("\n多点插值测试:")
        A1_multi = np.array([0.25, 0.75])
        A2_multi = np.array([0.25, 0.75])
        A3_multi = np.array([0.25, 0.75])
        A4_multi = np.array([0.25, 0.75])
        A5_multi = np.array([0.25, 0.75])
        A6_multi = np.array([0.25, 0.75])
        
        y_multi = dpm_interpn(xx1, xx2, xx3, xx4, xx5, xx6, YY, A1_multi, A2_multi, A3_multi, A4_multi, A5_multi, A6_multi)
        expected_multi = A1_multi + A2_multi + A3_multi + A4_multi + A5_multi + A6_multi
        
        print(f"  多点输入: A1={A1_multi}, A2={A2_multi}, A3={A3_multi}, A4={A4_multi}, A5={A5_multi}, A6={A6_multi}")
        print(f"  多点结果: {y_multi}")
        print(f"  期望结果: {expected_multi}")
        print(f"  误差: {np.abs(y_multi - expected_multi)}")
        
        # 测试边界外的点
        print("\n边界外点测试:")
        boundary_tests = [
            (-0.5, 0.5, 0.5, 0.5, 0.5, 0.5, "第一维下边界外"),
            (1.5, 0.5, 0.5, 0.5, 0.5, 0.5, "第一维上边界外"),
            (0.5, -0.5, 0.5, 0.5, 0.5, 0.5, "第二维下边界外"),
            (0.5, 1.5, 0.5, 0.5, 0.5, 0.5, "第二维上边界外"),
        ]
        
        for a1, a2, a3, a4, a5, a6, description in boundary_tests:
            A1, A2, A3, A4, A5, A6 = np.array([a1]), np.array([a2]), np.array([a3]), np.array([a4]), np.array([a5]), np.array([a6])
            y = dpm_interpn(xx1, xx2, xx3, xx4, xx5, xx6, YY, A1, A2, A3, A4, A5, A6)
            print(f"  ({a1:4.1f}, {a2:4.1f}, {a3:4.1f}, {a4:4.1f}, {a5:4.1f}, {a6:4.1f}): y = {y[0]:8.4f} ({description})")
        
        print("\n验证完成!")
        
        # 总体精度检查
        if max_error < 1e-10:
            print("✓ 六维插值精度极高，角点误差 < 1e-10")
        elif max_error < 1e-6:
            print("✓ 六维插值精度良好，角点误差 < 1e-6")
        else:
            print(f"⚠️ 六维插值精度一般，角点误差 = {max_error:.8f}")
        
        print("✓ 六维插值基本功能正常")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
