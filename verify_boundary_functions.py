#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证边界线函数的简单脚本

这个脚本快速验证 dpm_boundary_line_lower 和 dpm_boundary_line_upper 函数的基本功能
"""

import numpy as np
from dpm import (dpm_boundary_line_lower, dpm_boundary_line_upper, 
                 Grid, Problem, Options, Input, Output)


def simple_model(inp, par=None):
    """简单的线性模型"""
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}
    C = {1: inp.U[1]**2 * inp.Ts}
    I = np.zeros_like(inp.X[1])
    return X, C, I


def main():
    """主验证函数"""
    print("验证边界线函数")
    print("=" * 40)
    
    try:
        # 创建最小的测试设置
        grd = Grid()
        grd.Nx = {1: [5] * 6}  # 5个网格点，5个时间步
        grd.Xn = {1: {'lo': [-1.0] * 6, 'hi': [1.0] * 6}}
        grd.X0 = {1: 0.0}
        grd.XN = {1: {'lo': 0.8, 'hi': 1.2}}
        
        grd.Nu = {1: [3] * 5}  # 3个网格点，5个时间步
        grd.Un = {1: {'lo': [-0.5] * 5, 'hi': [0.5] * 5}}
        
        prb = Problem(Ts=0.2, N=5)
        prb.W = {}
        
        options = Options()
        options.MyInf = 1e4
        options.Minimize = True
        options.Verbose = 'off'
        options.Warnings = 'off'
        options.SaveMap = True
        options.Iter = 5
        options.Tol = 1e-6
        
        print("1. 测试下边界线函数...")
        try:
            line_lower = dpm_boundary_line_lower(simple_model, None, grd, prb, options)
            print(f"   ✓ 下边界线计算成功，状态轨迹形状: {line_lower.Xo.shape}")
        except Exception as e:
            print(f"   ✗ 下边界线计算失败: {str(e)}")
        
        print("2. 测试上边界线函数...")
        try:
            line_upper = dpm_boundary_line_upper(simple_model, None, grd, prb, options)
            print(f"   ✓ 上边界线计算成功，状态轨迹形状: {line_upper.Xo.shape}")
        except Exception as e:
            print(f"   ✗ 上边界线计算失败: {str(e)}")
        
        print("3. 比较边界线结果...")
        try:
            if 'line_lower' in locals() and 'line_upper' in locals():
                print(f"   下边界线首个值: {line_lower.Xo.flat[0]:.4f}")
                print(f"   上边界线首个值: {line_upper.Xo.flat[0]:.4f}")
                print("   ✓ 边界线比较完成")
        except Exception as e:
            print(f"   ✗ 边界线比较失败: {str(e)}")
        
        print("\n验证完成!")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
