#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修改后的dpm_interpn二维插值函数
验证参数顺序与MATLAB的dpm_interpn保持一致
"""

import numpy as np
from dpm import dpm_interpn


def test_simple_case():
    """测试简单的二维插值案例"""
    print("测试简单的二维插值案例")
    print("=" * 50)
    
    # 创建简单的2x2网格
    xx1 = np.array([0, 1])  # 第一维网格（列）
    xx2 = np.array([0, 1])  # 第二维网格（行）
    
    # 创建简单的值矩阵
    # YY[i,j] 表示在 (xx2[i], xx1[j]) 处的值
    YY = np.array([[0, 1],    # xx2=0时：(0,0)=0, (0,1)=1
                   [2, 3]])   # xx2=1时：(1,0)=2, (1,1)=3
    
    print(f"xx1 (列): {xx1}")
    print(f"xx2 (行): {xx2}")
    print(f"YY 矩阵:\n{YY}")
    print(f"YY[0,0] = {YY[0,0]} (xx2=0, xx1=0)")
    print(f"YY[0,1] = {YY[0,1]} (xx2=0, xx1=1)")
    print(f"YY[1,0] = {YY[1,0]} (xx2=1, xx1=0)")
    print(f"YY[1,1] = {YY[1,1]} (xx2=1, xx1=1)")
    
    # 测试插值点
    A1 = np.array([0.5])  # 第一维查询点（列方向）
    A2 = np.array([0.5])  # 第二维查询点（行方向）
    
    print(f"\n查询点: A1={A1[0]} (列), A2={A2[0]} (行)")
    
    # 使用新的参数顺序调用：dpm_interpn(xx2, xx1, YY, A2, A1)
    result = dpm_interpn(xx2, xx1, YY, A2, A1)
    
    print(f"插值结果: {result[0]}")
    
    # 手动计算双线性插值
    # 在点 (A2=0.5, A1=0.5) 处
    # 四个角点的值：
    # (0,0) = 0, (0,1) = 1, (1,0) = 2, (1,1) = 3
    # 双线性插值：
    # v = (1-A2)*(1-A1)*YY[0,0] + (1-A2)*A1*YY[0,1] + A2*(1-A1)*YY[1,0] + A2*A1*YY[1,1]
    # v = 0.5*0.5*0 + 0.5*0.5*1 + 0.5*0.5*2 + 0.5*0.5*3
    # v = 0 + 0.25 + 1 + 0.75 = 1.5
    expected = 0.5 * 0.5 * 0 + 0.5 * 0.5 * 1 + 0.5 * 0.5 * 2 + 0.5 * 0.5 * 3
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ 简单测试通过")
        return True
    else:
        print("✗ 简单测试失败")
        return False


def test_boundary_points():
    """测试边界点"""
    print("\n测试边界点")
    print("=" * 50)
    
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    YY = np.array([[0, 1],
                   [2, 3]])
    
    # 测试四个角点
    test_points = [
        (np.array([0]), np.array([0]), 0),  # (A2=0, A1=0) -> YY[0,0] = 0
        (np.array([1]), np.array([0]), 1),  # (A2=0, A1=1) -> YY[0,1] = 1
        (np.array([0]), np.array([1]), 2),  # (A2=1, A1=0) -> YY[1,0] = 2
        (np.array([1]), np.array([1]), 3),  # (A2=1, A1=1) -> YY[1,1] = 3
    ]
    
    all_passed = True
    for i, (A1, A2, expected) in enumerate(test_points):
        result = dpm_interpn(xx2, xx1, YY, A2, A1)
        error = abs(result[0] - expected)
        print(f"点 {i+1}: A1={A1[0]}, A2={A2[0]} -> 结果={result[0]}, 期望={expected}, 误差={error}")
        if error > 1e-10:
            all_passed = False
    
    if all_passed:
        print("✓ 边界点测试通过")
        return True
    else:
        print("✗ 边界点测试失败")
        return False


def test_matlab_example():
    """测试与MATLAB示例一致的案例"""
    print("\n测试与MATLAB示例一致的案例")
    print("=" * 50)
    
    # 使用与MATLAB测试相同的数据
    xx1 = np.array([0, 1, 2])
    xx2 = np.array([0, 1])
    
    # 创建测试矩阵，使得插值结果容易验证
    YY = np.array([[0, 1, 4],    # xx2=0时：f(0,0)=0, f(0,1)=1, f(0,2)=4
                   [1, 2, 5]])   # xx2=1时：f(1,0)=1, f(1,1)=2, f(1,2)=5
    
    print(f"xx1: {xx1}")
    print(f"xx2: {xx2}")
    print(f"YY 矩阵:\n{YY}")
    
    # 测试中点插值
    A1 = np.array([0.5])  # xx1方向的中点
    A2 = np.array([0.5])  # xx2方向的中点
    
    result = dpm_interpn(xx2, xx1, YY, A2, A1)
    
    # 手动计算：在 (A2=0.5, A1=0.5) 处
    # 四个角点：(0,0)=0, (0,1)=1, (1,0)=1, (1,1)=2
    # 双线性插值：0.5*0.5*0 + 0.5*0.5*1 + 0.5*0.5*1 + 0.5*0.5*2 = 1.0
    expected = 0.5 * 0.5 * 0 + 0.5 * 0.5 * 1 + 0.5 * 0.5 * 1 + 0.5 * 0.5 * 2
    
    print(f"查询点: A1={A1[0]}, A2={A2[0]}")
    print(f"插值结果: {result[0]}")
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ MATLAB示例测试通过")
        return True
    else:
        print("✗ MATLAB示例测试失败")
        return False


def main():
    """主测试函数"""
    print("简单测试修改后的dpm_interpn二维插值函数")
    print("验证参数顺序：dpm_interpn(xx2, xx1, YY, A2, A1)")
    print()
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 简单案例
    if test_simple_case():
        success_count += 1
    
    # 测试2: 边界点
    if test_boundary_points():
        success_count += 1
    
    # 测试3: MATLAB示例
    if test_matlab_example():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！参数顺序修改成功。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
