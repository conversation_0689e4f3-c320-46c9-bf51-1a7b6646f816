#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的测试模型文件
状态数量: 2
输入数量: 1
"""

import numpy as np
from dpm import Input, Output

def test_model(inp: Input, par=None):
    """
    测试模型函数

    参数:
        inp: 输入结构
            inp.X{i} 状态
            inp.U{i} 输入
            inp.W{i} 扰动 (如dis结构中定义)
            inp.Ts   时间步长
        par: 包含用户定义参数的结构

    返回:
        X: 状态更新 (必须在模型函数中设置)
        C: 代价 (必须在模型函数中设置)
        I: 不可行性 (0=可行/1=不可行，必须在模型函数中设置)
        signals: 用户定义的输出信号
    """

    # 状态更新 (out.X{i} 必须在模型函数中设置)
    X = {}
    X[1] = 0.013121 * (inp.X[1] + inp.U[1] + inp.W[1]) / inp.Ts + inp.X[1]
    X[2] = 0.386574 * (inp.X[1] + inp.U[1] + inp.W[1]) / inp.Ts + inp.X[2]

    # 代价函数 (out.C{1} 必须在模型函数中设置)
    C = {1: -inp.Ts * inp.U[1]}

    # 不可行性检查 (out.I [0=可行/1=不可行] 必须在模型函数中设置)
    # 例如，如果状态超出网格范围或出现不可行的输入状态组合
    # 这些状态输入组合的代价将被DPM函数设置为options.MyInf
    I = 0

    # 存储信号 (在out结构中存储任何其他信号)
    signals = {}
    signals["U1"] = inp.U[1]

    return X, C, I, signals
