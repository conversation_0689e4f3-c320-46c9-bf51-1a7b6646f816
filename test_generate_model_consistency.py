#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试生成测试模型功能与MATLAB版本的一致性

验证Python版本的_generate_test_model函数是否与MATLAB版本@dpm.m（134-172行）一致
"""

import numpy as np
import os
import sys
from dpm import dpm, Input

def test_model_generation():
    """测试模型生成功能"""
    print("=" * 60)
    print("测试生成测试模型功能与MATLAB版本的一致性")
    print("=" * 60)
    
    # 测试1: 基本模型生成
    print("测试1: 基本模型生成")
    test_filename = "test_consistency_model"
    
    # 清理可能存在的文件
    if os.path.exists(f"{test_filename}.py"):
        os.remove(f"{test_filename}.py")
    
    try:
        # 生成模型 (2个状态, 1个输入)
        dpm(test_filename, 2, 1)
        
        # 检查文件是否生成
        if os.path.exists(f"{test_filename}.py"):
            print("✓ 模型文件生成成功")
            
            # 导入并测试生成的模型
            import importlib.util
            spec = importlib.util.spec_from_file_location(test_filename, f"{test_filename}.py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            model_func = getattr(module, test_filename)
            
            # 创建测试输入
            inp = Input()
            inp.X = {1: np.array([0.5]), 2: np.array([0.3])}
            inp.U = {1: np.array([0.1])}
            inp.W = {1: np.array([0.0])}
            inp.Ts = 0.1
            
            # 调用模型
            result = model_func(inp, None)
            
            # 验证返回值数量和类型
            if len(result) == 4:
                X, C, I, signals = result
                print("✓ 返回4个值 (与MATLAB一致)")
                
                # 验证X的结构
                if isinstance(X, dict) and len(X) == 2:
                    print("✓ X结构正确 (2个状态)")
                else:
                    print("✗ X结构错误")
                
                # 验证C的结构
                if isinstance(C, dict) and 1 in C:
                    print("✓ C结构正确")
                else:
                    print("✗ C结构错误")
                
                # 验证I的类型 (应该是标量，与MATLAB一致)
                if isinstance(I, (int, float)) and I == 0:
                    print("✓ I为标量0 (与MATLAB一致)")
                else:
                    print(f"✗ I类型错误: {type(I)}, 值: {I}")
                
                # 验证signals结构
                if isinstance(signals, dict) and "U1" in signals:
                    print("✓ signals结构正确")
                else:
                    print("✗ signals结构错误")
                
            else:
                print(f"✗ 返回值数量错误: {len(result)} (应该是4)")
            
        else:
            print("✗ 模型文件生成失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 清理测试文件
    if os.path.exists(f"{test_filename}.py"):
        os.remove(f"{test_filename}.py")

def test_file_exists_error():
    """测试文件已存在时的错误处理"""
    print("\n测试2: 文件已存在时的错误处理")
    
    test_filename = "test_exists_model"
    
    try:
        # 先创建一个文件
        with open(f"{test_filename}.py", 'w') as f:
            f.write("# 测试文件")
        
        # 尝试生成同名模型，应该抛出错误
        error_caught = False
        try:
            dpm(test_filename, 1, 1)
            print("✗ 应该抛出错误但没有")
        except ValueError as e:
            error_caught = True
            if "已存在" in str(e):
                print("✓ 正确抛出文件已存在错误")
            else:
                print(f"✗ 错误信息不正确: {str(e)}")
        except Exception as e:
            error_caught = True
            print(f"✗ 抛出了错误的异常类型: {type(e)}")

        if not error_caught:
            print("✗ 没有抛出任何错误")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    # 清理测试文件
    if os.path.exists(f"{test_filename}.py"):
        os.remove(f"{test_filename}.py")

def test_different_sizes():
    """测试不同的状态和输入数量"""
    print("\n测试3: 不同的状态和输入数量")
    
    test_cases = [
        (1, 1),  # 1状态, 1输入
        (3, 2),  # 3状态, 2输入
        (2, 3),  # 2状态, 3输入
    ]
    
    for nx, nu in test_cases:
        test_filename = f"test_model_{nx}_{nu}"
        
        # 清理可能存在的文件
        if os.path.exists(f"{test_filename}.py"):
            os.remove(f"{test_filename}.py")
        
        try:
            # 生成模型
            dpm(test_filename, nx, nu)
            
            if os.path.exists(f"{test_filename}.py"):
                print(f"✓ 模型 {nx}状态x{nu}输入 生成成功")
                
                # 验证生成的代码内容
                with open(f"{test_filename}.py", 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查状态数量
                x_count = content.count("X[")
                if x_count >= nx:  # 至少应该有nx个X[]
                    print(f"  ✓ 包含足够的状态变量")
                else:
                    print(f"  ✗ 状态变量数量不足: {x_count} < {nx}")
                
                # 检查signals数量
                u_signal_count = content.count('signals["U')
                if u_signal_count == nu:
                    print(f"  ✓ signals包含正确数量的输入: {nu}")
                else:
                    print(f"  ✗ signals输入数量错误: {u_signal_count} != {nu}")
                
            else:
                print(f"✗ 模型 {nx}状态x{nu}输入 生成失败")
                
        except Exception as e:
            print(f"✗ 测试 {nx}状态x{nu}输入 失败: {str(e)}")
        
        # 清理测试文件
        if os.path.exists(f"{test_filename}.py"):
            os.remove(f"{test_filename}.py")

def main():
    """主测试函数"""
    print("测试生成测试模型功能与MATLAB版本的一致性")
    print("对比MATLAB @dpm.m 第134-172行的逻辑")
    print()
    
    success_count = 0
    total_tests = 3
    
    try:
        test_model_generation()
        success_count += 1
    except:
        pass
    
    try:
        test_file_exists_error()
        success_count += 1
    except:
        pass
    
    try:
        test_different_sizes()
        success_count += 1
    except:
        pass
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！")
        print("✓ Python版本与MATLAB版本一致")
        print("✓ 生成的模型返回4个值: X, C, I, signals")
        print("✓ I为标量0 (与MATLAB一致)")
        print("✓ signals包含所有输入信号")
        print("✓ 错误处理正确")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
