#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 PHEV HT21 Series P1P3 混合动力车辆模型函数

这个脚本测试混合动力车辆模型函数的各种工作模式和输入条件
"""

import numpy as np
import matplotlib.pyplot as plt
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试 PHEV HT21 Series P1P3 基本功能")
    print("=" * 60)
    
    try:
        # 创建测试输入
        inp = {
            'W': [20.0, 0.5],  # 车轮速度 20 m/s, 加速度 0.5 m/s^2
            'U': [0.5, 1],     # 扭矩分配比例 0.5, 工作模式 1
            'X': [0.6]         # 电池荷电状态 60%
        }
        
        # 用户定义参数 (可以为空)
        par = {}
        
        print(f"输入参数:")
        print(f"  车轮速度: {inp['W'][0]} m/s")
        print(f"  车轮加速度: {inp['W'][1]} m/s^2")
        print(f"  扭矩分配比例: {inp['U'][0]}")
        print(f"  工作模式: {inp['U'][1]}")
        print(f"  初始SOC: {inp['X'][0]}")
        
        # 调用函数
        X, C, I, out = phev_ht21_serip1p3(inp, par)
        
        print(f"\n输出结果:")
        print(f"  最终SOC: {X[0]:.4f}")
        print(f"  燃油消耗: {C[0]:.6f} kg/s")
        print(f"  不可行标志: {I}")
        
        print(f"\n详细输出信号:")
        print(f"  车轮扭矩: {out['Tv']:.2f} Nm")
        print(f"  发动机扭矩: {out['Te']:.2f} Nm")
        print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
        print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
        print(f"  发动机转速: {out['wg']:.2f} rad/s")
        print(f"  P1电机转速: {out['wm1']:.2f} rad/s")
        print(f"  P3电机转速: {out['wm3']:.2f} rad/s")
        print(f"  电池电流: {out['Ib']:.2f} A")
        print(f"  电池功率: {out['Pb']:.2f} W")
        print(f"  发动机效率: {out['e_th']:.4f}")
        
        print("✓ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_different_modes():
    """测试不同工作模式"""
    print("\n" + "=" * 60)
    print("测试不同工作模式")
    print("=" * 60)
    
    try:
        # 基础输入
        base_inp = {
            'W': [15.0, 0.0],  # 恒定速度
            'U': [0.5, 0],     # 将被修改
            'X': [0.5]         # 50% SOC
        }
        par = {}
        
        modes = [
            (0, "串联充电模式"),
            (1, "并联模式"),
            (2, "混合模式")
        ]
        
        print("不同工作模式测试:")
        for mode, description in modes:
            inp = base_inp.copy()
            inp['U'] = [0.5, mode]
            
            X, C, I, out = phev_ht21_serip1p3(inp, par)
            
            print(f"\n{description} (模式 {mode}):")
            print(f"  SOC变化: {inp['X'][0]:.3f} → {X[0]:.3f}")
            print(f"  燃油消耗: {C[0]:.6f} kg/s")
            print(f"  发动机扭矩: {out['Te']:.2f} Nm")
            print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
            print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
            print(f"  不可行: {I}")
        
        print("\n✓ 不同工作模式测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 不同工作模式测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_torque_split_ratios():
    """测试不同扭矩分配比例"""
    print("\n" + "=" * 60)
    print("测试不同扭矩分配比例")
    print("=" * 60)
    
    try:
        # 基础输入
        base_inp = {
            'W': [25.0, 1.0],  # 加速工况
            'U': [0.0, 1],     # 扭矩分配比例将被修改
            'X': [0.7]         # 70% SOC
        }
        par = {}
        
        ratios = [0.0, 0.3, 0.5, 0.7, 1.0]
        
        print("不同扭矩分配比例测试:")
        for ratio in ratios:
            inp = base_inp.copy()
            inp['U'] = [ratio, 1]
            
            X, C, I, out = phev_ht21_serip1p3(inp, par)
            
            print(f"\n扭矩分配比例 {ratio:.1f}:")
            print(f"  SOC变化: {inp['X'][0]:.3f} → {X[0]:.3f}")
            print(f"  燃油消耗: {C[0]:.6f} kg/s")
            print(f"  发动机扭矩: {out['Te']:.2f} Nm")
            print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
            print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
            print(f"  电池功率: {out['Pb']:.2f} W")
            print(f"  不可行: {I}")
        
        print("\n✓ 不同扭矩分配比例测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 不同扭矩分配比例测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_soc_range():
    """测试不同SOC范围"""
    print("\n" + "=" * 60)
    print("测试不同SOC范围")
    print("=" * 60)
    
    try:
        # 基础输入
        base_inp = {
            'W': [20.0, 0.0],  # 恒定速度
            'U': [0.5, 1],     # 并联模式
            'X': [0.5]         # SOC将被修改
        }
        par = {}
        
        soc_values = [0.2, 0.4, 0.6, 0.8, 0.9]
        
        print("不同SOC测试:")
        for soc in soc_values:
            inp = base_inp.copy()
            inp['X'] = [soc]
            
            X, C, I, out = phev_ht21_serip1p3(inp, par)
            
            print(f"\n初始SOC {soc:.1f}:")
            print(f"  SOC变化: {inp['X'][0]:.3f} → {X[0]:.3f}")
            print(f"  燃油消耗: {C[0]:.6f} kg/s")
            print(f"  电池电流: {out['Ib']:.2f} A")
            print(f"  电池功率: {out['Pb']:.2f} W")
            print(f"  不可行: {I}")
        
        print("\n✓ 不同SOC范围测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 不同SOC范围测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_driving_conditions():
    """测试不同驾驶工况"""
    print("\n" + "=" * 60)
    print("测试不同驾驶工况")
    print("=" * 60)
    
    try:
        # 基础输入
        base_inp = {
            'W': [0.0, 0.0],   # 速度和加速度将被修改
            'U': [0.5, 1],     # 并联模式
            'X': [0.6]         # 60% SOC
        }
        par = {}
        
        conditions = [
            ([0.0, 0.0], "怠速"),
            ([10.0, 0.0], "低速巡航"),
            ([30.0, 0.0], "高速巡航"),
            ([20.0, 2.0], "加速"),
            ([20.0, -2.0], "减速"),
        ]
        
        print("不同驾驶工况测试:")
        for (speed, accel), description in conditions:
            inp = base_inp.copy()
            inp['W'] = [speed, accel]
            
            X, C, I, out = phev_ht21_serip1p3(inp, par)
            
            print(f"\n{description} (速度: {speed} m/s, 加速度: {accel} m/s²):")
            print(f"  车轮扭矩: {out['Tv']:.2f} Nm")
            print(f"  SOC变化: {inp['X'][0]:.3f} → {X[0]:.3f}")
            print(f"  燃油消耗: {C[0]:.6f} kg/s")
            print(f"  发动机扭矩: {out['Te']:.2f} Nm")
            print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
            print(f"  不可行: {I}")
        
        print("\n✓ 不同驾驶工况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 不同驾驶工况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("PHEV HT21 Series P1P3 混合动力车辆模型测试程序")
    print("这个程序测试混合动力车辆模型函数的各种工作模式和输入条件")
    
    success_count = 0
    total_tests = 5
    
    # 测试1: 基本功能
    if test_basic_functionality():
        success_count += 1
    
    # 测试2: 不同工作模式
    if test_different_modes():
        success_count += 1
    
    # 测试3: 不同扭矩分配比例
    if test_torque_split_ratios():
        success_count += 1
    
    # 测试4: 不同SOC范围
    if test_soc_range():
        success_count += 1
    
    # 测试5: 不同驾驶工况
    if test_driving_conditions():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有混合动力车辆模型测试都通过了！函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查混合动力车辆模型实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
