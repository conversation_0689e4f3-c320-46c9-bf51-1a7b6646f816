#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 PHEV HT21 Series P1P3 混合动力车辆模型函数

这个脚本快速验证混合动力车辆模型函数的基本功能
"""

import numpy as np
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def main():
    """主验证函数"""
    print("快速验证 PHEV HT21 Series P1P3 混合动力车辆模型")
    print("=" * 50)
    
    try:
        # 创建基本测试输入
        inp = {
            'W': [20.0, 0.5],  # 车轮速度 20 m/s, 加速度 0.5 m/s^2
            'U': [0.5, 1],     # 扭矩分配比例 0.5, 并联模式
            'X': [0.6]         # 电池荷电状态 60%
        }
        
        par = {}  # 用户定义参数
        
        print("输入参数:")
        print(f"  车轮速度: {inp['W'][0]} m/s")
        print(f"  车轮加速度: {inp['W'][1]} m/s^2")
        print(f"  扭矩分配比例: {inp['U'][0]}")
        print(f"  工作模式: {inp['U'][1]} (并联模式)")
        print(f"  初始SOC: {inp['X'][0]}")
        
        # 调用混合动力车辆模型函数
        X, C, I, out = phev_ht21_serip1p3(inp, par)
        
        print("\n基本输出:")
        print(f"  最终SOC: {X[0]:.4f}")
        print(f"  燃油消耗率: {C[0]:.6f} kg/s")
        print(f"  不可行标志: {I}")
        
        print("\n关键信号:")
        print(f"  车轮扭矩需求: {out['Tv']:.2f} Nm")
        print(f"  发动机扭矩: {out['Te']:.2f} Nm")
        print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
        print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
        
        print("\n转速信息:")
        print(f"  发动机转速: {out['wg']:.2f} rad/s ({out['wg']*30/np.pi:.0f} rpm)")
        print(f"  P1电机转速: {out['wm1']:.2f} rad/s ({out['wm1']*30/np.pi:.0f} rpm)")
        print(f"  P3电机转速: {out['wm3']:.2f} rad/s ({out['wm3']*30/np.pi:.0f} rpm)")
        
        print("\n电气系统:")
        print(f"  电池电流: {out['Ib']:.2f} A")
        print(f"  电池功率: {out['Pb']:.2f} W")
        print(f"  发动机热效率: {out['e_th']:.4f}")
        
        # 测试纯电动模式
        print("\n" + "-" * 30)
        print("测试纯电动模式:")
        inp_ev = inp.copy()
        inp_ev['U'] = [1.0, 0]  # 纯电动模式
        
        X_ev, C_ev, I_ev, out_ev = phev_ht21_serip1p3(inp_ev, par)
        
        print(f"  纯电动模式SOC: {inp_ev['X'][0]:.4f} → {X_ev[0]:.4f}")
        print(f"  纯电动燃油消耗: {C_ev[0]:.6f} kg/s")
        print(f"  P3电机扭矩: {out_ev['Tm3']:.2f} Nm")
        print(f"  发动机扭矩: {out_ev['Te']:.2f} Nm")
        print(f"  不可行标志: {I_ev}")
        
        # 测试串联充电模式
        print("\n" + "-" * 30)
        print("测试串联充电模式:")
        inp_series = inp.copy()
        inp_series['U'] = [0.0, 0]  # 串联充电模式
        inp_series['X'] = [0.3]     # 低SOC
        
        X_series, C_series, I_series, out_series = phev_ht21_serip1p3(inp_series, par)
        
        print(f"  串联模式SOC: {inp_series['X'][0]:.4f} → {X_series[0]:.4f}")
        print(f"  串联燃油消耗: {C_series[0]:.6f} kg/s")
        print(f"  发动机扭矩: {out_series['Te']:.2f} Nm")
        print(f"  P1电机扭矩: {out_series['Tm1']:.2f} Nm")
        print(f"  P3电机扭矩: {out_series['Tm3']:.2f} Nm")
        print(f"  不可行标志: {I_series}")
        
        # 能量平衡检查
        print("\n" + "-" * 30)
        print("能量平衡检查:")
        
        # 计算功率平衡
        P_wheel = out['Tv'] * out['wm3'] / 10.03  # 车轮功率
        P_engine = out['Te'] * out['wg']          # 发动机功率
        P_motor1 = out['Tm1'] * out['wm1']        # P1电机功率
        P_motor3 = out['Tm3'] * out['wm3']        # P3电机功率
        
        print(f"  车轮功率需求: {P_wheel:.2f} W")
        print(f"  发动机功率: {P_engine:.2f} W")
        print(f"  P1电机功率: {P_motor1:.2f} W")
        print(f"  P3电机功率: {P_motor3:.2f} W")
        print(f"  电池功率: {out['Pb']:.2f} W")
        
        print("\n验证完成!")
        
        # 基本合理性检查
        checks_passed = 0
        total_checks = 4
        
        # 检查1: SOC应该在合理范围内
        if 0 <= X[0] <= 1:
            print("✓ SOC在合理范围内")
            checks_passed += 1
        else:
            print("✗ SOC超出合理范围")
        
        # 检查2: 燃油消耗应该非负
        if C[0] >= 0:
            print("✓ 燃油消耗非负")
            checks_passed += 1
        else:
            print("✗ 燃油消耗为负")
        
        # 检查3: 纯电动模式下燃油消耗应该为0
        if C_ev[0] == 0:
            print("✓ 纯电动模式燃油消耗为0")
            checks_passed += 1
        else:
            print("✗ 纯电动模式燃油消耗不为0")
        
        # 检查4: 输出信号应该是数值
        if all(np.isfinite(val) for val in [out['Te'], out['Tm1'], out['Tm3'], out['Pb']]):
            print("✓ 所有输出信号都是有限数值")
            checks_passed += 1
        else:
            print("✗ 部分输出信号不是有限数值")
        
        print(f"\n合理性检查: {checks_passed}/{total_checks} 通过")
        
        if checks_passed == total_checks:
            print("✓ 混合动力车辆模型函数基本功能正常")
        else:
            print("⚠️ 混合动力车辆模型函数可能存在问题")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
