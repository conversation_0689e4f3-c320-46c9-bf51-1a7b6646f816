#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpn 三维插值功能的脚本

这个脚本专门测试三维插值的完整实现
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from dpm import dpm_interpn


def test_3d_basic_interpolation():
    """测试基本的三维插值功能"""
    print("=" * 60)
    print("测试三维插值基本功能")
    print("=" * 60)
    
    try:
        # 创建三维测试数据
        xx1 = np.array([0, 1, 2])  # 3个点
        xx2 = np.array([0, 1])     # 2个点
        xx3 = np.array([0, 1, 2])  # 3个点
        
        # 创建三维值矩阵 (3x2x3)
        # 使用简单的函数: f(x1,x2,x3) = x1^2 + x2^2 + x3^2
        YY = np.zeros((3, 2, 3))  # (depth, rows, cols) = (xx3, xx2, xx1)
        for i3, x3 in enumerate(xx3):
            for i2, x2 in enumerate(xx2):
                for i1, x1 in enumerate(xx1):
                    YY[i3, i2, i1] = x1**2 + x2**2 + x3**2
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"第三维网格 xx3: {xx3}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        print(f"YY[:,:,0] (x1=0的切片):\n{YY[:,:,0]}")
        print(f"YY[:,:,1] (x1=1的切片):\n{YY[:,:,1]}")
        print(f"YY[:,:,2] (x1=2的切片):\n{YY[:,:,2]}")
        
        # 测试插值点
        test_cases = [
            (0.5, 0.5, 0.5, "中心点"),
            (1.0, 0.0, 1.0, "网格点"),
            (1.5, 0.3, 1.7, "一般插值点"),
            (0.0, 0.0, 0.0, "原点"),
            (2.0, 1.0, 2.0, "边界点"),
        ]
        
        print("\n插值测试:")
        for a1, a2, a3, description in test_cases:
            A1 = np.array([a1])
            A2 = np.array([a2])
            A3 = np.array([a3])
            
            y = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
            expected = a1**2 + a2**2 + a3**2  # 理论期望值
            error = abs(y[0] - expected)
            
            print(f"  ({a1:.1f}, {a2:.1f}, {a3:.1f}): y = {y[0]:.4f}, 期望 = {expected:.4f}, 误差 = {error:.4f} ({description})")
        
        print("✓ 三维插值基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 三维插值基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_3d_boundary_cases():
    """测试三维插值的边界情况"""
    print("\n" + "=" * 60)
    print("测试三维插值边界情况")
    print("=" * 60)
    
    try:
        # 测试1: 最小3D网格 (2x2x2)
        print("测试1: 最小3D网格 (2x2x2)")
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        
        # 创建2x2x2矩阵
        YY = np.array([[[1, 2], [3, 4]], [[5, 6], [7, 8]]])
        
        print(f"YY形状: {YY.shape}")
        print(f"YY[0,:,:] (x3=0的切片):\n{YY[0,:,:]}")
        print(f"YY[1,:,:] (x3=1的切片):\n{YY[1,:,:]}")
        
        # 测试各个角点
        corner_points = [
            (0, 0, 0), (1, 0, 0), (0, 1, 0), (1, 1, 0),
            (0, 0, 1), (1, 0, 1), (0, 1, 1), (1, 1, 1)
        ]
        
        print("\n角点测试:")
        for a1, a2, a3 in corner_points:
            A1, A2, A3 = np.array([a1]), np.array([a2]), np.array([a3])
            y = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
            expected = YY[a3, a2, a1]  # 直接从矩阵中获取
            print(f"  ({a1}, {a2}, {a3}): y = {y[0]:.1f}, 期望 = {expected:.1f}")
        
        # 测试中心点
        print("\n中心点测试:")
        A1, A2, A3 = np.array([0.5]), np.array([0.5]), np.array([0.5])
        y_center = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
        expected_center = np.mean(YY)  # 中心点应该接近所有值的平均值
        print(f"  中心点 (0.5, 0.5, 0.5): y = {y_center[0]:.4f}, 期望≈{expected_center:.4f}")
        
        print("✓ 三维插值边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 三维插值边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_3d_multiple_points():
    """测试多点同时插值"""
    print("\n" + "=" * 60)
    print("测试三维插值多点同时处理")
    print("=" * 60)
    
    try:
        # 创建较大的3D网格
        xx1 = np.linspace(0, 2, 3)
        xx2 = np.linspace(0, 2, 3)
        xx3 = np.linspace(0, 2, 3)
        
        # 创建3x3x3矩阵，使用线性函数
        YY = np.zeros((3, 3, 3))
        for i3, x3 in enumerate(xx3):
            for i2, x2 in enumerate(xx2):
                for i1, x1 in enumerate(xx1):
                    YY[i3, i2, i1] = x1 + 2*x2 + 3*x3
        
        print(f"网格: xx1={xx1}, xx2={xx2}, xx3={xx3}")
        print(f"值矩阵形状: {YY.shape}")
        
        # 测试多个点同时插值
        A1 = np.array([0.5, 1.0, 1.5])
        A2 = np.array([0.3, 0.8, 1.2])
        A3 = np.array([0.7, 1.1, 1.6])
        
        print(f"\n多点插值测试:")
        print(f"A1: {A1}")
        print(f"A2: {A2}")
        print(f"A3: {A3}")
        
        y_multi = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
        expected_multi = A1 + 2*A2 + 3*A3  # 线性函数的期望值
        
        print(f"插值结果: {y_multi}")
        print(f"期望结果: {expected_multi}")
        print(f"误差: {np.abs(y_multi - expected_multi)}")
        
        # 测试2D数组输入
        print(f"\n2D数组输入测试:")
        A1_2d = np.array([[0.5, 1.0], [1.5, 0.8]])
        A2_2d = np.array([[0.3, 0.8], [1.2, 0.6]])
        A3_2d = np.array([[0.7, 1.1], [1.6, 0.9]])
        
        y_2d = dpm_interpn(xx1, xx2, xx3, YY, A1_2d, A2_2d, A3_2d)
        expected_2d = A1_2d + 2*A2_2d + 3*A3_2d
        
        print(f"2D输入形状: {A1_2d.shape}")
        print(f"2D输出形状: {y_2d.shape}")
        print(f"2D插值结果:\n{y_2d}")
        print(f"2D期望结果:\n{expected_2d}")
        print(f"2D误差:\n{np.abs(y_2d - expected_2d)}")
        
        print("✓ 三维插值多点处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 三维插值多点处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_3d_edge_cases():
    """测试三维插值的特殊情况"""
    print("\n" + "=" * 60)
    print("测试三维插值特殊情况")
    print("=" * 60)
    
    try:
        # 创建测试数据
        xx1 = np.array([0, 1, 2, 3])
        xx2 = np.array([0, 1, 2])
        xx3 = np.array([0, 1])
        
        # 创建随机3D矩阵
        np.random.seed(42)  # 确保可重复性
        YY = np.random.rand(2, 3, 4) * 10
        
        print(f"网格大小: {len(xx1)} x {len(xx2)} x {len(xx3)}")
        print(f"值矩阵形状: {YY.shape}")
        
        # 测试1: 边界外的点
        print("\n测试1: 边界外的点")
        A1_out = np.array([-0.5, 3.5])
        A2_out = np.array([0.5, 0.5])
        A3_out = np.array([0.5, 0.5])
        
        y_out = dpm_interpn(xx1, xx2, xx3, YY, A1_out, A2_out, A3_out)
        print(f"边界外插值结果: {y_out}")
        
        # 测试2: 单点插值
        print("\n测试2: 单点插值")
        A1_single = np.array([1.5])
        A2_single = np.array([1.2])
        A3_single = np.array([0.7])
        
        y_single = dpm_interpn(xx1, xx2, xx3, YY, A1_single, A2_single, A3_single)
        print(f"单点插值结果: {y_single}")
        
        # 测试3: 网格点精确匹配
        print("\n测试3: 网格点精确匹配")
        A1_exact = np.array([1.0, 2.0])
        A2_exact = np.array([1.0, 2.0])
        A3_exact = np.array([0.0, 1.0])
        
        y_exact = dpm_interpn(xx1, xx2, xx3, YY, A1_exact, A2_exact, A3_exact)
        expected_exact = [YY[0, 1, 1], YY[1, 2, 2]]
        
        print(f"精确网格点插值结果: {y_exact}")
        print(f"期望结果: {expected_exact}")
        print(f"误差: {np.abs(y_exact - expected_exact)}")
        
        print("✓ 三维插值特殊情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 三维插值特殊情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("三维插值函数专项测试程序")
    print("这个程序专门测试 dpm_interpn 的三维插值功能")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 基本三维插值
    if test_3d_basic_interpolation():
        success_count += 1
    
    # 测试2: 边界情况
    if test_3d_boundary_cases():
        success_count += 1
    
    # 测试3: 多点处理
    if test_3d_multiple_points():
        success_count += 1
    
    # 测试4: 特殊情况
    if test_3d_edge_cases():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有三维插值测试都通过了！函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查三维插值实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
