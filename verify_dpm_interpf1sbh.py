#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 dpm_interpf1sbh 函数的脚本

这个脚本快速验证 dpm_interpf1sbh 函数的基本功能
"""

import numpy as np
from dpm import dpm_interpf1sbh


def main():
    """主验证函数"""
    print("快速验证 dpm_interpf1sbh 函数")
    print("=" * 40)
    
    try:
        # 创建简单的测试设置
        xx = np.array([0, 1, 2, 3, 4])
        yy = np.array([0, 1, 4, 9, 16])  # y = x^2
        lim = np.array([1.5, 3.5])  # 边界限制
        
        print(f"输入网格 xx: {xx}")
        print(f"输入值 yy: {yy}")
        print(f"边界限制 lim: {lim}")
        
        # 测试不同区域的点
        test_cases = [
            (0.5, "下边界以下"),
            (1.5, "下边界上"),
            (1.8, "下边界和网格之间"),
            (2.5, "常规网格内"),
            (3.2, "网格和上边界之间"),
            (3.5, "上边界上"),
            (4.5, "上边界以上")
        ]
        
        print("\n插值测试:")
        for a, description in test_cases:
            y = dpm_interpf1sbh(xx, yy, a, lim)
            expected = a**2  # 理论期望值
            print(f"  a = {a:.1f}: y = {y:.4f}, 期望≈{expected:.4f} ({description})")
        
        print("\n验证完成!")
        
        # 简单的正确性检查
        # 检查边界外的点是否使用边界值
        y_below = dpm_interpf1sbh(xx, yy, 0.5, lim)  # 应该使用下边界对应的值
        y_above = dpm_interpf1sbh(xx, yy, 4.5, lim)  # 应该使用上边界对应的值
        
        print(f"\n正确性检查:")
        print(f"下边界外 (a=0.5): y = {y_below:.4f}")
        print(f"上边界外 (a=4.5): y = {y_above:.4f}")
        
        # 检查连续性（在边界附近）
        y_before_lower = dpm_interpf1sbh(xx, yy, 1.49, lim)
        y_at_lower = dpm_interpf1sbh(xx, yy, 1.5, lim)
        y_after_lower = dpm_interpf1sbh(xx, yy, 1.51, lim)
        
        print(f"\n下边界连续性检查:")
        print(f"边界前 (a=1.49): y = {y_before_lower:.6f}")
        print(f"边界上 (a=1.50): y = {y_at_lower:.6f}")
        print(f"边界后 (a=1.51): y = {y_after_lower:.6f}")
        
        print("✓ 基本功能验证完成")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
