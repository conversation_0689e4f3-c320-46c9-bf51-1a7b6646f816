#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 DPM 辅助函数的脚本

这个脚本测试 get_size, input_check_grd, notify_user_of_error, clear_waitbars 函数
"""

import numpy as np
from dpm import (get_size, input_check_grd, notify_user_of_error, clear_waitbars,
                 Grid, Problem, Options)


def test_get_size():
    """测试 get_size 函数"""
    print("=" * 60)
    print("测试 get_size 函数")
    print("=" * 60)
    
    try:
        # 测试1: 基本功能
        print("测试1: 基本功能")
        current_grd = {
            'X': {
                1: [0, 1, 2, 3, 4],      # 5个点
                2: [0, 1, 2],            # 3个点
                3: [0, 1]                # 2个点
            }
        }
        
        sze = get_size(current_grd)
        expected = [5, 3, 2]
        
        print(f"  输入网格: {current_grd['X']}")
        print(f"  结果: {sze}")
        print(f"  期望: {expected}")
        print(f"  匹配: {sze == expected}")
        
        # 测试2: 空网格
        print("\n测试2: 空网格")
        empty_grd = {'X': {}}
        sze_empty = get_size(empty_grd)
        print(f"  空网格结果: {sze_empty}")
        print(f"  应该是空列表: {sze_empty == []}")
        
        # 测试3: 缺少X键
        print("\n测试3: 缺少X键")
        no_x_grd = {'Y': {1: [0, 1]}}
        sze_no_x = get_size(no_x_grd)
        print(f"  无X键结果: {sze_no_x}")
        print(f"  应该是空列表: {sze_no_x == []}")
        
        # 测试4: 标量值
        print("\n测试4: 标量值")
        scalar_grd = {
            'X': {
                1: [0, 1, 2],
                2: 5  # 标量值
            }
        }
        sze_scalar = get_size(scalar_grd)
        print(f"  标量网格结果: {sze_scalar}")
        print(f"  期望: [3, 1]")
        
        print("✓ get_size 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ get_size 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_input_check_grd():
    """测试 input_check_grd 函数"""
    print("\n" + "=" * 60)
    print("测试 input_check_grd 函数")
    print("=" * 60)
    
    try:
        # 测试1: 基本网格检查
        print("测试1: 基本网格检查")
        grd = Grid()
        
        # 设置状态网格（长度不正确）
        grd.Nx = {1: [5], 2: [3, 4]}  # 第一个太短，第二个长度正确
        grd.Xn = {
            1: {'lo': [0.0], 'hi': [1.0]},  # 太短
            2: {'lo': [0.0, 0.0, 0.0], 'hi': [1.0, 1.0, 1.0]}  # 正确长度
        }
        
        # 设置输入网格
        grd.Nu = {1: [7, 8]}  # 长度正确
        grd.Un = {1: {'lo': [-1.0], 'hi': [1.0]}}  # 太短
        
        T = 2  # 2个时间步
        
        print(f"  修正前:")
        print(f"    grd.Nx[1]: {grd.Nx[1]} (应该有{T+1}个元素)")
        print(f"    grd.Nx[2]: {grd.Nx[2]} (应该有{T+1}个元素)")
        print(f"    grd.Xn[1]['lo']: {grd.Xn[1]['lo']} (应该有{T+1}个元素)")
        print(f"    grd.Nu[1]: {grd.Nu[1]} (应该有{T}个元素)")
        print(f"    grd.Un[1]['lo']: {grd.Un[1]['lo']} (应该有{T}个元素)")
        
        # 调用检查函数
        grd_checked = input_check_grd(grd, T)
        
        print(f"  修正后:")
        print(f"    grd.Nx[1]: {grd_checked.Nx[1]}")
        print(f"    grd.Nx[2]: {grd_checked.Nx[2]}")
        print(f"    grd.Xn[1]['lo']: {grd_checked.Xn[1]['lo']}")
        print(f"    grd.Nu[1]: {grd_checked.Nu[1]}")
        print(f"    grd.Un[1]['lo']: {grd_checked.Un[1]['lo']}")
        
        # 验证长度
        assert len(grd_checked.Nx[1]) == T + 1, f"Nx[1] 长度错误: {len(grd_checked.Nx[1])}"
        assert len(grd_checked.Nx[2]) == T + 1, f"Nx[2] 长度错误: {len(grd_checked.Nx[2])}"
        assert len(grd_checked.Xn[1]['lo']) == T + 1, f"Xn[1]['lo'] 长度错误"
        assert len(grd_checked.Nu[1]) == T, f"Nu[1] 长度错误: {len(grd_checked.Nu[1])}"
        assert len(grd_checked.Un[1]['lo']) == T, f"Un[1]['lo'] 长度错误"
        
        # 测试2: 截断过长的数组
        print("\n测试2: 截断过长的数组")
        grd2 = Grid()
        grd2.Nx = {1: [5, 6, 7, 8, 9]}  # 太长
        grd2.Nu = {1: [3, 4, 5, 6]}     # 太长
        
        T2 = 2
        print(f"  修正前: Nx[1] = {grd2.Nx[1]} (长度 {len(grd2.Nx[1])})")
        print(f"  修正前: Nu[1] = {grd2.Nu[1]} (长度 {len(grd2.Nu[1])})")
        
        grd2_checked = input_check_grd(grd2, T2)
        
        print(f"  修正后: Nx[1] = {grd2_checked.Nx[1]} (长度 {len(grd2_checked.Nx[1])})")
        print(f"  修正后: Nu[1] = {grd2_checked.Nu[1]} (长度 {len(grd2_checked.Nu[1])})")
        
        assert len(grd2_checked.Nx[1]) == T2 + 1, "截断失败"
        assert len(grd2_checked.Nu[1]) == T2, "截断失败"
        
        print("✓ input_check_grd 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ input_check_grd 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_notify_user_of_error():
    """测试 notify_user_of_error 函数"""
    print("\n" + "=" * 60)
    print("测试 notify_user_of_error 函数")
    print("=" * 60)
    
    try:
        # 测试1: 基本错误通知
        print("测试1: 基本错误通知")
        
        try:
            # 故意创建一个错误
            result = 1 / 0
        except ZeroDivisionError as e:
            print("  捕获到除零错误，调用 notify_user_of_error:")
            notify_user_of_error(e)
        
        # 测试2: 自定义错误
        print("\n测试2: 自定义错误")
        custom_error = ValueError("这是一个测试错误消息")
        print("  调用 notify_user_of_error 处理自定义错误:")
        notify_user_of_error(custom_error)
        
        print("✓ notify_user_of_error 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ notify_user_of_error 函数测试失败: {str(e)}")
        return False


def test_clear_waitbars():
    """测试 clear_waitbars 函数"""
    print("\n" + "=" * 60)
    print("测试 clear_waitbars 函数")
    print("=" * 60)
    
    try:
        print("测试1: 基本清理功能")
        print("  调用 clear_waitbars()...")
        
        # 模拟一些进度输出
        print("  模拟进度显示: ", end="")
        for i in range(5):
            print(f"{i*20}%", end=" ")
        
        # 调用清理函数
        clear_waitbars()
        
        print("  清理完成")
        
        print("✓ clear_waitbars 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ clear_waitbars 函数测试失败: {str(e)}")
        return False


def test_integration():
    """集成测试：测试函数之间的协作"""
    print("\n" + "=" * 60)
    print("集成测试：函数协作")
    print("=" * 60)
    
    try:
        # 创建一个完整的网格检查流程
        print("测试1: 完整的网格处理流程")
        
        # 创建网格
        grd = Grid()
        grd.Nx = {1: [10], 2: [5, 6]}
        grd.Xn = {1: {'lo': [0.0], 'hi': [1.0]}}
        grd.Nu = {1: [7]}
        grd.Un = {1: {'lo': [-1.0], 'hi': [1.0]}}
        
        T = 3
        
        # 检查网格
        grd_checked = input_check_grd(grd, T)
        
        # 创建当前网格
        current_grd = {
            'X': {
                1: np.linspace(0, 1, grd_checked.Nx[1][0]),
                2: np.linspace(0, 1, grd_checked.Nx[2][0])
            }
        }
        
        # 获取大小
        sze = get_size(current_grd)
        
        print(f"  网格大小: {sze}")
        print(f"  期望大小: [{grd_checked.Nx[1][0]}, {grd_checked.Nx[2][0]}]")
        
        # 清理
        clear_waitbars()
        
        print("✓ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {str(e)}")
        notify_user_of_error(e)
        return False


def main():
    """主测试函数"""
    print("DPM 辅助函数测试程序")
    print("这个程序测试 get_size, input_check_grd, notify_user_of_error, clear_waitbars 函数")
    
    success_count = 0
    total_tests = 5
    
    # 测试1: get_size
    if test_get_size():
        success_count += 1
    
    # 测试2: input_check_grd
    if test_input_check_grd():
        success_count += 1
    
    # 测试3: notify_user_of_error
    if test_notify_user_of_error():
        success_count += 1
    
    # 测试4: clear_waitbars
    if test_clear_waitbars():
        success_count += 1
    
    # 测试5: 集成测试
    if test_integration():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有辅助函数测试都通过了！")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
