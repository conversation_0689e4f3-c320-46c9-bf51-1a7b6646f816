#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpf2sbh 函数的脚本

这个脚本测试 dpm_interpf2sbh 函数的各种功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from dpm import dpm_interpf2sbh


def test_basic_2d_sbh_interpolation():
    """测试基本的二维边界处理插值功能"""
    print("=" * 60)
    print("测试 dpm_interpf2sbh 基本二维边界处理插值功能")
    print("=" * 60)
    
    try:
        # 创建二维测试数据
        xx1 = np.linspace(0, 4, 5)  # [0, 1, 2, 3, 4]
        xx2 = np.linspace(0, 3, 4)  # [0, 1, 2, 3]
        
        # 创建二维值矩阵 (4x5矩阵，对应xx2 x xx1)
        X1, X2 = np.meshgrid(xx1, xx2, indexing='ij')
        YY = X1**2 + X2**2  # 简单的二次函数
        YY = YY.T  # 转置以匹配MATLAB的列优先顺序
        
        # 设置边界矩阵 (2 x length(xx2))
        # 下边界随xx2变化: [1.0, 1.5, 2.0, 2.5]
        # 上边界随xx2变化: [3.0, 3.5, 4.0, 4.5]
        lim = np.array([[1.0, 1.5, 2.0, 2.5],    # 下边界
                        [3.0, 3.5, 4.0, 4.5]])   # 上边界
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        print(f"边界矩阵 lim 形状: {lim.shape}")
        print(f"边界矩阵 lim:\n{lim}")
        
        # 测试不同的插值点
        test_cases = [
            (2.0, 1.0, "常规情况"),
            (0.5, 1.5, "第一维在下边界以下"),
            (4.5, 2.0, "第一维在上边界以上"),
            (2.5, 0.5, "第二维在网格内"),
            (3.2, 2.8, "边界内插值"),
        ]
        
        print("\n插值测试:")
        for a1, a2, description in test_cases:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim)
            expected = a1**2 + a2**2  # 理论期望值
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}, 期望≈{expected:.4f} ({description})")
        
        print("✓ dpm_interpf2sbh 基本二维边界处理插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2sbh 基本二维边界处理插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_variations_2d():
    """测试不同边界变化的情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf2sbh 不同边界变化情况")
    print("=" * 60)
    
    try:
        # 测试1: 常数边界
        print("测试1: 常数边界")
        xx1 = np.array([0, 1, 2, 3])
        xx2 = np.array([0, 1, 2])
        YY = np.array([[0, 1, 4, 9],
                       [1, 2, 5, 10],
                       [4, 5, 8, 13]])  # 3x4矩阵
        
        # 常数边界
        lim_const = np.array([[1.0, 1.0, 1.0],    # 下边界常数
                              [2.5, 2.5, 2.5]])   # 上边界常数
        
        test_points = [(0.5, 1.0), (1.5, 1.5), (3.0, 0.5)]
        for a1, a2 in test_points:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim_const)
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}")
        
        # 测试2: 线性变化边界
        print("\n测试2: 线性变化边界")
        lim_linear = np.array([[0.5, 1.0, 1.5],    # 下边界线性增加
                               [2.0, 2.5, 3.0]])   # 上边界线性增加
        
        for a1, a2 in test_points:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim_linear)
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}")
        
        # 测试3: 非单调边界
        print("\n测试3: 非单调边界")
        lim_nonmono = np.array([[1.0, 0.5, 1.5],    # 下边界非单调
                                [2.5, 3.0, 2.0]])   # 上边界非单调
        
        for a1, a2 in test_points:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim_nonmono)
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}")
        
        print("✓ dpm_interpf2sbh 不同边界变化情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2sbh 不同边界变化情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases_2d_sbh():
    """测试二维边界处理的边界情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf2sbh 边界情况")
    print("=" * 60)
    
    try:
        # 测试1: 最小网格
        print("测试1: 最小网格 (2x2)")
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        YY = np.array([[0, 1],
                       [1, 2]])  # 2x2矩阵
        
        lim_small = np.array([[0.3, 0.3],    # 下边界
                              [0.7, 0.7]])   # 上边界
        
        test_points_small = [(0.2, 0.5), (0.5, 0.5), (0.8, 0.5)]
        for a1, a2 in test_points_small:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim_small)
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}")
        
        # 测试2: 边界重合
        print("\n测试2: 边界重合")
        lim_same = np.array([[0.5, 0.5],    # 下边界
                             [0.5, 0.5]])   # 上边界（与下边界相同）
        
        try:
            y = dpm_interpf2sbh(xx1, xx2, YY, 0.5, 0.5, lim_same)
            print(f"  边界重合时: y = {y:.4f}")
        except Exception as e:
            print(f"  边界重合时出错: {e}")
        
        # 测试3: 边界外插值
        print("\n测试3: 边界外插值")
        xx1_large = np.array([0, 1, 2, 3, 4])
        xx2_large = np.array([0, 1, 2, 3])
        YY_large = np.random.rand(4, 5) * 10  # 随机矩阵
        
        lim_narrow = np.array([[1.5, 1.5, 1.5, 1.5],    # 窄边界
                               [2.5, 2.5, 2.5, 2.5]])
        
        test_points_out = [(0.5, 1.5), (3.5, 2.0), (2.0, 3.5)]
        for a1, a2 in test_points_out:
            y = dpm_interpf2sbh(xx1_large, xx2_large, YY_large, a1, a2, lim_narrow)
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}")
        
        print("✓ dpm_interpf2sbh 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2sbh 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_with_visualization_2d_sbh():
    """测试并可视化二维边界处理插值结果"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf2sbh 并可视化")
    print("=" * 60)
    
    try:
        # 创建测试数据
        xx1 = np.linspace(0, 5, 6)
        xx2 = np.linspace(0, 4, 5)
        
        # 创建有趣的二维函数
        X1, X2 = np.meshgrid(xx1, xx2, indexing='ij')
        YY = np.sin(X1) * np.cos(X2) + 0.1 * X1 * X2
        YY = YY.T  # 转置以匹配期望的格式
        
        # 设置变化的边界
        lim = np.array([[1.5, 2.0, 2.5, 3.0, 3.5],    # 下边界递增
                        [3.5, 4.0, 4.5, 5.0, 4.5]])   # 上边界先增后减
        
        # 创建测试点网格
        a1_test = np.linspace(0, 6, 30)
        a2_test = np.linspace(0, 4, 25)
        
        # 计算插值结果
        results = np.zeros((len(a2_test), len(a1_test)))
        for i, a2 in enumerate(a2_test):
            for j, a1 in enumerate(a1_test):
                try:
                    results[i, j] = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim)
                except:
                    results[i, j] = np.nan
        
        # 创建可视化
        try:
            fig = plt.figure(figsize=(15, 10))
            
            # 子图1: 原始数据
            ax1 = fig.add_subplot(221, projection='3d')
            X1_orig, X2_orig = np.meshgrid(xx1, xx2, indexing='ij')
            ax1.plot_surface(X1_orig, X2_orig, YY.T, alpha=0.7, cmap='viridis')
            ax1.set_title('原始数据')
            ax1.set_xlabel('X1')
            ax1.set_ylabel('X2')
            ax1.set_zlabel('Y')
            
            # 子图2: 插值结果
            ax2 = fig.add_subplot(222)
            A1_grid, A2_grid = np.meshgrid(a1_test, a2_test, indexing='ij')
            valid_mask = ~np.isnan(results)
            im = ax2.contourf(A1_grid.T, A2_grid.T, results, levels=20, cmap='viridis')
            plt.colorbar(im, ax=ax2)
            ax2.set_title('插值结果')
            ax2.set_xlabel('A1')
            ax2.set_ylabel('A2')
            
            # 子图3: 边界可视化
            ax3 = fig.add_subplot(223)
            ax3.plot(xx2, lim[0, :], 'g--', linewidth=2, label='下边界')
            ax3.plot(xx2, lim[1, :], 'r--', linewidth=2, label='上边界')
            ax3.fill_between(xx2, lim[0, :], lim[1, :], alpha=0.3, color='yellow', label='可行区域')
            ax3.set_xlabel('X2')
            ax3.set_ylabel('X1边界')
            ax3.set_title('边界变化')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 子图4: 切片比较
            ax4 = fig.add_subplot(224)
            # 在a2=2.0处的切片
            a2_slice = 2.0
            y_slice = []
            for a1 in a1_test:
                try:
                    y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2_slice, lim)
                    y_slice.append(y)
                except:
                    y_slice.append(np.nan)
            
            ax4.plot(a1_test, y_slice, 'b-', linewidth=2, label=f'插值结果 (a2={a2_slice})')
            
            # 绘制对应的边界
            lim2_lower = np.interp(a2_slice, xx2, lim[0, :])
            lim2_upper = np.interp(a2_slice, xx2, lim[1, :])
            ax4.axvline(x=lim2_lower, color='g', linestyle='--', label=f'下边界 ({lim2_lower:.2f})')
            ax4.axvline(x=lim2_upper, color='r', linestyle='--', label=f'上边界 ({lim2_upper:.2f})')
            
            ax4.set_xlabel('A1')
            ax4.set_ylabel('Y')
            ax4.set_title(f'一维切片 (A2={a2_slice})')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('dpm_interpf2sbh_test.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            print("✓ 可视化图表已保存为 'dpm_interpf2sbh_test.png'")
            
        except Exception as plot_error:
            print(f"可视化失败（但插值成功）: {plot_error}")
        
        # 验证一些关键点
        print("\n关键点验证:")
        key_points = [(1.0, 2.0), (3.0, 2.0), (5.0, 2.0)]
        for a1, a2 in key_points:
            y = dpm_interpf2sbh(xx1, xx2, YY, a1, a2, lim)
            # 计算对应的边界
            lim2_lower = np.interp(a2, xx2, lim[0, :])
            lim2_upper = np.interp(a2, xx2, lim[1, :])
            status = "边界内" if lim2_lower <= a1 <= lim2_upper else "边界外"
            print(f"  (a1={a1:.1f}, a2={a2:.1f}): y = {y:.4f}, 边界=[{lim2_lower:.2f}, {lim2_upper:.2f}] ({status})")
        
        print("✓ dpm_interpf2sbh 可视化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2sbh 可视化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("dpm_interpf2sbh 函数测试程序")
    print("这个程序测试二维边界处理插值函数的各种功能")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 基本二维边界处理插值
    if test_basic_2d_sbh_interpolation():
        success_count += 1
    
    # 测试2: 不同边界变化情况
    if test_boundary_variations_2d():
        success_count += 1
    
    # 测试3: 边界情况
    if test_edge_cases_2d_sbh():
        success_count += 1
    
    # 测试4: 可视化测试
    if test_with_visualization_2d_sbh():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！dpm_interpf2sbh 函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
