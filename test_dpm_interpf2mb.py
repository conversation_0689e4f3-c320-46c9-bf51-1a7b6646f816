#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpf2mb 函数的脚本

这个脚本测试 dpm_interpf2mb 函数的各种功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from dpm import dpm_interpf2mb


def test_basic_2d_interpolation():
    """测试基本二维插值功能"""
    print("=" * 60)
    print("测试 dpm_interpf2mb 基本二维插值功能")
    print("=" * 60)
    
    try:
        # 创建二维测试数据
        xx1 = np.linspace(0, 4, 5)  # [0, 1, 2, 3, 4]
        xx2 = np.linspace(0, 3, 4)  # [0, 1, 2, 3]
        
        # 创建二维值矩阵 (4x5矩阵，对应xx2 x xx1)
        X1, X2 = np.meshgrid(xx1, xx2, indexing='ij')
        YY = X1**2 + X2**2  # 简单的二次函数
        YY = YY.T  # 转置以匹配MATLAB的列优先顺序
        
        # 设置边界 (2 x length(xx2))
        xlim = np.array([[1.5, 1.5, 1.5, 1.5],    # 下边界
                         [3.5, 3.5, 3.5, 3.5]])   # 上边界
        
        # 对应的边界值
        ylim = np.array([[1.5**2 + 0**2, 1.5**2 + 1**2, 1.5**2 + 2**2, 1.5**2 + 3**2],  # 下边界值
                         [3.5**2 + 0**2, 3.5**2 + 1**2, 3.5**2 + 2**2, 3.5**2 + 3**2]])  # 上边界值
        
        # 测试插值点
        A1 = np.array([0.5, 2.0, 2.5, 4.5])  # 包含边界内外的点
        A2 = np.array([0.5, 1.0, 1.5, 2.0])  # 对应的第二维坐标
        
        myInf = 1e6
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        print(f"边界 xlim 形状: {xlim.shape}")
        print(f"边界值 ylim 形状: {ylim.shape}")
        print(f"插值点 A1: {A1}")
        print(f"插值点 A2: {A2}")
        
        # 调用插值函数
        y = dpm_interpf2mb(xx1, xx2, YY, A1, A2, xlim, ylim, myInf)
        
        print(f"插值结果 y: {y}")
        
        # 验证结果
        for i, (a1, a2, y_val) in enumerate(zip(A1, A2, y)):
            expected = a1**2 + a2**2
            status = "边界内" if (1.5 <= a1 <= 3.5) else "边界外"
            print(f"  A1[{i}] = {a1:.1f}, A2[{i}] = {a2:.1f}, y[{i}] = {y_val:.2f}, 期望≈{expected:.2f} ({status})")
        
        print("✓ dpm_interpf2mb 基本二维插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2mb 基本二维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_cases_2d():
    """测试二维边界情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf2mb 二维边界情况")
    print("=" * 60)
    
    try:
        # 测试1: 简单的3x3网格
        print("测试1: 简单的3x3网格")
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1, 2])
        YY = np.array([[0, 1, 4],
                       [1, 2, 5],
                       [4, 5, 8]])  # 3x3矩阵
        
        xlim = np.array([[0.5, 0.5, 0.5],    # 下边界
                         [1.5, 1.5, 1.5]])   # 上边界
        ylim = np.array([[0.5, 1.5, 4.5],   # 下边界值
                         [1.5, 2.5, 5.5]])  # 上边界值
        
        A1 = np.array([0.2, 1.0, 1.8])  # 边界外、内、外
        A2 = np.array([1.0, 1.0, 1.0])  # 都在第二维的中间
        myInf = 999
        
        y1 = dpm_interpf2mb(xx1, xx2, YY, A1, A2, xlim, ylim, myInf)
        print(f"  结果: {y1}")
        
        # 测试2: 单点插值
        print("测试2: 单点插值")
        A1_single = np.array([1.0])
        A2_single = np.array([1.0])
        y2 = dpm_interpf2mb(xx1, xx2, YY, A1_single, A2_single, xlim, ylim, myInf)
        print(f"  单点结果: {y2}")
        
        # 测试3: 多维数组输入
        print("测试3: 多维数组输入")
        A1_2d = np.array([[0.8, 1.2], [1.0, 1.4]])
        A2_2d = np.array([[1.0, 1.0], [0.5, 1.5]])
        y3 = dpm_interpf2mb(xx1, xx2, YY, A1_2d, A2_2d, xlim, ylim, myInf)
        print(f"  2D输入形状: {A1_2d.shape}")
        print(f"  2D输出形状: {y3.shape}")
        print(f"  2D结果: {y3}")
        
        print("✓ dpm_interpf2mb 二维边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2mb 二维边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_with_visualization_2d():
    """测试并可视化二维插值结果"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf2mb 并可视化")
    print("=" * 60)
    
    try:
        # 创建更详细的测试数据
        xx1 = np.linspace(0, 5, 6)
        xx2 = np.linspace(0, 4, 5)
        
        # 创建有趣的二维函数
        X1, X2 = np.meshgrid(xx1, xx2, indexing='ij')
        YY = np.sin(X1) * np.cos(X2) + 0.1 * X1 * X2
        YY = YY.T  # 转置以匹配期望的格式
        
        # 设置边界
        xlim = np.array([[2.0, 2.0, 2.0, 2.0, 2.0],    # 下边界
                         [4.0, 4.0, 4.0, 4.0, 4.0]])   # 上边界
        
        # 计算边界值
        ylim = np.zeros((2, 5))
        for i in range(5):
            ylim[0, i] = np.sin(2.0) * np.cos(xx2[i]) + 0.1 * 2.0 * xx2[i]  # 下边界值
            ylim[1, i] = np.sin(4.0) * np.cos(xx2[i]) + 0.1 * 4.0 * xx2[i]  # 上边界值
        
        # 创建测试点网格
        A1_test = np.linspace(1, 6, 20)
        A2_test = np.linspace(0, 4, 20)
        A1_grid, A2_grid = np.meshgrid(A1_test, A2_test, indexing='ij')
        
        myInf = 10.0  # 使用较小的无穷大值以便可视化
        
        # 调用插值函数
        y_grid = dpm_interpf2mb(xx1, xx2, YY, A1_grid, A2_grid, xlim, ylim, myInf)
        
        # 创建可视化
        try:
            fig = plt.figure(figsize=(15, 5))
            
            # 子图1: 原始数据
            ax1 = fig.add_subplot(131, projection='3d')
            X1_orig, X2_orig = np.meshgrid(xx1, xx2, indexing='ij')
            ax1.plot_surface(X1_orig, X2_orig, YY.T, alpha=0.7, cmap='viridis')
            ax1.set_title('原始数据')
            ax1.set_xlabel('X1')
            ax1.set_ylabel('X2')
            ax1.set_zlabel('Y')
            
            # 子图2: 插值结果
            ax2 = fig.add_subplot(132, projection='3d')
            valid_mask = y_grid < myInf
            ax2.scatter(A1_grid[valid_mask], A2_grid[valid_mask], y_grid[valid_mask], 
                       c=y_grid[valid_mask], cmap='viridis', s=10)
            ax2.set_title('插值结果（有效区域）')
            ax2.set_xlabel('A1')
            ax2.set_ylabel('A2')
            ax2.set_zlabel('Y')
            
            # 子图3: 边界可视化
            ax3 = fig.add_subplot(133)
            # 绘制边界
            ax3.fill_between(xx2, xlim[0, :], xlim[1, :], alpha=0.3, color='green', label='可行区域')
            ax3.plot(xx2, xlim[0, :], 'g--', label='下边界')
            ax3.plot(xx2, xlim[1, :], 'g--', label='上边界')
            
            # 绘制测试点
            valid_points = y_grid < myInf
            invalid_points = y_grid >= myInf
            ax3.scatter(A2_grid[valid_points], A1_grid[valid_points], 
                       c='blue', s=5, alpha=0.5, label='有效插值点')
            ax3.scatter(A2_grid[invalid_points], A1_grid[invalid_points], 
                       c='red', s=5, alpha=0.5, label='无效插值点')
            
            ax3.set_xlabel('A2')
            ax3.set_ylabel('A1')
            ax3.set_title('边界和插值点分布')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('dpm_interpf2mb_test.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            print("✓ 可视化图表已保存为 'dpm_interpf2mb_test.png'")
            
        except Exception as plot_error:
            print(f"可视化失败（但插值成功）: {plot_error}")
        
        # 验证一些关键点
        test_points_1 = np.array([1.5, 3.0, 4.5])  # 边界外、内、外
        test_points_2 = np.array([2.0, 2.0, 2.0])  # 都在第二维的同一位置
        test_results = dpm_interpf2mb(xx1, xx2, YY, test_points_1, test_points_2, xlim, ylim, myInf)
        
        print("关键点验证:")
        for i, (p1, p2, result) in enumerate(zip(test_points_1, test_points_2, test_results)):
            status = "边界外" if (p1 < 2.0 or p1 > 4.0) else "边界内"
            print(f"  (A1={p1:.1f}, A2={p2:.1f}): y = {result:.4f} ({status})")
        
        print("✓ dpm_interpf2mb 可视化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf2mb 可视化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("dpm_interpf2mb 函数测试程序")
    print("这个程序测试二维边界插值函数的各种功能")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 基本二维插值
    if test_basic_2d_interpolation():
        success_count += 1
    
    # 测试2: 边界情况
    if test_boundary_cases_2d():
        success_count += 1
    
    # 测试3: 可视化测试
    if test_with_visualization_2d():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！dpm_interpf2mb 函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
