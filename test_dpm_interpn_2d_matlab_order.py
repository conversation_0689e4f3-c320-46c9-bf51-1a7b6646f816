#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的dpm_interpn二维插值函数
验证参数顺序与MATLAB的dpm_interpn保持一致

MATLAB参数顺序: dpm_interpn(xx2, xx1, YY, A2, A1)
Python参数顺序: dpm_interpn(xx2, xx1, YY, A2, A1)
"""

import numpy as np
import matplotlib.pyplot as plt
from dpm import dpm_interpn


def test_basic_2d_interpolation():
    """测试基本的二维插值功能"""
    print("=" * 60)
    print("测试基本二维插值功能（MATLAB参数顺序）")
    print("=" * 60)
    
    try:
        # 创建简单的二维网格
        xx1 = np.array([0, 1, 2])  # 第一维网格
        xx2 = np.array([0, 1])     # 第二维网格
        
        # 创建二维值矩阵 YY[i,j] = xx1[j]^2 + xx2[i]^2
        # 注意：YY的形状是 (len(xx2), len(xx1))
        YY = np.zeros((len(xx2), len(xx1)))
        for i in range(len(xx2)):
            for j in range(len(xx1)):
                YY[i, j] = xx1[j]**2 + xx2[i]**2
        
        print(f"xx1 (第一维): {xx1}")
        print(f"xx2 (第二维): {xx2}")
        print(f"YY 矩阵形状: {YY.shape}")
        print(f"YY 矩阵:\n{YY}")
        
        # 测试插值点
        A1 = np.array([0.5, 1.5])  # 第一维查询点
        A2 = np.array([0.3, 0.7])  # 第二维查询点
        
        print(f"\n查询点:")
        print(f"A1 (第一维): {A1}")
        print(f"A2 (第二维): {A2}")
        
        # 使用新的参数顺序调用：dpm_interpn(xx2, xx1, YY, A2, A1)
        result = dpm_interpn(xx2, xx1, YY, A2, A1)
        
        print(f"\n插值结果: {result}")
        
        # 验证结果：期望值应该是 A1^2 + A2^2
        expected = A1**2 + A2**2
        print(f"期望结果: {expected}")
        
        # 计算误差
        error = np.abs(result - expected)
        print(f"误差: {error}")
        
        # 检查误差是否在可接受范围内
        max_error = np.max(error)
        print(f"最大误差: {max_error}")
        
        if max_error < 1e-10:
            print("✓ 基本二维插值测试通过")
            return True
        else:
            print("✗ 基本二维插值测试失败：误差过大")
            return False
            
    except Exception as e:
        print(f"✗ 基本二维插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_matlab_consistency():
    """测试与MATLAB的一致性"""
    print("\n" + "=" * 60)
    print("测试与MATLAB dpm_interpn的一致性")
    print("=" * 60)
    
    try:
        # 使用与MATLAB测试相同的数据
        xx1 = np.array([0, 1, 2, 3])
        xx2 = np.array([0, 1, 2])
        
        # 创建测试矩阵
        YY = np.array([[0, 1, 4, 9],      # xx2=0时的值
                       [1, 2, 5, 10],     # xx2=1时的值
                       [4, 5, 8, 13]])    # xx2=2时的值
        
        print(f"测试网格:")
        print(f"xx1: {xx1}")
        print(f"xx2: {xx2}")
        print(f"YY 矩阵:\n{YY}")
        
        # 测试多个插值点
        test_cases = [
            (np.array([0.5]), np.array([0.5])),
            (np.array([1.5]), np.array([1.0])),
            (np.array([2.5]), np.array([1.5])),
            (np.array([0.5, 1.5, 2.5]), np.array([0.5, 1.0, 1.5]))
        ]
        
        for i, (A1, A2) in enumerate(test_cases):
            print(f"\n测试用例 {i+1}:")
            print(f"A1: {A1}, A2: {A2}")
            
            # 调用插值函数：dpm_interpn(xx2, xx1, YY, A2, A1)
            result = dpm_interpn(xx2, xx1, YY, A2, A1)
            print(f"结果: {result}")
            
            # 手动验证第一个点
            if len(A1) == 1 and len(A2) == 1:
                # 双线性插值手动计算
                a1, a2 = A1[0], A2[0]
                
                # 找到周围的四个点
                i1_low = int(np.floor(a1))
                i1_high = min(i1_low + 1, len(xx1) - 1)
                i2_low = int(np.floor(a2))
                i2_high = min(i2_low + 1, len(xx2) - 1)
                
                # 计算权重
                w1 = a1 - i1_low if i1_high > i1_low else 0
                w2 = a2 - i2_low if i2_high > i2_low else 0
                
                # 双线性插值
                v00 = YY[i2_low, i1_low]
                v01 = YY[i2_low, i1_high]
                v10 = YY[i2_high, i1_low]
                v11 = YY[i2_high, i1_high]
                
                manual_result = (1-w1)*(1-w2)*v00 + w1*(1-w2)*v01 + (1-w1)*w2*v10 + w1*w2*v11
                
                print(f"手动计算结果: {manual_result}")
                print(f"误差: {abs(result[0] - manual_result)}")
        
        print("✓ MATLAB一致性测试完成")
        return True
        
    except Exception as e:
        print(f"✗ MATLAB一致性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    try:
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        YY = np.array([[0, 1, 4],
                       [1, 2, 5]])
        
        # 测试1: 边界点
        print("测试1: 边界点")
        A1_boundary = np.array([0, 1, 2])
        A2_boundary = np.array([0, 0, 0])
        result_boundary = dpm_interpn(xx2, xx1, YY, A2_boundary, A1_boundary)
        print(f"边界点结果: {result_boundary}")
        print(f"期望结果: {YY[0, :]}")  # 应该等于YY的第一行
        
        # 测试2: 超出范围的点
        print("\n测试2: 超出范围的点")
        A1_out = np.array([-0.5, 2.5])
        A2_out = np.array([0.5, 0.5])
        result_out = dpm_interpn(xx2, xx1, YY, A2_out, A1_out)
        print(f"超出范围点结果: {result_out}")
        
        # 测试3: 单点插值
        print("\n测试3: 单点插值")
        A1_single = np.array([1.5])
        A2_single = np.array([0.5])
        result_single = dpm_interpn(xx2, xx1, YY, A2_single, A1_single)
        print(f"单点插值结果: {result_single}")
        
        print("✓ 边界情况测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("测试修改后的dpm_interpn二维插值函数")
    print("验证参数顺序与MATLAB保持一致")
    print("参数顺序: dpm_interpn(xx2, xx1, YY, A2, A1)")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 基本功能
    if test_basic_2d_interpolation():
        success_count += 1
    
    # 测试2: MATLAB一致性
    if test_matlab_consistency():
        success_count += 1
    
    # 测试3: 边界情况
    if test_edge_cases():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！参数顺序修改成功。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
