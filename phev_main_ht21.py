#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PHEV HT21 主函数 - 混合动力车辆动态规划优化
"""

import numpy as np
import pandas as pd
from dpm import dpm, Grid, Problem, Options
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def load_driving_cycle():
    """
    加载驾驶循环数据

    """
    try:
        # 读取速度数据
        speed_df = pd.read_csv('speed_vector.csv', header=None)
        speed_vector = speed_df.iloc[0].values  # 取第一行数据
        
        # 读取加速度数据
        accel_df = pd.read_csv('acceleration_vector.csv', header=None)
        acceleration_vector = accel_df.iloc[0].values  # 取第一行数据
        
        # 读取档位数据
        gear_df = pd.read_csv('gearnumber_vector.csv', header=None)
        gearnumber_vector = gear_df.iloc[0].values  # 取第一行数据
        
        print(f"驾驶循环数据加载成功:")
        print(f"  数据点数: {len(speed_vector)}")
        print(f"  速度范围: {np.min(speed_vector):.1f} - {np.max(speed_vector):.1f} m/s")
        print(f"  加速度范围: {np.min(acceleration_vector):.2f} - {np.max(acceleration_vector):.2f} m/s²")
        print(f"  档位范围: {int(np.min(gearnumber_vector))} - {int(np.max(gearnumber_vector))}")
        
        return speed_vector, acceleration_vector, gearnumber_vector
        
    except Exception as e:
        print(f"加载驾驶循环数据失败: {str(e)}")
        raise


def create_grid():
    """
    创建动态规划网格

    定义状态空间和控制空间的离散化网格

    """
    grd = Grid()

    # 状态变量1 - 电池荷电状态 (SOC)
    grd.Nx = {1: 401}  # SOC分辨率 0.05%
    grd.Xn = {1: {'hi': 0.6, 'lo': 0.4}}  # 最大和最小电池SOC

    # 控制变量1 - 扭矩分配比例
    grd.Nu = {1: 351}  # 扭矩分配分辨率
    grd.Un = {1: {'hi': 1, 'lo': -2.5}}  # 1:EV/Regen, 0:Engine Only, 0-1:Power assist, <0:Recharge

    # 控制变量2 - 工作模式
    grd.Nu[2] = 3  # 工作模式数量
    grd.Un[2] = {'hi': 2, 'lo': 0}  # 0:Series mode, 1:P1 mode, 2:P3 mode

    # 初始状态
    grd.X0 = {1: 0.50}  # 初始SOC为50%

    # 终端状态约束
    grd.XN = {1: {'hi': 0.5 - 0*0.01, 'lo': 0.495 - 0*0.01}}  # 终端SOC约束

    print(f"网格创建完成:")
    print(f"  SOC网格点数: {grd.Nx[1]} (范围: {grd.Xn[1]['lo']:.1f} - {grd.Xn[1]['hi']:.1f})")
    print(f"  扭矩分配网格点数: {grd.Nu[1]} (范围: {grd.Un[1]['lo']:.1f} - {grd.Un[1]['hi']:.1f})")
    print(f"  工作模式网格点数: {grd.Nu[2]} (范围: {grd.Un[2]['lo']} - {grd.Un[2]['hi']})")
    print(f"  初始SOC: {grd.X0[1]:.2f}")
    print(f"  终端SOC范围: {grd.XN[1]['lo']:.3f} - {grd.XN[1]['hi']:.3f}")

    return grd


def define_problem(speed_vector, acceleration_vector, gearnumber_vector):
    """
    定义动态规划问题

    """
    # 驾驶循环数据
    W_data = {1: speed_vector, 2: acceleration_vector, 3: gearnumber_vector}
    Ts = 1  # 采样时间 (秒)
    N = (len(speed_vector) - 1) * 1 // Ts + 1  # 时间步数

    prb = Problem(Ts=Ts, N=N, W=W_data)

    print(f"问题定义完成:")
    print(f"  采样时间: {prb.Ts} 秒")
    print(f"  总时间步数: {prb.N}")
    print(f"  总时间: {(prb.N-1) * prb.Ts} 秒")

    # 根据数据长度判断驾驶循环类型
    if prb.N == 1179:
        cycle_type = "NEDC"
    elif prb.N == 1799:
        cycle_type = "WLTC/CLTC"
    elif prb.N == 1876:
        cycle_type = "FTP75"
    else:
        cycle_type = f"自定义 ({prb.N} 点)"

    print(f"  驾驶循环类型: {cycle_type}")

    return prb


def set_options():
    """
    设置动态规划选项

    """
    options = dpm()
    
    # 基本设置
    options.MyInf = 1000
    options.BoundaryMethod = 'Line'  # also possible: 'none' or 'LevelSet';
    
    if options.BoundaryMethod == 'Line':
        options.Iter = 5      # 迭代次数
        options.Tol = 1e-8    # 容差
        options.FixedGrid = 0 # 固定网格
    
    print(f"动态规划选项设置:")
    print(f"  无穷大值: {options.MyInf}")
    print(f"  边界方法: {options.BoundaryMethod}")
    if options.BoundaryMethod == 'Line':
        print(f"  迭代次数: {options.Iter}")
        print(f"  容差: {options.Tol}")
        print(f"  固定网格: {options.FixedGrid}")
    
    return options


def main():
    """
    主函数 - 执行混合动力车辆动态规划优化
    """
    print("=" * 80)
    print("PHEV HT21 混合动力车辆动态规划优化")
    print("=" * 80)
    
    try:
        # 步骤1: 加载驾驶循环数据
        print("\n步骤1: 加载驾驶循环数据")
        print("-" * 40)
        speed_vector, acceleration_vector, gearnumber_vector = load_driving_cycle()
        
        # 步骤2: 创建网格
        print("\n步骤2: 创建动态规划网格")
        print("-" * 40)
        grd = create_grid()
        
        # 步骤3: 定义问题
        print("\n步骤3: 定义动态规划问题")
        print("-" * 40)
        prb = define_problem(speed_vector, acceleration_vector, gearnumber_vector)
        
        # 步骤4: 设置选项
        print("\n步骤4: 设置动态规划选项")
        print("-" * 40)
        options = set_options()
        
        # 步骤5: 执行动态规划优化
        print("\n步骤5: 执行动态规划优化")
        print("-" * 40)
        
        # 调用动态规划求解器
        result = dpm(phev_ht21_serip1p3, None, grd, prb, options)
        if isinstance(result, tuple) and len(result) == 2:
            res, dyn = result
        else:
            # 如果只返回一个值，就是dyn
            dyn = result
            res = None
        
        # 步骤6: 显示结果
        print("\n步骤6: 优化结果")
        print("-" * 40)
        print("动态规划优化完成!")
        
        # 显示基本结果信息
        if hasattr(res, 'J') and res.J is not None:
            print(f"最优成本: {res.J:.6f}")

        if hasattr(res, 'X') and res.X is not None:
            print(f"最优轨迹维度: {len(res.X)}")
            if len(res.X) > 0 and hasattr(res.X[1], '__len__') and len(res.X[1]) > 0:
                initial_soc = res.X[1][0]
                final_soc = res.X[1][-1]
                print(f"初始SOC: {initial_soc:.3f}")
                print(f"最终SOC: {final_soc:.3f}")
                print(f"SOC变化: {final_soc - initial_soc:.3f}")
        
        return res, dyn
        
    except Exception as e:
        print(f"\n优化过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    # 执行主函数
    results, dynamics = main()
    
    if results is not None:
        import matplotlib.pyplot as plt

        # 绘制SOC轨迹
        plt.figure(figsize=(10, 6))
        plt.plot(results.X[1], linewidth=2, color='blue')
        plt.xlabel('时间步')
        plt.ylabel('SOC')
        plt.title('最优SOC轨迹')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # 保存图片
        plt.savefig('soc_trajectory.png', dpi=300, bbox_inches='tight')
        print(f"SOC轨迹图已保存为: soc_trajectory.png")

        # 保存SOC数据到CSV文件
        soc_data = pd.DataFrame({
            'time_step': range(len(results.X[1])),
            'soc': results.X[1]
        })
        soc_data.to_csv('soc.csv', index=False)
        print(f"SOC数据已保存到: soc.csv")
        print(f"数据点数: {len(results.X[1])}")

        plt.show()
