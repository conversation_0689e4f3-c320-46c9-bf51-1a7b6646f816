#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试_process_options函数与MATLAB版本的一致性
"""

import numpy as np
import warnings
from dpm import Options, Grid, _process_options

def test_matlab_consistency():
    """测试与MATLAB版本的完全一致性"""
    print("=" * 60)
    print("测试_process_options函数与MATLAB版本的完全一致性")
    print("=" * 60)
    
    # 测试1: 创建一个新的Options对象，模拟MATLAB的默认行为
    print("测试1: 默认选项处理")
    
    # 创建一个最小的options对象
    class MinimalOptions:
        pass
    
    options = MinimalOptions()
    
    # 模拟MATLAB中没有设置任何字段的情况
    processed = _process_options(options)
    
    print(f"  CalcLine: {getattr(processed, 'CalcLine', 'missing')}")
    print(f"  SaveMap: {getattr(processed, 'SaveMap', 'missing')}")
    print(f"  BoundaryMethod: '{getattr(processed, 'BoundaryMethod', 'missing')}'")
    print(f"  UseUmap: {getattr(processed, 'UseUmap', 'missing')}")
    
    # 验证关键值
    success = True
    if getattr(processed, 'CalcLine', None) != 0:
        print("✗ CalcLine应该为0")
        success = False
    else:
        print("✓ CalcLine正确设置为0")
    
    if getattr(processed, 'SaveMap', None) != 0:
        print("✗ SaveMap应该为0")
        success = False
    else:
        print("✓ SaveMap正确设置为0")
    
    if getattr(processed, 'BoundaryMethod', None) != '':
        print("✗ BoundaryMethod应该为空字符串")
        success = False
    else:
        print("✓ BoundaryMethod正确设置为空字符串")
    
    if getattr(processed, 'UseUmap', None) != 1:
        print("✗ UseUmap应该为1")
        success = False
    else:
        print("✓ UseUmap正确设置为1")
    
    return success

def test_boundary_methods():
    """测试边界方法设置"""
    print("\n测试2: 边界方法设置")
    print("-" * 40)
    
    success = True
    
    # 测试Line方法
    class TestOptions:
        BoundaryMethod = 'Line'
    
    options = TestOptions()
    processed = _process_options(options)
    
    if (getattr(processed, 'UseLine', None) == 1 and
        getattr(processed, 'UseLevelSet', None) == 0 and
        getattr(processed, 'UseUmap', None) == 1):
        print("✓ Line方法设置正确")
    else:
        print("✗ Line方法设置错误")
        success = False
    
    # 测试LevelSet方法
    class TestOptions2:
        BoundaryMethod = 'LevelSet'
    
    options = TestOptions2()
    processed = _process_options(options)
    
    if (getattr(processed, 'UseLine', None) == 0 and
        getattr(processed, 'UseLevelSet', None) == 1 and
        getattr(processed, 'UseUmap', None) == 0):
        print("✓ LevelSet方法设置正确")
    else:
        print("✗ LevelSet方法设置错误")
        success = False
    
    return success

def test_dimension_warnings():
    """测试维度警告"""
    print("\n测试3: 维度警告")
    print("-" * 40)
    
    # 创建多维网格
    grd = Grid()
    grd.Nx = [21, 31]  # 二维
    
    class TestOptions:
        BoundaryMethod = 'Line'
    
    options = TestOptions()
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        processed = _process_options(options, grd)
        
        # 检查是否有维度警告
        dimension_warning = any("一维系统" in str(warning.message) for warning in w)
        if dimension_warning:
            print("✓ 边界线方法多维警告正确")
            return True
        else:
            print("✗ 边界线方法多维警告缺失")
            return False

def test_calcline_condition():
    """测试CalcLine条件"""
    print("\n测试4: CalcLine条件")
    print("-" * 40)
    
    # 测试已有CalcLine时不处理
    class TestOptions:
        CalcLine = 1
        BoundaryMethod = 'Line'  # 这个不应该被处理
    
    options = TestOptions()
    original_boundary = options.BoundaryMethod
    processed = _process_options(options)
    
    if (processed.CalcLine == 1 and 
        processed.BoundaryMethod == original_boundary):
        print("✓ 已有CalcLine时不进行处理")
        return True
    else:
        print("✗ 已有CalcLine时错误地进行了处理")
        return False

def main():
    """主测试函数"""
    print("最终测试：_process_options函数与MATLAB版本的一致性")
    print("对比MATLAB @dpm.m 第245-375行的逻辑")
    print()
    
    tests = [
        test_matlab_consistency,
        test_boundary_methods,
        test_dimension_warnings,
        test_calcline_condition
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        print("✓ Python版本与MATLAB版本完全一致")
        print("✓ CalcLine条件检查正确")
        print("✓ SaveMap默认值为0")
        print("✓ BoundaryMethod默认值为空字符串")
        print("✓ 维度检查和警告正确")
        print("✓ 边界方法设置正确")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
