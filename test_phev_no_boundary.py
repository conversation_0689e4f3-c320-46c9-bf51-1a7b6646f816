#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PHEV HT21 主函数测试 - 不使用边界线方法
"""

import numpy as np
import pandas as pd
from dpm import dpm, Grid, Problem, Options
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def load_driving_cycle():
    """加载驾驶循环数据"""
    try:
        # 读取速度数据
        speed_df = pd.read_csv('speed_vector.csv', header=None)
        speed_vector = speed_df.iloc[0].values
        
        # 读取加速度数据
        accel_df = pd.read_csv('acceleration_vector.csv', header=None)
        acceleration_vector = accel_df.iloc[0].values
        
        # 读取档位数据
        gear_df = pd.read_csv('gearnumber_vector.csv', header=None)
        gearnumber_vector = gear_df.iloc[0].values
        
        print(f"驾驶循环数据加载成功:")
        print(f"  数据点数: {len(speed_vector)}")
        print(f"  速度范围: {np.min(speed_vector):.1f} - {np.max(speed_vector):.1f} m/s")
        print(f"  加速度范围: {np.min(acceleration_vector):.2f} - {np.max(acceleration_vector):.2f} m/s²")
        print(f"  档位范围: {int(np.min(gearnumber_vector))} - {int(np.max(gearnumber_vector))}")
        
        return speed_vector, acceleration_vector, gearnumber_vector
        
    except Exception as e:
        print(f"加载驾驶循环数据失败: {str(e)}")
        raise


def create_grid():
    """创建动态规划网格"""
    grd = Grid()

    # 状态变量1 - 电池荷电状态 (SOC)
    grd.Nx = {1: [101]}  # 减少分辨率以加快计算
    grd.Xn = {1: {'hi': [0.6], 'lo': [0.4]}}

    # 控制变量1 - 扭矩分配比例
    grd.Nu = {1: [51]}  # 减少分辨率
    grd.Un = {1: {'hi': [1], 'lo': [-2.5]}}

    # 控制变量2 - 工作模式
    grd.Nu[2] = [3]
    grd.Un[2] = {'hi': [2], 'lo': [0]}

    # 初始状态
    grd.X0 = {1: 0.50}

    # 终端状态约束
    grd.XN = {1: {'hi': 0.5, 'lo': 0.495}}

    print(f"网格创建完成:")
    print(f"  SOC网格点数: {grd.Nx[1][0]} (范围: {grd.Xn[1]['lo'][0]:.1f} - {grd.Xn[1]['hi'][0]:.1f})")
    print(f"  扭矩分配网格点数: {grd.Nu[1][0]} (范围: {grd.Un[1]['lo'][0]:.1f} - {grd.Un[1]['hi'][0]:.1f})")
    print(f"  工作模式网格点数: {grd.Nu[2][0]} (范围: {grd.Un[2]['lo'][0]} - {grd.Un[2]['hi'][0]})")
    print(f"  初始SOC: {grd.X0[1]:.2f}")
    print(f"  终端SOC范围: {grd.XN[1]['lo']:.3f} - {grd.XN[1]['hi']:.3f}")

    return grd


def define_problem(speed_vector, acceleration_vector, gearnumber_vector):
    """定义动态规划问题"""
    # 为了测试，只使用前100个数据点
    test_length = min(100, len(speed_vector))
    
    W_data = {0: speed_vector[:test_length], 
              1: acceleration_vector[:test_length], 
              2: gearnumber_vector[:test_length]}
    Ts = 1
    N = test_length

    prb = Problem(Ts=Ts, N=N, W=W_data)

    print(f"问题定义完成:")
    print(f"  采样时间: {prb.Ts} 秒")
    print(f"  总时间步数: {prb.N}")
    print(f"  总时间: {(prb.N-1) * prb.Ts} 秒")

    return prb


def set_options():
    """设置动态规划选项"""
    options = dpm()
    
    # 基本设置
    options.MyInf = 1000
    options.BoundaryMethod = 'none'  # 不使用边界线方法
    options.Verbose = 'on'
    
    print(f"动态规划选项设置:")
    print(f"  无穷大值: {options.MyInf}")
    print(f"  边界方法: {options.BoundaryMethod}")
    
    return options


def main():
    """主函数"""
    print("=" * 80)
    print("PHEV HT21 混合动力车辆动态规划优化 (无边界线版本)")
    print("=" * 80)
    
    try:
        # 步骤1: 加载驾驶循环数据
        print("\n步骤1: 加载驾驶循环数据")
        print("-" * 40)
        speed_vector, acceleration_vector, gearnumber_vector = load_driving_cycle()
        
        # 步骤2: 创建网格
        print("\n步骤2: 创建动态规划网格")
        print("-" * 40)
        grd = create_grid()
        
        # 步骤3: 定义问题
        print("\n步骤3: 定义动态规划问题")
        print("-" * 40)
        prb = define_problem(speed_vector, acceleration_vector, gearnumber_vector)
        
        # 步骤4: 设置选项
        print("\n步骤4: 设置动态规划选项")
        print("-" * 40)
        options = set_options()
        
        # 步骤5: 执行动态规划优化
        print("\n步骤5: 执行动态规划优化")
        print("-" * 40)
        
        # 调用动态规划求解器
        result = dpm(phev_ht21_serip1p3, None, grd, prb, options)
        if isinstance(result, tuple) and len(result) == 2:
            res, dyn = result
        else:
            dyn = result
            res = None
        
        # 步骤6: 显示结果
        print("\n步骤6: 优化结果")
        print("-" * 40)
        print("动态规划优化完成!")
        
        # 显示基本结果信息
        if hasattr(res, 'J') and res.J is not None:
            print(f"最优成本: {res.J:.6f}")

        if hasattr(res, 'X') and res.X is not None:
            print(f"最优轨迹维度: {len(res.X)}")
            if len(res.X) > 0 and hasattr(res.X[0], '__len__') and len(res.X[0]) > 0:
                initial_soc = res.X[0][0]
                final_soc = res.X[0][-1]
                print(f"初始SOC: {initial_soc:.3f}")
                print(f"最终SOC: {final_soc:.3f}")
                print(f"SOC变化: {final_soc - initial_soc:.3f}")

        if hasattr(res, 'U') and res.U is not None:
            print(f"最优控制序列维度: {len(res.U)}")

        print("\n优化完成! 结果已保存在 res 和 dyn 变量中。")
        
        return res, dyn
        
    except Exception as e:
        print(f"\n优化过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    results, dynamics = main()
    
    if results is not None:
        print("\n可以使用以下变量访问结果:")
        print("  results: 优化结果")
        print("  dynamics: 动态信息")
    else:
        print("\n优化失败，请检查错误信息。")
