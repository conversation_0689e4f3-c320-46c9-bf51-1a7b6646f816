#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 PHEV HT21 主函数

验证混合动力车辆动态规划优化主函数的各个组件
"""

import numpy as np
import matplotlib.pyplot as plt
from phev_main_ht21 import load_driving_cycle, create_grid, define_problem, set_options
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def test_data_loading():
    """测试数据加载功能"""
    print("=" * 60)
    print("测试数据加载功能")
    print("=" * 60)
    
    try:
        speed_vector, acceleration_vector, gearnumber_vector = load_driving_cycle()
        
        print(f"\n数据验证:")
        print(f"  速度数据: {len(speed_vector)} 点")
        print(f"  加速度数据: {len(acceleration_vector)} 点")
        print(f"  档位数据: {len(gearnumber_vector)} 点")
        
        # 检查数据一致性
        if len(speed_vector) == len(acceleration_vector) == len(gearnumber_vector):
            print("✓ 数据长度一致")
        else:
            print("✗ 数据长度不一致")
            return False
        
        # 检查数据合理性
        if np.all(speed_vector >= 0) and np.all(speed_vector <= 50):
            print("✓ 速度数据合理")
        else:
            print("✗ 速度数据异常")
        
        if np.all(acceleration_vector >= -5) and np.all(acceleration_vector <= 5):
            print("✓ 加速度数据合理")
        else:
            print("✗ 加速度数据异常")
        
        if np.all(gearnumber_vector >= 1) and np.all(gearnumber_vector <= 6):
            print("✓ 档位数据合理")
        else:
            print("✗ 档位数据异常")
        
        print("✓ 数据加载测试通过")
        return True, (speed_vector, acceleration_vector, gearnumber_vector)
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {str(e)}")
        return False, None


def test_grid_creation():
    """测试网格创建功能"""
    print("\n" + "=" * 60)
    print("测试网格创建功能")
    print("=" * 60)
    
    try:
        grd = create_grid()
        
        print(f"\n网格验证:")
        
        # 检查必要的字段
        required_fields = ['Nx', 'Xn', 'Nu', 'Un', 'X0', 'XN']
        for field in required_fields:
            if field in grd:
                print(f"✓ {field} 字段存在")
            else:
                print(f"✗ {field} 字段缺失")
                return False
        
        # 检查网格大小
        if grd['Nx'][0] > 0:
            print(f"✓ SOC网格大小: {grd['Nx'][0]}")
        else:
            print("✗ SOC网格大小无效")
        
        if len(grd['Nu']) == 2 and grd['Nu'][0] > 0 and grd['Nu'][1] > 0:
            print(f"✓ 控制网格大小: {grd['Nu']}")
        else:
            print("✗ 控制网格大小无效")
        
        # 检查边界条件
        if 0 <= grd['X0'][0] <= 1:
            print(f"✓ 初始SOC合理: {grd['X0'][0]}")
        else:
            print("✗ 初始SOC不合理")
        
        print("✓ 网格创建测试通过")
        return True, grd
        
    except Exception as e:
        print(f"✗ 网格创建测试失败: {str(e)}")
        return False, None


def test_problem_definition(driving_data):
    """测试问题定义功能"""
    print("\n" + "=" * 60)
    print("测试问题定义功能")
    print("=" * 60)
    
    try:
        speed_vector, acceleration_vector, gearnumber_vector = driving_data
        prb = define_problem(speed_vector, acceleration_vector, gearnumber_vector)
        
        print(f"\n问题验证:")
        
        # 检查必要的字段
        required_fields = ['W', 'Ts', 'N']
        for field in required_fields:
            if field in prb:
                print(f"✓ {field} 字段存在")
            else:
                print(f"✗ {field} 字段缺失")
                return False
        
        # 检查驾驶循环数据
        if len(prb['W']) == 3:
            print(f"✓ 驾驶循环数据包含3个向量")
        else:
            print("✗ 驾驶循环数据格式错误")
        
        # 检查时间参数
        if prb['Ts'] > 0:
            print(f"✓ 采样时间: {prb['Ts']} 秒")
        else:
            print("✗ 采样时间无效")
        
        if prb['N'] > 0:
            print(f"✓ 时间步数: {prb['N']}")
        else:
            print("✗ 时间步数无效")
        
        print("✓ 问题定义测试通过")
        return True, prb
        
    except Exception as e:
        print(f"✗ 问题定义测试失败: {str(e)}")
        return False, None


def test_options_setting():
    """测试选项设置功能"""
    print("\n" + "=" * 60)
    print("测试选项设置功能")
    print("=" * 60)
    
    try:
        options = set_options()
        
        print(f"\n选项验证:")
        
        # 检查基本选项
        if 'MyInf' in options and options['MyInf'] > 0:
            print(f"✓ MyInf: {options['MyInf']}")
        else:
            print("✗ MyInf 设置无效")
        
        if 'BoundaryMethod' in options:
            print(f"✓ 边界方法: {options['BoundaryMethod']}")
        else:
            print("✗ 边界方法未设置")
        
        # 检查Line方法的特定选项
        if options.get('BoundaryMethod') == 'Line':
            line_options = ['Iter', 'Tol', 'FixedGrid']
            for opt in line_options:
                if opt in options:
                    print(f"✓ {opt}: {options[opt]}")
                else:
                    print(f"✗ {opt} 未设置")
        
        print("✓ 选项设置测试通过")
        return True, options
        
    except Exception as e:
        print(f"✗ 选项设置测试失败: {str(e)}")
        return False, None


def test_vehicle_model():
    """测试车辆模型功能"""
    print("\n" + "=" * 60)
    print("测试车辆模型功能")
    print("=" * 60)
    
    try:
        # 创建测试输入
        inp = {
            'W': [20.0, 0.5],  # 速度和加速度
            'U': [0.5, 1],     # 控制输入
            'X': [0.6]         # 状态
        }
        par = {}
        
        print(f"测试车辆模型调用:")
        print(f"  输入速度: {inp['W'][0]} m/s")
        print(f"  输入加速度: {inp['W'][1]} m/s²")
        print(f"  扭矩分配: {inp['U'][0]}")
        print(f"  工作模式: {inp['U'][1]}")
        print(f"  初始SOC: {inp['X'][0]}")
        
        # 调用车辆模型
        X, C, I, out = phev_ht21_serip1p3(inp, par)
        
        print(f"\n模型输出:")
        print(f"  最终SOC: {X[0]:.4f}")
        print(f"  燃油消耗: {C[0]:.6f} kg/s")
        print(f"  不可行标志: {I}")
        print(f"  发动机扭矩: {out['Te']:.2f} Nm")
        print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
        print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
        
        # 验证输出合理性
        if 0 <= X[0] <= 1:
            print("✓ SOC输出合理")
        else:
            print("✗ SOC输出异常")
        
        if C[0] >= 0:
            print("✓ 燃油消耗非负")
        else:
            print("✗ 燃油消耗为负")
        
        print("✓ 车辆模型测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 车辆模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_driving_cycle_analysis(driving_data):
    """分析驾驶循环特性"""
    print("\n" + "=" * 60)
    print("驾驶循环特性分析")
    print("=" * 60)
    
    try:
        speed_vector, acceleration_vector, gearnumber_vector = driving_data
        
        # 基本统计
        print(f"驾驶循环统计:")
        print(f"  总时间: {len(speed_vector)} 秒")
        print(f"  平均速度: {np.mean(speed_vector):.2f} m/s ({np.mean(speed_vector)*3.6:.1f} km/h)")
        print(f"  最大速度: {np.max(speed_vector):.2f} m/s ({np.max(speed_vector)*3.6:.1f} km/h)")
        print(f"  怠速时间: {np.sum(speed_vector == 0)} 秒 ({np.sum(speed_vector == 0)/len(speed_vector)*100:.1f}%)")
        
        # 加速度分析
        print(f"\n加速度分析:")
        print(f"  最大加速度: {np.max(acceleration_vector):.2f} m/s²")
        print(f"  最大减速度: {np.min(acceleration_vector):.2f} m/s²")
        print(f"  加速时间: {np.sum(acceleration_vector > 0.1)} 秒")
        print(f"  减速时间: {np.sum(acceleration_vector < -0.1)} 秒")
        print(f"  匀速时间: {np.sum(np.abs(acceleration_vector) <= 0.1)} 秒")
        
        # 档位分析
        print(f"\n档位分析:")
        for gear in range(1, int(np.max(gearnumber_vector))+1):
            gear_time = np.sum(gearnumber_vector == gear)
            print(f"  {gear}档时间: {gear_time} 秒 ({gear_time/len(gearnumber_vector)*100:.1f}%)")
        
        print("✓ 驾驶循环分析完成")
        return True
        
    except Exception as e:
        print(f"✗ 驾驶循环分析失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("PHEV HT21 主函数组件测试程序")
    print("验证混合动力车辆动态规划优化的各个组件")
    
    success_count = 0
    total_tests = 6
    
    # 测试1: 数据加载
    success, driving_data = test_data_loading()
    if success:
        success_count += 1
    
    # 测试2: 网格创建
    success, grd = test_grid_creation()
    if success:
        success_count += 1
    
    # 测试3: 问题定义
    if driving_data:
        success, prb = test_problem_definition(driving_data)
        if success:
            success_count += 1
    
    # 测试4: 选项设置
    success, options = test_options_setting()
    if success:
        success_count += 1
    
    # 测试5: 车辆模型
    if test_vehicle_model():
        success_count += 1
    
    # 测试6: 驾驶循环分析
    if driving_data and test_driving_cycle_analysis(driving_data):
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有组件测试都通过了！")
        print("✓ 数据加载功能正常")
        print("✓ 网格创建功能正常")
        print("✓ 问题定义功能正常")
        print("✓ 选项设置功能正常")
        print("✓ 车辆模型功能正常")
        print("✓ 驾驶循环分析功能正常")
        print("\n主函数已准备好进行动态规划优化！")
    else:
        print("⚠️  部分测试失败。请检查相关组件。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
