#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpf1sbh 函数的脚本

这个脚本测试 dpm_interpf1sbh 函数的各种功能
"""

import numpy as np
import matplotlib.pyplot as plt
from dpm import dpm_interpf1sbh


def test_basic_sbh_interpolation():
    """测试基本的边界处理插值功能"""
    print("=" * 60)
    print("测试 dpm_interpf1sbh 基本边界处理插值功能")
    print("=" * 60)
    
    try:
        # 创建测试数据
        xx = np.linspace(0, 10, 11)  # [0, 1, 2, ..., 10]
        yy = xx**2  # 二次函数 [0, 1, 4, 9, 16, 25, 36, 49, 64, 81, 100]
        
        # 设置边界限制
        lim = np.array([3.0, 7.0])  # 下边界3.0，上边界7.0
        
        print(f"输入网格 xx: {xx}")
        print(f"输入值 yy: {yy}")
        print(f"边界限制 lim: {lim}")
        
        # 测试不同区域的插值点
        test_points = [
            1.0,   # 在下边界以下
            3.0,   # 正好在下边界
            3.5,   # 在下边界和第一个网格点之间
            5.0,   # 在常规网格内
            6.5,   # 在最后一个网格点和上边界之间
            7.0,   # 正好在上边界
            9.0    # 在上边界以上
        ]
        
        print("\n插值测试:")
        for a in test_points:
            y = dpm_interpf1sbh(xx, yy, a, lim)
            
            # 确定a所在的区域
            if a <= lim[0]:
                region = "下边界以下"
            elif a >= lim[1]:
                region = "上边界以上"
            elif a < xx[4] and a > lim[0]:  # xx[4] = 4.0，第一个大于下边界的点
                region = "下边界和网格之间"
            elif a < lim[1] and a > xx[6]:  # xx[6] = 6.0，最后一个小于上边界的点
                region = "网格和上边界之间"
            else:
                region = "常规网格内"
            
            expected = a**2  # 理论期望值
            print(f"  a = {a:.1f}: y = {y:.4f}, 期望≈{expected:.4f}, 区域: {region}")
        
        print("✓ dpm_interpf1sbh 基本边界处理插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1sbh 基本边界处理插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases_sbh():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf1sbh 边界情况")
    print("=" * 60)
    
    try:
        # 测试1: 边界正好在网格点上
        print("测试1: 边界正好在网格点上")
        xx = np.array([0, 1, 2, 3, 4, 5])
        yy = np.array([0, 1, 4, 9, 16, 25])
        lim = np.array([2.0, 4.0])  # 边界正好在网格点上
        
        test_points = [1.5, 2.0, 3.0, 4.0, 4.5]
        for a in test_points:
            y = dpm_interpf1sbh(xx, yy, a, lim)
            print(f"  a = {a:.1f}: y = {y:.4f}")
        
        # 测试2: 非常窄的边界
        print("\n测试2: 非常窄的边界")
        lim_narrow = np.array([2.8, 3.2])
        test_points_narrow = [2.5, 2.8, 3.0, 3.2, 3.5]
        for a in test_points_narrow:
            y = dpm_interpf1sbh(xx, yy, a, lim_narrow)
            print(f"  a = {a:.1f}: y = {y:.4f}")
        
        # 测试3: 边界覆盖整个网格
        print("\n测试3: 边界覆盖整个网格")
        lim_wide = np.array([-1.0, 6.0])
        test_points_wide = [-0.5, 2.5, 5.5]
        for a in test_points_wide:
            y = dpm_interpf1sbh(xx, yy, a, lim_wide)
            print(f"  a = {a:.1f}: y = {y:.4f}")
        
        # 测试4: 单调递减函数
        print("\n测试4: 单调递减函数")
        yy_decreasing = 25 - xx**2  # 递减函数
        lim_dec = np.array([1.5, 4.5])
        test_points_dec = [1.0, 2.0, 3.0, 4.0, 5.0]
        for a in test_points_dec:
            y = dpm_interpf1sbh(xx, yy_decreasing, a, lim_dec)
            expected = 25 - a**2
            print(f"  a = {a:.1f}: y = {y:.4f}, 期望≈{expected:.4f}")
        
        print("✓ dpm_interpf1sbh 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1sbh 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_with_visualization_sbh():
    """测试并可视化边界处理插值结果"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf1sbh 并可视化")
    print("=" * 60)
    
    try:
        # 创建更详细的测试数据
        xx = np.linspace(0, 10, 21)  # 更密集的网格
        yy = np.sin(xx) + 0.1 * xx  # 正弦函数加线性趋势
        
        # 设置边界
        lim = np.array([3.5, 7.5])
        
        # 创建密集的插值点
        a_test = np.linspace(1, 9, 100)
        
        # 计算插值结果
        y_results = []
        for a in a_test:
            y = dpm_interpf1sbh(xx, yy, a, lim)
            y_results.append(y)
        y_results = np.array(y_results)
        
        # 创建可视化
        try:
            plt.figure(figsize=(12, 8))
            
            # 绘制原始数据点
            plt.plot(xx, yy, 'bo-', label='原始数据点', markersize=6, linewidth=2)
            
            # 绘制插值结果
            plt.plot(a_test, y_results, 'r-', label='边界处理插值结果', linewidth=2)
            
            # 绘制边界
            plt.axvline(x=lim[0], color='g', linestyle='--', linewidth=2, label=f'下边界 x={lim[0]}')
            plt.axvline(x=lim[1], color='g', linestyle='--', linewidth=2, label=f'上边界 x={lim[1]}')
            
            # 标记不同区域
            plt.axvspan(0, lim[0], alpha=0.2, color='red', label='下边界外区域')
            plt.axvspan(lim[0], lim[1], alpha=0.2, color='green', label='边界内区域')
            plt.axvspan(lim[1], 10, alpha=0.2, color='red', label='上边界外区域')
            
            # 添加一些关键点的标注
            key_points = [2.0, 3.5, 5.0, 7.5, 8.5]
            for point in key_points:
                y_point = dpm_interpf1sbh(xx, yy, point, lim)
                plt.plot(point, y_point, 'ko', markersize=8)
                plt.annotate(f'({point:.1f}, {y_point:.2f})', 
                           xy=(point, y_point), xytext=(5, 5), 
                           textcoords='offset points', fontsize=8)
            
            plt.xlabel('x')
            plt.ylabel('y')
            plt.title('dpm_interpf1sbh 边界处理插值结果可视化')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('dpm_interpf1sbh_test.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            print("✓ 可视化图表已保存为 'dpm_interpf1sbh_test.png'")
            
        except Exception as plot_error:
            print(f"可视化失败（但插值成功）: {plot_error}")
        
        # 验证连续性
        print("\n连续性验证:")
        boundary_points = [lim[0] - 0.01, lim[0], lim[0] + 0.01,
                          lim[1] - 0.01, lim[1], lim[1] + 0.01]
        for point in boundary_points:
            y = dpm_interpf1sbh(xx, yy, point, lim)
            print(f"  x = {point:.2f}: y = {y:.6f}")
        
        print("✓ dpm_interpf1sbh 可视化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1sbh 可视化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_special_cases_sbh():
    """测试特殊情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf1sbh 特殊情况")
    print("=" * 60)
    
    try:
        # 测试1: 常数函数
        print("测试1: 常数函数")
        xx = np.array([0, 1, 2, 3, 4])
        yy = np.array([5, 5, 5, 5, 5])  # 常数函数
        lim = np.array([1.5, 3.5])
        
        test_points = [0.5, 1.5, 2.5, 3.5, 4.5]
        for a in test_points:
            y = dpm_interpf1sbh(xx, yy, a, lim)
            print(f"  a = {a:.1f}: y = {y:.4f} (应该都是5.0)")
        
        # 测试2: 线性函数
        print("\n测试2: 线性函数")
        yy_linear = xx * 2 + 1  # y = 2x + 1
        for a in test_points:
            y = dpm_interpf1sbh(xx, yy_linear, a, lim)
            expected = 2 * a + 1
            print(f"  a = {a:.1f}: y = {y:.4f}, 期望 = {expected:.4f}")
        
        # 测试3: 边界重合的情况
        print("\n测试3: 边界重合的情况")
        lim_same = np.array([2.0, 2.0])  # 上下边界相同
        try:
            y = dpm_interpf1sbh(xx, yy_linear, 2.0, lim_same)
            print(f"  边界重合时 a=2.0: y = {y:.4f}")
        except Exception as e:
            print(f"  边界重合时出错: {e}")
        
        # 测试4: 极小网格
        print("\n测试4: 极小网格")
        xx_small = np.array([0, 1])
        yy_small = np.array([0, 1])
        lim_small = np.array([0.3, 0.7])
        
        test_points_small = [0.2, 0.5, 0.8]
        for a in test_points_small:
            y = dpm_interpf1sbh(xx_small, yy_small, a, lim_small)
            print(f"  a = {a:.1f}: y = {y:.4f}")
        
        print("✓ dpm_interpf1sbh 特殊情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1sbh 特殊情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("dpm_interpf1sbh 函数测试程序")
    print("这个程序测试一维边界处理插值函数的各种功能")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 基本边界处理插值
    if test_basic_sbh_interpolation():
        success_count += 1
    
    # 测试2: 边界情况
    if test_edge_cases_sbh():
        success_count += 1
    
    # 测试3: 可视化测试
    if test_with_visualization_sbh():
        success_count += 1
    
    # 测试4: 特殊情况
    if test_special_cases_sbh():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！dpm_interpf1sbh 函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
