#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试_process_options函数与MATLAB版本@dpm.m（245-375行）的一致性

验证Python版本的_process_options函数是否与MATLAB版本完全一致
"""

import numpy as np
import warnings
from dpm import dpm, Options, Grid, _process_options

def test_backward_compatibility():
    """测试向后兼容性处理"""
    print("=" * 60)
    print("测试1: 向后兼容性处理")
    print("=" * 60)
    
    # 测试InfCost重命名
    print("测试InfCost重命名:")
    options = Options()
    options.InfCost = 5000
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        processed = _process_options(options)
        
        if len(w) > 0 and "InfCost" in str(w[0].message):
            print("✓ InfCost警告正确")
        else:
            print("✗ InfCost警告缺失")
        
        if hasattr(processed, 'MyInf') and processed.MyInf == 5000:
            print("✓ InfCost正确重命名为MyInf")
        else:
            print("✗ InfCost重命名失败")
        
        if not hasattr(processed, 'InfCost'):
            print("✓ 旧字段InfCost已删除")
        else:
            print("✗ 旧字段InfCost未删除")

    # 测试BoundaryLineMethod重命名
    print("\n测试BoundaryLineMethod重命名:")
    options = Options()
    options.BoundaryLineMethod = 'Line'
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        processed = _process_options(options)
        
        if len(w) > 0 and "BoundaryLineMethod" in str(w[0].message):
            print("✓ BoundaryLineMethod警告正确")
        else:
            print("✗ BoundaryLineMethod警告缺失")

def test_savemap_processing():
    """测试SaveMap选项处理"""
    print("\n测试2: SaveMap选项处理")
    print("=" * 60)
    
    # 测试默认值
    options = Options()
    delattr(options, 'SaveMap')  # 删除默认SaveMap
    processed = _process_options(options)
    
    if hasattr(processed, 'SaveMap') and processed.SaveMap == 0:
        print("✓ SaveMap默认值为0 (与MATLAB一致)")
    else:
        print(f"✗ SaveMap默认值错误: {getattr(processed, 'SaveMap', 'missing')}")
    
    # 测试字符串转换
    options = Options()
    options.SaveMap = 'on'
    processed = _process_options(options)
    
    if processed.SaveMap == 1:
        print("✓ SaveMap 'on' 转换为1")
    else:
        print(f"✗ SaveMap 'on' 转换错误: {processed.SaveMap}")
    
    options.SaveMap = 'off'
    processed = _process_options(options)
    
    if processed.SaveMap == 0:
        print("✓ SaveMap 'off' 转换为0")
    else:
        print(f"✗ SaveMap 'off' 转换错误: {processed.SaveMap}")

def test_boundary_method():
    """测试边界方法处理"""
    print("\n测试3: 边界方法处理")
    print("=" * 60)
    
    # 测试默认值
    options = Options()
    delattr(options, 'BoundaryMethod')
    processed = _process_options(options)
    
    if hasattr(processed, 'BoundaryMethod') and processed.BoundaryMethod == '':
        print("✓ BoundaryMethod默认值为空字符串 (与MATLAB一致)")
    else:
        print(f"✗ BoundaryMethod默认值错误: {getattr(processed, 'BoundaryMethod', 'missing')}")
    
    # 测试Line方法
    options = Options()
    options.BoundaryMethod = 'Line'
    processed = _process_options(options)
    
    if (hasattr(processed, 'UseLine') and processed.UseLine == 1 and
        hasattr(processed, 'UseLevelSet') and processed.UseLevelSet == 0 and
        hasattr(processed, 'UseUmap') and processed.UseUmap == 1):
        print("✓ Line方法设置正确")
    else:
        print("✗ Line方法设置错误")
    
    # 测试LevelSet方法
    options = Options()
    options.BoundaryMethod = 'LevelSet'
    processed = _process_options(options)
    
    if (hasattr(processed, 'UseLine') and processed.UseLine == 0 and
        hasattr(processed, 'UseLevelSet') and processed.UseLevelSet == 1 and
        hasattr(processed, 'UseUmap') and processed.UseUmap == 0):
        print("✓ LevelSet方法设置正确")
    else:
        print("✗ LevelSet方法设置错误")

def test_dimension_checks():
    """测试维度检查"""
    print("\n测试4: 维度检查")
    print("=" * 60)
    
    # 创建一维网格
    grd_1d = Grid()
    grd_1d.Nx = [21]
    
    # 创建多维网格
    grd_2d = Grid()
    grd_2d.Nx = [21, 31]
    
    # 测试边界线方法的维度检查
    options = Options()
    options.BoundaryMethod = 'Line'
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        processed = _process_options(options, grd_2d)
        
        # 检查是否有维度警告
        dimension_warning = any("一维系统" in str(warning.message) for warning in w)
        if dimension_warning:
            print("✓ 边界线方法多维警告正确")
        else:
            print("✗ 边界线方法多维警告缺失")
    
    # 测试水平集方法的维度提示
    options = Options()
    options.BoundaryMethod = 'LevelSet'
    
    import io
    import sys
    captured_output = io.StringIO()
    sys.stdout = captured_output
    
    processed = _process_options(options, grd_1d)
    
    sys.stdout = sys.__stdout__
    output = captured_output.getvalue()
    
    if "边界线方法" in output:
        print("✓ 水平集方法一维提示正确")
    else:
        print("✗ 水平集方法一维提示缺失")

def test_calcline_condition():
    """测试CalcLine条件检查"""
    print("\n测试5: CalcLine条件检查")
    print("=" * 60)
    
    # 测试没有CalcLine时的处理
    options = Options()
    if hasattr(options, 'CalcLine'):
        delattr(options, 'CalcLine')
    
    processed = _process_options(options)
    
    if hasattr(processed, 'CalcLine') and processed.CalcLine == 0:
        print("✓ CalcLine正确设置为0")
    else:
        print(f"✗ CalcLine设置错误: {getattr(processed, 'CalcLine', 'missing')}")
    
    # 测试已有CalcLine时不处理
    options = Options()
    options.CalcLine = 1
    original_boundary_method = getattr(options, 'BoundaryMethod', 'none')
    
    processed = _process_options(options)
    
    # 如果已有CalcLine，应该不进行其他处理
    if processed.CalcLine == 1:
        print("✓ 已有CalcLine时保持不变")
    else:
        print("✗ 已有CalcLine时被错误修改")

def main():
    """主测试函数"""
    print("测试_process_options函数与MATLAB版本的一致性")
    print("对比MATLAB @dpm.m 第245-375行的逻辑")
    print()
    
    success_count = 0
    total_tests = 5
    
    try:
        test_backward_compatibility()
        success_count += 1
    except Exception as e:
        print(f"测试1失败: {e}")
    
    try:
        test_savemap_processing()
        success_count += 1
    except Exception as e:
        print(f"测试2失败: {e}")
    
    try:
        test_boundary_method()
        success_count += 1
    except Exception as e:
        print(f"测试3失败: {e}")
    
    try:
        test_dimension_checks()
        success_count += 1
    except Exception as e:
        print(f"测试4失败: {e}")
    
    try:
        test_calcline_condition()
        success_count += 1
    except Exception as e:
        print(f"测试5失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！")
        print("✓ Python版本与MATLAB版本一致")
        print("✓ CalcLine条件检查正确")
        print("✓ SaveMap默认值为0")
        print("✓ BoundaryMethod默认值为空字符串")
        print("✓ 维度检查和警告正确")
        print("✓ 向后兼容性处理正确")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
