#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_backward 函数的功能逻辑

这个脚本测试 dpm_backward 函数的各种功能，包括：
1. 基本的动态规划计算
2. 边界线方法
3. 水平集方法
4. 不同维度的状态和输入空间
5. 错误处理和边界情况
"""

import numpy as np
import sys
import os

# 添加当前目录到路径，以便导入 dpm 模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dpm import (
    dpm_backward, Grid, Problem, Options, DynamicResult,
    Input, Output
)
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def phev_model_adapter(inp, par=None):
    """
    PHEV模型适配器，将PHEV模型的输出转换为dpm_backward期望的格式

    dpm_backward期望：
    - inp.X[1]: 电池荷电状态 (SOC)
    - inp.U[1]: 控制输入1 (发动机开关)
    - inp.U[2]: 控制输入2 (工作模式)
    - inp.W[0]: 车速 (m/s)
    - inp.W[1]: 车辆加速度 (m/s^2)

    PHEV模型期望：
    - inp.X[0]: 电池荷电状态 (SOC)
    - inp.U[0]: 控制输入1 (发动机开关)
    - inp.U[1]: 控制输入2 (工作模式)
    """
    try:
        # 创建PHEV模型的输入结构（索引从0开始）
        phev_inp = Input()
        phev_inp.X = {0: inp.X[1]}  # 转换状态索引 1->0
        phev_inp.U = {0: inp.U[1], 1: inp.U[2]}  # 转换输入索引 1,2->0,1
        phev_inp.W = inp.W  # 扰动保持不变
        phev_inp.Ts = inp.Ts

        # 调用PHEV模型
        output, out = phev_ht21_serip1p3(phev_inp, par)

        # 转换输出格式（索引从0转换为1）
        X = {1: output.X[0]}  # 转换状态索引 0->1
        C = {1: output.C[0]}  # 转换代价索引 0->1
        I = output.I  # 不可行标志保持不变

        # 创建输出结构
        class Output:
            def __init__(self):
                self.X = X
                self.C = C
                self.I = I

        return Output(), out

    except Exception as e:
        # 如果模型调用失败，返回默认值
        print(f"PHEV模型调用失败: {e}")
        import traceback
        traceback.print_exc()

        # 创建默认输出
        X = {1: inp.X[1]}  # 保持SOC不变
        C = {1: np.ones_like(inp.X[1]) * 1000}  # 高代价
        I = np.ones_like(inp.X[1])  # 标记为不可行

        class Output:
            def __init__(self):
                self.X = X
                self.C = C
                self.I = I

        return Output(), None


def simple_1d_model(inp, par=None):
    """
    简单的一维模型函数

    状态方程: x(k+1) = x(k) + u(k) * Ts
    代价函数: C = u(k)^2 * Ts
    """
    # 状态更新
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}

    # 代价函数
    C = {1: inp.U[1]**2 * inp.Ts}

    # 不可行性检查
    I = np.zeros_like(inp.X[1])

    # 创建输出结构
    class Output:
        def __init__(self):
            self.X = X
            self.C = C
            self.I = I

    # 返回2个值：output, out
    return Output(), None


def simple_2d_model(inp, par=None):
    """
    简单的二维模型函数

    状态方程:
    x1(k+1) = x1(k) + u1(k) * Ts
    x2(k+1) = x2(k) + u2(k) * Ts
    代价函数: C = u1(k)^2 + u2(k)^2
    """
    # 状态更新
    X = {
        1: inp.X[1] + inp.U[1] * inp.Ts,
        2: inp.X[2] + inp.U[2] * inp.Ts
    }

    # 代价函数
    C = {1: inp.U[1]**2 + inp.U[2]**2}

    # 不可行性检查
    I = np.zeros_like(inp.X[1])

    # 创建输出结构
    class Output:
        def __init__(self):
            self.X = X
            self.C = C
            self.I = I

    # 返回2个值：output, out
    return Output(), None


def create_phev_test_case():
    """创建PHEV测试案例"""
    # 网格结构 - 注意：dpm_backward期望索引从1开始
    grd = Grid()
    grd.X0 = {1: 0.6}  # 初始SOC = 60%
    grd.XN = {1: {'lo': 0.2, 'hi': 0.8}}  # 最终SOC约束 20%-80%
    grd.Nx = {1: [11] * 6}  # 11个SOC网格点，5个时间步+1
    grd.Xn = {1: {'lo': [0.2] * 6, 'hi': [0.8] * 6}}  # SOC范围 20%-80%

    # 控制输入
    grd.Nu = {1: [2] * 5, 2: [3] * 5}  # 发动机开关(2) + 工作模式(3)，5个时间步
    grd.Un = {1: {'lo': [0] * 5, 'hi': [1] * 5},      # 发动机开关: 0或1
              2: {'lo': [0] * 5, 'hi': [2] * 5}}      # 工作模式: 0,1,2

    # 问题参数
    prb = Problem(Ts=1.0, N=5)  # 1秒时间步，5个时间步
    # 驾驶循环数据 (车速和加速度)
    prb.W = {
        0: [10.0, 15.0, 20.0, 15.0, 10.0],  # 车速 (m/s)
        1: [1.0, 0.5, -0.5, -1.0, 0.0]      # 加速度 (m/s^2)
    }

    # 选项设置
    options = Options()
    options.MyInf = 1e6
    options.Minimize = 1
    options.Verbose = 'on'
    options.Warnings = 'on'
    options.SaveMap = 'on'

    return grd, prb, options


def create_1d_test_case():
    """创建一维测试案例"""
    # 网格结构
    grd = Grid()
    grd.X0 = {1: 0.0}  # 初始状态
    grd.XN = {1: {'lo': 0.9, 'hi': 1.1}}  # 最终状态约束
    grd.Nx = {1: [10] * 6}  # 10个网格点，5个时间步+1
    grd.Xn = {1: {'lo': [0.0] * 6, 'hi': [1.0] * 6}}
    grd.Nu = {1: [5] * 5}  # 5个网格点，5个时间步
    grd.Un = {1: {'lo': [-0.5] * 5, 'hi': [0.5] * 5}}

    # 问题参数
    prb = Problem(Ts=0.2, N=5)
    prb.W = {}

    # 选项设置
    options = Options()
    options.MyInf = 1e4
    options.Minimize = 1
    options.Verbose = 'on'
    options.Warnings = 'on'
    options.SaveMap = 'on'

    return grd, prb, options


def create_2d_test_case():
    """创建二维测试案例"""
    # 网格结构
    grd = Grid()
    grd.X0 = {1: 0.0, 2: 0.0}  # 初始状态
    grd.XN = {1: {'lo': 0.9, 'hi': 1.1}, 2: {'lo': 0.9, 'hi': 1.1}}  # 最终状态约束
    grd.Nx = {1: [8] * 4, 2: [8] * 4}  # 8个网格点，3个时间步+1
    grd.Xn = {1: {'lo': [0.0] * 4, 'hi': [1.0] * 4},
              2: {'lo': [0.0] * 4, 'hi': [1.0] * 4}}
    grd.Nu = {1: [5] * 3, 2: [5] * 3}  # 5个网格点，3个时间步
    grd.Un = {1: {'lo': [-0.3] * 3, 'hi': [0.3] * 3},
              2: {'lo': [-0.3] * 3, 'hi': [0.3] * 3}}
    
    # 问题参数
    prb = Problem(Ts=0.3, N=3)
    prb.W = {}
    
    # 选项设置
    options = Options()
    options.MyInf = 1e4
    options.Minimize = 1
    options.Verbose = 'on'
    options.Warnings = 'on'
    options.SaveMap = 'on'
    
    return grd, prb, options


def test_phev_model():
    """测试PHEV模型"""
    print("=" * 60)
    print("测试 1: PHEV混合动力车辆模型")
    print("=" * 60)

    try:
        grd, prb, options = create_phev_test_case()

        print("开始PHEV动态规划计算...")
        print(f"SOC范围: {grd.Xn[1]['lo'][0]:.1%} - {grd.Xn[1]['hi'][0]:.1%}")
        print(f"初始SOC: {grd.X0[1]:.1%}")
        print(f"时间步数: {prb.N}")
        print(f"驾驶循环: 速度 {prb.W[0]} m/s, 加速度 {prb.W[1]} m/s²")

        # 调用 dpm_backward 函数
        dyn = dpm_backward(phev_model_adapter, None, grd, prb, options)

        print("PHEV动态规划计算完成!")

        # 检查结果
        print(f"结果类型: {type(dyn)}")
        print(f"代价函数键: {list(dyn.Jo.keys()) if hasattr(dyn, 'Jo') else 'None'}")
        print(f"最优输入键: {list(dyn.Uo.keys()) if hasattr(dyn, 'Uo') else 'None'}")

        if hasattr(dyn, 'Jo') and 1 in dyn.Jo:
            if isinstance(dyn.Jo[1], dict):
                print(f"代价函数是字典，键: {list(dyn.Jo[1].keys())}")
                if dyn.Jo[1]:  # 如果字典不为空
                    first_key = list(dyn.Jo[1].keys())[0]
                    if hasattr(dyn.Jo[1][first_key], 'shape'):
                        print(f"代价函数形状: {dyn.Jo[1][first_key].shape}")
                        print(f"代价函数范围: [{np.min(dyn.Jo[1][first_key]):.4f}, {np.max(dyn.Jo[1][first_key]):.4f}]")
            else:
                print(f"代价函数形状: {dyn.Jo[1].shape}")
                print(f"代价函数范围: [{np.min(dyn.Jo[1]):.4f}, {np.max(dyn.Jo[1]):.4f}]")

        if hasattr(dyn, 'Uo'):
            for i in [1, 2]:
                if i in dyn.Uo:
                    print(f"最优输入{i}时间步数: {len(dyn.Uo[i])}")

        print("✓ PHEV模型测试通过")
        return True

    except Exception as e:
        print(f"✗ PHEV模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_1d_backward():
    """测试基本的一维动态规划"""
    print("=" * 60)
    print("测试 2: 基本一维动态规划")
    print("=" * 60)
    
    try:
        grd, prb, options = create_1d_test_case()
        
        print("开始一维动态规划计算...")
        
        # 调用 dpm_backward 函数
        dyn = dpm_backward(simple_1d_model, None, grd, prb, options)
        
        print("一维动态规划计算完成!")
        
        # 检查结果
        print(f"结果类型: {type(dyn)}")
        print(f"代价函数键: {list(dyn.Jo.keys()) if hasattr(dyn, 'Jo') else 'None'}")
        print(f"最优输入键: {list(dyn.Uo.keys()) if hasattr(dyn, 'Uo') else 'None'}")
        
        if hasattr(dyn, 'Jo') and 1 in dyn.Jo:
            if isinstance(dyn.Jo[1], dict):
                print(f"代价函数是字典，键: {list(dyn.Jo[1].keys())}")
                if dyn.Jo[1]:  # 如果字典不为空
                    first_key = list(dyn.Jo[1].keys())[0]
                    if hasattr(dyn.Jo[1][first_key], 'shape'):
                        print(f"代价函数形状: {dyn.Jo[1][first_key].shape}")
            else:
                print(f"代价函数形状: {dyn.Jo[1].shape}")
                print(f"代价函数范围: [{np.min(dyn.Jo[1]):.4f}, {np.max(dyn.Jo[1]):.4f}]")
        
        if hasattr(dyn, 'Uo') and 1 in dyn.Uo:
            print(f"最优输入时间步数: {len(dyn.Uo[1])}")
        
        print("✓ 基本一维动态规划测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本一维动态规划测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_2d_backward():
    """测试基本的二维动态规划"""
    print("=" * 60)
    print("测试 3: 基本二维动态规划")
    print("=" * 60)
    
    try:
        grd, prb, options = create_2d_test_case()
        
        print("开始二维动态规划计算...")
        
        # 调用 dpm_backward 函数
        dyn = dpm_backward(simple_2d_model, None, grd, prb, options)
        
        print("二维动态规划计算完成!")
        
        # 检查结果
        print(f"结果类型: {type(dyn)}")
        print(f"代价函数键: {list(dyn.Jo.keys()) if hasattr(dyn, 'Jo') else 'None'}")
        print(f"最优输入键: {list(dyn.Uo.keys()) if hasattr(dyn, 'Uo') else 'None'}")
        
        if hasattr(dyn, 'Jo') and 1 in dyn.Jo:
            if isinstance(dyn.Jo[1], dict):
                print(f"代价函数是字典，键: {list(dyn.Jo[1].keys())}")
                if dyn.Jo[1]:  # 如果字典不为空
                    first_key = list(dyn.Jo[1].keys())[0]
                    if hasattr(dyn.Jo[1][first_key], 'shape'):
                        print(f"代价函数形状: {dyn.Jo[1][first_key].shape}")
            else:
                print(f"代价函数形状: {dyn.Jo[1].shape}")
                print(f"代价函数范围: [{np.min(dyn.Jo[1]):.4f}, {np.max(dyn.Jo[1]):.4f}]")
        
        if hasattr(dyn, 'Uo'):
            for i in [1, 2]:
                if i in dyn.Uo:
                    print(f"最优输入{i}时间步数: {len(dyn.Uo[i])}")
        
        print("✓ 基本二维动态规划测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本二维动态规划测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_options_variations():
    """测试不同选项设置"""
    print("=" * 60)
    print("测试 4: 不同选项设置")
    print("=" * 60)
    
    try:
        grd, prb, _ = create_1d_test_case()
        
        # 测试不同的选项组合
        test_cases = [
            {"name": "最大化问题", "Minimize": 0},
            {"name": "不保存映射", "SaveMap": 'off'},
            {"name": "高精度", "MyInf": 1e6},
            {"name": "静默模式", "Verbose": 'off', "Warnings": 'off'},
        ]
        
        for test_case in test_cases:
            print(f"\n测试子案例: {test_case['name']}")
            
            options = Options()
            options.MyInf = 1e4
            options.Minimize = 1
            options.Verbose = 'off'
            options.Warnings = 'off'
            options.SaveMap = 'on'
            
            # 应用测试案例的特定选项
            for key, value in test_case.items():
                if key != "name":
                    setattr(options, key, value)
            
            # 运行测试
            dyn = dpm_backward(simple_1d_model, None, grd, prb, options)
            
            print(f"  ✓ {test_case['name']} 测试通过")
        
        print("✓ 选项变化测试全部通过")
        return True
        
    except Exception as e:
        print(f"✗ 选项变化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """测试边界情况和错误处理"""
    print("=" * 60)
    print("测试 5: 边界情况和错误处理")
    print("=" * 60)

    try:
        # 测试案例1: 单时间步问题
        print("\n子测试 4.1: 单时间步问题")
        grd, _, _ = create_1d_test_case()
        prb = Problem(Ts=1.0, N=1)  # 只有一个时间步
        prb.W = {}
        options = Options()
        options.Verbose = 'off'
        options.Warnings = 'off'

        dyn = dpm_backward(simple_1d_model, None, grd, prb, options)
        print("  ✓ 单时间步测试通过")

        # 测试案例2: 大网格问题
        print("\n子测试 4.2: 大网格问题")
        grd_large = Grid()
        grd_large.X0 = {1: 0.0}
        grd_large.XN = {1: {'lo': 0.9, 'hi': 1.1}}
        grd_large.Nx = {1: [50] * 3}  # 大网格
        grd_large.Xn = {1: {'lo': [0.0] * 3, 'hi': [1.0] * 3}}
        grd_large.Nu = {1: [20] * 2}
        grd_large.Un = {1: {'lo': [-0.5] * 2, 'hi': [0.5] * 2}}

        prb_large = Problem(Ts=0.5, N=2)
        prb_large.W = {}

        dyn = dpm_backward(simple_1d_model, None, grd_large, prb_large, options)
        print("  ✓ 大网格测试通过")

        # 测试案例3: 紧约束问题
        print("\n子测试 4.3: 紧约束问题")
        grd_tight = Grid()
        grd_tight.X0 = {1: 0.5}
        grd_tight.XN = {1: {'lo': 0.49, 'hi': 0.51}}  # 非常紧的约束
        grd_tight.Nx = {1: [10] * 3}
        grd_tight.Xn = {1: {'lo': [0.0] * 3, 'hi': [1.0] * 3}}
        grd_tight.Nu = {1: [5] * 2}
        grd_tight.Un = {1: {'lo': [-0.1] * 2, 'hi': [0.1] * 2}}

        prb_tight = Problem(Ts=0.1, N=2)
        prb_tight.W = {}

        dyn = dpm_backward(simple_1d_model, None, grd_tight, prb_tight, options)
        print("  ✓ 紧约束测试通过")

        print("✓ 边界情况测试全部通过")
        return True

    except Exception as e:
        print(f"✗ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_infeasible_model():
    """测试不可行模型"""
    print("=" * 60)
    print("测试 6: 不可行模型处理")
    print("=" * 60)

    def infeasible_model(inp, par=None):
        """总是返回不可行的模型"""
        X = {1: inp.X[1] + inp.U[1] * inp.Ts}
        C = {1: inp.U[1]**2 * inp.Ts}
        I = np.ones_like(inp.X[1])  # 总是不可行

        # 创建输出结构
        class Output:
            def __init__(self):
                self.X = X
                self.C = C
                self.I = I

        return Output(), None

    try:
        grd, prb, options = create_1d_test_case()
        options.Verbose = 'off'
        options.Warnings = 'off'

        print("测试完全不可行的模型...")

        # 这应该会产生警告或特殊处理
        dyn = dpm_backward(infeasible_model, None, grd, prb, options)

        print("不可行模型处理完成")

        # 检查结果是否合理
        if hasattr(dyn, 'Jo') and 1 in dyn.Jo:
            if isinstance(dyn.Jo[1], dict):
                print("代价函数是字典结构")
                if dyn.Jo[1]:
                    first_key = list(dyn.Jo[1].keys())[0]
                    cost_array = dyn.Jo[1][first_key]
                    if hasattr(cost_array, 'size'):
                        inf_count = np.sum(cost_array >= options.MyInf)
                        total_count = cost_array.size
                        print(f"无穷大代价比例: {inf_count}/{total_count} = {inf_count/total_count:.2%}")
            else:
                inf_count = np.sum(dyn.Jo[1] >= options.MyInf)
                total_count = dyn.Jo[1].size
                print(f"无穷大代价比例: {inf_count}/{total_count} = {inf_count/total_count:.2%}")

        print("✓ 不可行模型测试通过")
        return True

    except Exception as e:
        print(f"✗ 不可行模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_benchmark():
    """性能基准测试"""
    print("=" * 60)
    print("测试 7: 性能基准测试")
    print("=" * 60)

    import time

    try:
        # 创建不同规模的问题
        test_cases = [
            {"name": "小规模 (5x5)", "nx": 5, "nu": 5, "N": 3},
            {"name": "中等规模 (10x10)", "nx": 10, "nu": 10, "N": 5},
            {"name": "大规模 (20x15)", "nx": 20, "nu": 15, "N": 4},
        ]

        for case in test_cases:
            print(f"\n性能测试: {case['name']}")

            # 创建测试案例
            grd = Grid()
            grd.X0 = {1: 0.0}
            grd.XN = {1: {'lo': 0.9, 'hi': 1.1}}
            grd.Nx = {1: [case['nx']] * (case['N'] + 1)}
            grd.Xn = {1: {'lo': [0.0] * (case['N'] + 1), 'hi': [1.0] * (case['N'] + 1)}}
            grd.Nu = {1: [case['nu']] * case['N']}
            grd.Un = {1: {'lo': [-0.5] * case['N'], 'hi': [0.5] * case['N']}}

            prb = Problem(Ts=0.2, N=case['N'])
            prb.W = {}

            options = Options()
            options.Verbose = 'off'
            options.Warnings = 'off'

            # 计时
            start_time = time.time()
            dyn = dpm_backward(simple_1d_model, None, grd, prb, options)
            end_time = time.time()

            elapsed = end_time - start_time
            print(f"  计算时间: {elapsed:.3f} 秒")

            # 计算问题规模
            total_states = case['nx'] ** (case['N'] + 1)
            total_inputs = case['nu'] ** case['N']
            print(f"  状态空间大小: {total_states:,}")
            print(f"  输入空间大小: {total_inputs:,}")
            print(f"  ✓ {case['name']} 完成")

        print("✓ 性能基准测试完成")
        return True

    except Exception as e:
        print(f"✗ 性能基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始 dpm_backward 函数测试")
    print("=" * 60)

    tests = [
        test_phev_model,
        test_basic_1d_backward,
        test_basic_2d_backward,
        test_options_variations,
        test_edge_cases,
        test_infeasible_model,
        test_performance_benchmark,
    ]

    results = []
    for test in tests:
        result = test()
        results.append(result)
        print()

    # 总结
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")

    if passed == total:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败")

    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
