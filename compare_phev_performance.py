#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比原版和优化版 phev_ht21_serip1p3 函数的性能
"""

import numpy as np
import time
import matplotlib.pyplot as plt
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3
from phev_ht21_serip1p3_optimized import phev_ht21_serip1p3_optimized


def create_test_input(grid_size=10):
    """创建测试输入数据"""
    inp = Input()
    
    # 创建网格化的输入数据
    inp.X = {1: np.random.uniform(0.4, 0.6, (grid_size, grid_size))}  # SOC
    inp.U = {
        1: np.random.uniform(-2.5, 1.0, (grid_size, grid_size)),  # 扭矩分配
        2: np.random.randint(0, 3, (grid_size, grid_size))        # 工作模式
    }
    inp.W = {
        1: np.random.uniform(0, 30, (grid_size, grid_size)),      # 速度 m/s
        2: np.random.uniform(-3, 3, (grid_size, grid_size))       # 加速度 m/s²
    }
    inp.Ts = 1.0
    
    return inp


def benchmark_function(func, inp, n_runs=10, warmup=2):
    """基准测试函数"""
    # 预热
    for _ in range(warmup):
        try:
            _ = func(inp, None)
        except Exception as e:
            print(f"预热时出错: {e}")
            return float('inf'), None
    
    # 计时测试
    times = []
    result = None
    
    for _ in range(n_runs):
        start_time = time.time()
        try:
            result = func(inp, None)
            end_time = time.time()
            times.append(end_time - start_time)
        except Exception as e:
            print(f"测试时出错: {e}")
            return float('inf'), None
    
    return np.mean(times), result


def compare_performance():
    """性能对比测试"""
    print("PHEV 函数性能对比测试")
    print("=" * 50)
    
    grid_sizes = [5, 10, 20, 30, 50]
    original_times = []
    optimized_times = []
    speedup_ratios = []
    
    for grid_size in grid_sizes:
        print(f"\n测试网格大小: {grid_size}x{grid_size} = {grid_size**2} 点")
        
        # 创建测试输入
        inp = create_test_input(grid_size)
        
        # 测试原版函数
        print("  测试原版函数...")
        original_time, original_result = benchmark_function(phev_ht21_serip1p3, inp)
        
        # 测试优化版函数
        print("  测试优化版函数...")
        optimized_time, optimized_result = benchmark_function(phev_ht21_serip1p3_optimized, inp)
        
        # 计算加速比
        if original_time != float('inf') and optimized_time != float('inf'):
            speedup = original_time / optimized_time
            speedup_ratios.append(speedup)
            original_times.append(original_time)
            optimized_times.append(optimized_time)
            
            print(f"  原版耗时: {original_time*1000:.2f} 毫秒")
            print(f"  优化版耗时: {optimized_time*1000:.2f} 毫秒")
            print(f"  加速比: {speedup:.2f}x")
            print(f"  每点耗时 (原版): {original_time*1000/grid_size**2:.3f} 毫秒")
            print(f"  每点耗时 (优化版): {optimized_time*1000/grid_size**2:.3f} 毫秒")
        else:
            print("  测试失败!")
            speedup_ratios.append(0)
            original_times.append(0)
            optimized_times.append(0)
    
    return grid_sizes, original_times, optimized_times, speedup_ratios


def analyze_memory_usage():
    """分析内存使用情况"""
    print("\n=== 内存使用分析 ===")
    
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    
    # 测试大网格的内存使用
    grid_size = 100
    inp = create_test_input(grid_size)
    
    # 原版函数内存使用
    mem_before = process.memory_info().rss / 1024 / 1024  # MB
    _ = phev_ht21_serip1p3(inp, None)
    mem_after_original = process.memory_info().rss / 1024 / 1024  # MB
    
    # 优化版函数内存使用
    mem_before_opt = process.memory_info().rss / 1024 / 1024  # MB
    _ = phev_ht21_serip1p3_optimized(inp, None)
    mem_after_opt = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"原版函数内存增长: {mem_after_original - mem_before:.1f} MB")
    print(f"优化版函数内存增长: {mem_after_opt - mem_before_opt:.1f} MB")


def plot_performance_comparison(grid_sizes, original_times, optimized_times, speedup_ratios):
    """绘制性能对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 执行时间对比
    ax1.plot(grid_sizes, [t*1000 for t in original_times], 'o-', label='原版函数', linewidth=2, markersize=8)
    ax1.plot(grid_sizes, [t*1000 for t in optimized_times], 's-', label='优化版函数', linewidth=2, markersize=8)
    ax1.set_xlabel('网格大小')
    ax1.set_ylabel('执行时间 (毫秒)')
    ax1.set_title('执行时间对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    
    # 加速比
    ax2.bar(range(len(grid_sizes)), speedup_ratios, alpha=0.7, color='green')
    ax2.set_xlabel('网格大小')
    ax2.set_ylabel('加速比')
    ax2.set_title('性能提升倍数')
    ax2.set_xticks(range(len(grid_sizes)))
    ax2.set_xticklabels([f'{size}x{size}' for size in grid_sizes])
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, ratio in enumerate(speedup_ratios):
        ax2.text(i, ratio + 0.05, f'{ratio:.2f}x', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('phev_performance_comparison.png', dpi=300, bbox_inches='tight')
    print(f"\n性能对比图已保存为: phev_performance_comparison.png")
    
    return fig


def analyze_bottlenecks():
    """分析性能瓶颈"""
    print("\n=== 性能瓶颈分析 ===")
    
    # 分析原版函数的主要瓶颈
    print("原版函数主要瓶颈:")
    print("1. 重复创建插值器 - 每次调用都重新创建interp1d和RegularGridInterpolator对象")
    print("2. 大量的条件判断 - 使用复杂的逻辑表达式进行数组操作")
    print("3. 内存分配 - 频繁创建临时数组")
    print("4. 数值计算稳定性 - sqrt操作可能产生NaN值")
    
    print("\n优化版函数的改进:")
    print("1. 预计算插值器 - 使用单例模式，插值器只创建一次")
    print("2. 优化条件判断 - 预计算布尔掩码，减少重复计算")
    print("3. 向量化操作 - 使用NumPy的向量化函数")
    print("4. 数值稳定性 - 添加错误处理和边界检查")
    
    print("\n进一步优化建议:")
    print("1. 使用Numba JIT编译加速数值计算")
    print("2. 实现自定义的快速插值算法")
    print("3. 使用内存池减少内存分配开销")
    print("4. 并行化独立的计算部分")


def main():
    """主函数"""
    print("开始性能对比测试...")
    
    # 1. 性能对比测试
    grid_sizes, original_times, optimized_times, speedup_ratios = compare_performance()
    
    # 2. 内存使用分析
    try:
        analyze_memory_usage()
    except ImportError:
        print("psutil未安装，跳过内存分析")
    
    # 3. 绘制对比图
    if any(t > 0 for t in original_times):
        plot_performance_comparison(grid_sizes, original_times, optimized_times, speedup_ratios)
    
    # 4. 瓶颈分析
    analyze_bottlenecks()
    
    # 5. 总结
    print("\n=== 性能测试总结 ===")
    if speedup_ratios:
        avg_speedup = np.mean([r for r in speedup_ratios if r > 0])
        max_speedup = max(speedup_ratios) if speedup_ratios else 0
        print(f"平均加速比: {avg_speedup:.2f}x")
        print(f"最大加速比: {max_speedup:.2f}x")
        
        if avg_speedup > 1.5:
            print("✅ 优化效果显著!")
        elif avg_speedup > 1.1:
            print("✅ 优化有一定效果")
        else:
            print("⚠️  优化效果有限")
    
    print("\n建议:")
    print("1. 在实际应用中使用优化版函数")
    print("2. 考虑进一步的优化措施")
    print("3. 根据具体使用场景调整优化策略")


if __name__ == "__main__":
    main()
