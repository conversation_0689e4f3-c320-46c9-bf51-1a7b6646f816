#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试 dpm_sub2indr 函数的脚本

这个脚本用于验证Python版本的dpm_sub2indr函数是否与MATLAB版本完全一致
"""

import numpy as np
from dpm import dpm_sub2indr

def test_matlab_example():
    """测试与MATLAB版本相同的例子"""
    print("=" * 60)
    print("测试 dpm_sub2indr 函数 - 详细验证")
    print("=" * 60)
    
    # 测试用例1: 基本情况
    print("测试用例1: 基本情况")
    sze = [3, 4]  # 3x4矩阵
    vl = np.array([1, 2])  # 下界
    vu = np.array([2, 3])  # 上界
    dim = 2  # 第二维
    
    ind, col = dpm_sub2indr(sze, vl, vu, dim)
    
    print(f"  矩阵大小: {sze}")
    print(f"  下界: {vl}")
    print(f"  上界: {vu}")
    print(f"  维度: {dim}")
    print(f"  索引: {ind}")
    print(f"  列: {col}")
    
    # 手动计算期望结果来验证
    # MATLAB逻辑分析:
    # ind0 = [0:sze(dim)-1].*sze(1) = [0:4-1]*3 = [0, 3, 6, 9]
    # ind = reshape([(vl+ind0); (vu+ind0)],1,numel(ind0)*2)
    #     = reshape([[1,2]+[0,3,6,9]; [2,3]+[0,3,6,9]], 1, 8)
    #     = reshape([[1,5,7,10]; [2,6,8,11]], 1, 8)
    #     = [1,2,5,6,7,8,10,11]
    # 然后通过eval生成范围: [1:2, 5:6, 7:8, 10:11] = [1,2,5,6,7,8,10,11]
    
    print("\n  手动验证MATLAB逻辑:")
    ind0 = np.arange(sze[dim-1]) * sze[0]  # [0, 3, 6, 9]
    print(f"  ind0 = {ind0}")
    
    # 生成上下界索引对
    vl_expanded = vl + ind0[:, np.newaxis]  # 广播
    vu_expanded = vu + ind0[:, np.newaxis]
    print(f"  vl_expanded = {vl_expanded}")
    print(f"  vu_expanded = {vu_expanded}")
    
    # 测试用例2: 不同参数
    print("\n测试用例2: 不同参数")
    sze2 = [5, 3]
    vl2 = np.array([2])
    vu2 = np.array([4])
    dim2 = 2
    
    ind2, col2 = dpm_sub2indr(sze2, vl2, vu2, dim2)
    print(f"  矩阵大小: {sze2}")
    print(f"  下界: {vl2}")
    print(f"  上界: {vu2}")
    print(f"  维度: {dim2}")
    print(f"  索引: {ind2}")
    print(f"  列: {col2}")
    
    # 测试用例3: 单个值
    print("\n测试用例3: 单个值")
    sze3 = [4, 5]
    vl3 = np.array([1])
    vu3 = np.array([1])  # 上下界相同
    dim3 = 2
    
    ind3, col3 = dpm_sub2indr(sze3, vl3, vu3, dim3)
    print(f"  矩阵大小: {sze3}")
    print(f"  下界: {vl3}")
    print(f"  上界: {vu3}")
    print(f"  维度: {dim3}")
    print(f"  索引: {ind3}")
    print(f"  列: {col3}")

def analyze_matlab_logic():
    """分析MATLAB版本的复杂逻辑"""
    print("\n" + "=" * 60)
    print("分析MATLAB版本的逻辑")
    print("=" * 60)
    
    # 模拟MATLAB的复杂字符串操作
    sze = [3, 4]
    vl = np.array([1, 2])
    vu = np.array([2, 3])
    dim = 2
    
    # 步骤1: ind0 = [0:sze(dim)-1].*sze(1)
    ind0 = np.arange(sze[dim-1]) * sze[0]
    print(f"步骤1 - ind0: {ind0}")
    
    # 步骤2: ind = reshape([(vl+ind0); (vu+ind0)],1,numel(ind0)*2)
    vl_plus_ind0 = vl[:, np.newaxis] + ind0  # 广播
    vu_plus_ind0 = vu[:, np.newaxis] + ind0
    print(f"步骤2a - vl+ind0: {vl_plus_ind0}")
    print(f"步骤2b - vu+ind0: {vu_plus_ind0}")
    
    # 在MATLAB中，这会被reshape为一行
    combined = np.vstack([vl_plus_ind0, vu_plus_ind0])
    print(f"步骤2c - 合并: {combined}")
    
    # MATLAB的reshape是列优先的
    ind_pairs = combined.T.flatten()  # 转置后展平，模拟MATLAB的列优先
    print(f"步骤2d - 重塑后: {ind_pairs}")
    
    # 步骤3-4: MATLAB使用eval和字符串操作生成范围
    # 这是关键部分，MATLAB生成类似 [1:2, 5:6, 7:8, 10:11] 的范围
    print(f"\n步骤3-4 - MATLAB生成范围:")
    ranges = []
    for i in range(0, len(ind_pairs), 2):
        start = ind_pairs[i]
        end = ind_pairs[i+1]
        range_vals = list(range(int(start), int(end) + 1))
        ranges.extend(range_vals)
        print(f"  范围 {start}:{end} -> {range_vals}")
    
    print(f"最终索引: {ranges}")
    
    # 步骤5: col = ceil((ind)/sze(1))
    col = np.ceil(np.array(ranges) / sze[0]).astype(int)
    print(f"步骤5 - 列索引: {col}")

if __name__ == "__main__":
    test_matlab_example()
    analyze_matlab_logic()
