#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 phev_ht21_serip1p3.py 函数的性能问题
"""

import numpy as np
import time
import cProfile
import pstats
from io import StringIO
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input(grid_size=10):
    """创建测试输入数据"""
    inp = Input()
    
    # 创建网格化的输入数据
    inp.X = {1: np.random.uniform(0.4, 0.6, (grid_size, grid_size))}  # SOC
    inp.U = {
        1: np.random.uniform(-2.5, 1.0, (grid_size, grid_size)),  # 扭矩分配
        2: np.random.randint(0, 3, (grid_size, grid_size))        # 工作模式
    }
    inp.W = {
        1: np.random.uniform(0, 30, (grid_size, grid_size)),      # 速度 m/s
        2: np.random.uniform(-3, 3, (grid_size, grid_size))       # 加速度 m/s²
    }
    inp.Ts = 1.0
    
    return inp


def profile_function_calls():
    """分析函数调用的性能"""
    print("=== 函数调用性能分析 ===")
    
    # 测试不同网格大小
    grid_sizes = [5, 10, 20, 50]
    
    for grid_size in grid_sizes:
        print(f"\n网格大小: {grid_size}x{grid_size} = {grid_size**2} 点")
        
        inp = create_test_input(grid_size)
        
        # 预热
        _ = phev_ht21_serip1p3(inp, None)
        
        # 计时测试
        start_time = time.time()
        n_runs = 5
        for _ in range(n_runs):
            X, C, I, out = phev_ht21_serip1p3(inp, None)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / n_runs
        time_per_point = avg_time / (grid_size**2)
        
        print(f"  平均耗时: {avg_time:.4f}秒")
        print(f"  每点耗时: {time_per_point*1000:.2f}毫秒")
        print(f"  吞吐量: {(grid_size**2)/avg_time:.1f} 点/秒")


def profile_detailed_analysis():
    """详细的性能分析"""
    print("\n=== 详细性能分析 ===")
    
    inp = create_test_input(20)  # 使用中等大小的网格
    
    # 使用cProfile进行详细分析
    pr = cProfile.Profile()
    pr.enable()
    
    # 运行函数
    X, C, I, out = phev_ht21_serip1p3(inp, None)
    
    pr.disable()
    
    # 分析结果
    s = StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(20)  # 显示前20个最耗时的函数
    
    print("前20个最耗时的函数调用:")
    print(s.getvalue())


def analyze_interpolation_performance():
    """分析插值操作的性能"""
    print("\n=== 插值操作性能分析 ===")
    
    from scipy.interpolate import interp1d, RegularGridInterpolator
    
    # 测试1D插值性能
    print("1D插值性能测试:")
    x_data = np.linspace(0, 100, 100)
    y_data = np.random.random(100)
    query_points = np.random.uniform(0, 100, 10000)
    
    start_time = time.time()
    interp_func = interp1d(x_data, y_data, kind='linear', bounds_error=False, fill_value='extrapolate')
    result = interp_func(query_points)
    end_time = time.time()
    print(f"  1D插值 (10000点): {(end_time - start_time)*1000:.2f}毫秒")
    
    # 测试2D插值性能
    print("2D插值性能测试:")
    x_grid = np.linspace(0, 100, 50)
    y_grid = np.linspace(0, 100, 50)
    z_data = np.random.random((50, 50))
    query_points_2d = np.column_stack([
        np.random.uniform(0, 100, 1000),
        np.random.uniform(0, 100, 1000)
    ])
    
    start_time = time.time()
    interp_func_2d = RegularGridInterpolator((x_grid, y_grid), z_data, bounds_error=False, fill_value=None)
    result_2d = interp_func_2d(query_points_2d)
    end_time = time.time()
    print(f"  2D插值 (1000点): {(end_time - start_time)*1000:.2f}毫秒")


def analyze_array_operations():
    """分析数组操作的性能"""
    print("\n=== 数组操作性能分析 ===")
    
    # 测试不同大小的数组操作
    sizes = [100, 1000, 10000, 100000]
    
    for size in sizes:
        print(f"\n数组大小: {size}")
        
        # 创建测试数组
        a = np.random.random(size)
        b = np.random.random(size)
        
        # 测试基本运算
        start_time = time.time()
        c = a * b + np.sin(a) / np.cos(b)
        end_time = time.time()
        print(f"  复杂运算: {(end_time - start_time)*1000:.2f}毫秒")
        
        # 测试条件操作
        start_time = time.time()
        result = np.where(a > 0.5, a * 2, b / 2)
        end_time = time.time()
        print(f"  条件操作: {(end_time - start_time)*1000:.2f}毫秒")
        
        # 测试逻辑操作
        start_time = time.time()
        mask = (a > 0.3) & (b < 0.7) | (a < 0.1)
        end_time = time.time()
        print(f"  逻辑操作: {(end_time - start_time)*1000:.2f}毫秒")


def compare_with_matlab_equivalent():
    """与MATLAB等效操作的比较"""
    print("\n=== 与MATLAB等效操作比较 ===")
    
    # 模拟MATLAB中的典型操作
    size = 10000
    
    # MATLAB风格的操作
    print("MATLAB风格操作:")
    a = np.random.random(size)
    b = np.random.random(size)
    
    start_time = time.time()
    # 模拟 (a~=0) .* func(a) + (a==0)
    result = np.where(a != 0, np.sin(a), 1.0)
    end_time = time.time()
    print(f"  条件表达式: {(end_time - start_time)*1000:.2f}毫秒")
    
    start_time = time.time()
    # 模拟 interp1 操作
    x_data = np.linspace(0, 1, 100)
    y_data = np.sin(x_data)
    from scipy.interpolate import interp1d
    interp_func = interp1d(x_data, y_data, kind='linear', bounds_error=False, fill_value='extrapolate')
    result = interp_func(a)
    end_time = time.time()
    print(f"  插值操作: {(end_time - start_time)*1000:.2f}毫秒")


def main():
    """主函数"""
    print("PHEV HT21 SerIP1P3 性能分析")
    print("=" * 50)
    
    # 1. 函数调用性能分析
    profile_function_calls()
    
    # 2. 详细性能分析
    profile_detailed_analysis()
    
    # 3. 插值操作性能分析
    analyze_interpolation_performance()
    
    # 4. 数组操作性能分析
    analyze_array_operations()
    
    # 5. 与MATLAB等效操作比较
    compare_with_matlab_equivalent()
    
    print("\n=== 性能分析完成 ===")


if __name__ == "__main__":
    main()
