#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试六维插值函数
"""

import numpy as np
from dpm import dpm_interpn

def main():
    print("简单测试六维插值函数")
    
    try:
        # 创建简单的2x2x2x2x2x2网格
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        xx4 = np.array([0, 1])
        xx5 = np.array([0, 1])
        xx6 = np.array([0, 1])
        
        # 创建值矩阵
        YY = np.zeros((2, 2, 2, 2, 2, 2))
        for i in range(2):
            for j in range(2):
                for k in range(2):
                    for l in range(2):
                        for m in range(2):
                            for n in range(2):
                                YY[i, j, k, l, m, n] = i + j + k + l + m + n
        
        print(f"网格: xx1={xx1}, xx2={xx2}, xx3={xx3}, xx4={xx4}, xx5={xx5}, xx6={xx6}")
        print(f"YY形状: {YY.shape}")
        
        # 测试角点
        A1 = np.array([0])
        A2 = np.array([0])
        A3 = np.array([0])
        A4 = np.array([0])
        A5 = np.array([0])
        A6 = np.array([0])
        
        print(f"测试角点: A1={A1[0]}, A2={A2[0]}, A3={A3[0]}, A4={A4[0]}, A5={A5[0]}, A6={A6[0]}")
        
        # 使用新的参数顺序：dpm_interpn(xx6, xx5, xx4, xx3, xx2, xx1, YY, A6, A5, A4, A3, A2, A1)
        result = dpm_interpn(xx6, xx5, xx4, xx3, xx2, xx1, YY, A6, A5, A4, A3, A2, A1)
        
        print(f"结果: {result[0]}")
        print(f"期望: {YY[0,0,0,0,0,0]}")
        
        if abs(result[0] - YY[0,0,0,0,0,0]) < 1e-10:
            print("✓ 六维插值测试通过")
        else:
            print("✗ 六维插值测试失败")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
