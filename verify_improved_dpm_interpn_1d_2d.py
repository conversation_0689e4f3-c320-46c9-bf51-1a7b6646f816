#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证改进后的一维和二维插值功能的脚本

这个脚本快速验证根据MATLAB版本改进后的一维和二维插值功能
"""

import numpy as np
from dpm import dpm_interpn


def main():
    """主验证函数"""
    print("快速验证改进后的一维和二维插值功能")
    print("=" * 40)
    
    try:
        # 测试一维插值
        print("1. 测试改进后的一维插值:")
        xx = np.array([0, 1, 2, 3, 4])
        yy = np.array([0, 1, 4, 9, 16])  # y = x^2
        A = np.array([1.5, 2.5])
        
        y1d = dpm_interpn(xx, yy, A)
        expected1d = A**2
        
        print(f"   输入: xx={xx}, yy={yy}")
        print(f"   查询点: A={A}")
        print(f"   结果: {y1d}")
        print(f"   期望: {expected1d}")
        print(f"   误差: {np.abs(y1d - expected1d)}")
        
        # 验证精度
        error_1d = np.max(np.abs(y1d - expected1d))
        print(f"   ✓ 一维插值精度良好" if error_1d < 1e-10 else f"   ⚠️ 一维插值误差: {error_1d:.8f}")
        
        # 测试二维插值
        print("\n2. 测试改进后的二维插值:")
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        
        # 创建线性函数值矩阵 f(x1,x2) = x1 + 2*x2
        YY = np.array([[0, 1, 2],    # x2=0: [0, 1, 2]
                       [2, 3, 4]])   # x2=1: [2, 3, 4]
        
        A1 = np.array([1.0])
        A2 = np.array([0.5])
        
        y2d = dpm_interpn(xx1, xx2, YY, A1, A2)
        expected2d = A1 + 2*A2  # 1.0 + 2*0.5 = 2.0
        
        print(f"   输入: xx1={xx1}, xx2={xx2}")
        print(f"   YY矩阵:\n{YY}")
        print(f"   查询点: A1={A1}, A2={A2}")
        print(f"   结果: {y2d}")
        print(f"   期望: {expected2d}")
        print(f"   误差: {np.abs(y2d - expected2d)}")
        
        # 验证精度
        error_2d = np.max(np.abs(y2d - expected2d))
        print(f"   ✓ 二维插值精度良好" if error_2d < 1e-10 else f"   ⚠️ 二维插值误差: {error_2d:.8f}")
        
        # 测试角点精确性
        print("\n3. 测试角点精确性:")
        corner_tests = [
            (0, 0, YY[0, 0]),  # (0,0) -> 0
            (2, 0, YY[0, 2]),  # (2,0) -> 2
            (0, 1, YY[1, 0]),  # (0,1) -> 2
            (2, 1, YY[1, 2]),  # (2,1) -> 4
        ]
        
        max_corner_error = 0
        for a1, a2, expected in corner_tests:
            A1_corner, A2_corner = np.array([a1]), np.array([a2])
            y_corner = dpm_interpn(xx1, xx2, YY, A1_corner, A2_corner)
            error = abs(y_corner[0] - expected)
            max_corner_error = max(max_corner_error, error)
            print(f"   ({a1}, {a2}): y = {y_corner[0]:.8f}, 期望 = {expected:.8f}, 误差 = {error:.10f}")
        
        print(f"   最大角点误差: {max_corner_error:.10f}")
        
        # 测试边界处理
        print("\n4. 测试边界处理:")
        boundary_tests = [
            (-0.5, 0.5, "第一维下边界外"),
            (2.5, 0.5, "第一维上边界外"),
            (1.0, -0.5, "第二维下边界外"),
            (1.0, 1.5, "第二维上边界外"),
        ]
        
        for a1, a2, description in boundary_tests:
            A1_bound, A2_bound = np.array([a1]), np.array([a2])
            y_bound = dpm_interpn(xx1, xx2, YY, A1_bound, A2_bound)
            print(f"   ({a1:4.1f}, {a2:4.1f}): y = {y_bound[0]:8.4f} ({description})")
        
        # 测试向量化
        print("\n5. 测试向量化输入:")
        A1_vec = np.array([0.5, 1.5])
        A2_vec = np.array([0.3, 0.7])
        y_vec = dpm_interpn(xx1, xx2, YY, A1_vec, A2_vec)
        expected_vec = A1_vec + 2*A2_vec
        
        print(f"   向量输入: A1={A1_vec}, A2={A2_vec}")
        print(f"   向量结果: {y_vec}")
        print(f"   期望结果: {expected_vec}")
        print(f"   向量误差: {np.abs(y_vec - expected_vec)}")
        
        print("\n验证完成!")
        
        # 总体评估
        if error_1d < 1e-10 and error_2d < 1e-10 and max_corner_error < 1e-10:
            print("✓ 改进后的插值函数精度极高，所有测试误差 < 1e-10")
        elif error_1d < 1e-6 and error_2d < 1e-6:
            print("✓ 改进后的插值函数精度良好，所有测试误差 < 1e-6")
        else:
            print(f"⚠️ 改进后的插值函数精度一般，最大误差: 1D={error_1d:.8f}, 2D={error_2d:.8f}")
        
        print("✓ 改进后的一维和二维插值基本功能正常")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
