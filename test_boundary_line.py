#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试边界线函数的脚本

这个脚本测试 dpm_boundary_line_lower 函数的基本功能
"""

import numpy as np
from dpm import (dpm_boundary_line_lower, dpm_boundary_line_upper, Grid, Problem, Options, Input, Output)


def simple_test_model(inp, par=None):
    """
    简单的测试模型
    
    系统方程: x[k+1] = x[k] + u[k]*Ts
    代价函数: J = u[k]^2 * Ts
    """
    # 状态更新
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}
    
    # 代价函数
    C = {1: inp.U[1]**2 * inp.Ts}
    
    # 不可行性检查
    I = np.zeros_like(inp.X[1])
    
    return X, C, I


def test_boundary_line_lower():
    """测试下边界线计算函数"""
    print("=" * 60)
    print("测试 dpm_boundary_line_lower 函数")
    print("=" * 60)
    
    try:
        # 创建简单的网格结构
        grd = Grid()
        
        # 状态网格设置
        grd.Nx = {1: [11] * 11}  # 11个网格点，10个时间步
        grd.Xn = {1: {'lo': [-2.0] * 11, 'hi': [2.0] * 11}}
        grd.X0 = {1: 0.0}  # 初始状态
        grd.XN = {1: {'lo': 0.9, 'hi': 1.1}}  # 最终状态约束
        
        # 输入网格设置
        grd.Nu = {1: [5] * 10}  # 5个网格点，10个时间步
        grd.Un = {1: {'lo': [-1.0] * 10, 'hi': [1.0] * 10}}
        
        # 问题参数
        prb = Problem(Ts=0.1, N=10)
        prb.W = {}  # 无扰动
        
        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'on'
        options.Warnings = 'off'
        options.SaveMap = True
        options.Iter = 10
        options.Tol = 1e-8
        
        print("开始计算下边界线...")
        
        # 调用下边界线计算函数
        line_lower = dpm_boundary_line_lower(simple_test_model, None, grd, prb, options)
        
        print("下边界线计算完成!")
        
        # 检查结果
        print(f"边界状态轨迹形状: {line_lower.Xo.shape}")
        print(f"边界输入映射键: {list(line_lower.Uo.keys())}")
        print(f"边界代价轨迹形状: {line_lower.Jo.shape}")
        
        # 显示一些数值结果
        if hasattr(line_lower.Xo, 'shape') and line_lower.Xo.size > 0:
            print(f"边界状态轨迹前5个值: {line_lower.Xo.flat[:5]}")
        
        if hasattr(line_lower.Jo, 'shape') and line_lower.Jo.size > 0:
            print(f"边界代价轨迹前5个值: {line_lower.Jo.flat[:5]}")
        
        print("✓ dpm_boundary_line_lower 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_boundary_line_lower 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_line_upper():
    """测试上边界线计算函数"""
    print("\n" + "=" * 60)
    print("测试 dpm_boundary_line_upper 函数")
    print("=" * 60)

    try:
        # 创建简单的网格结构
        grd = Grid()

        # 状态网格设置
        grd.Nx = {1: [11] * 11}  # 11个网格点，10个时间步
        grd.Xn = {1: {'lo': [-2.0] * 11, 'hi': [2.0] * 11}}
        grd.X0 = {1: 0.0}  # 初始状态
        grd.XN = {1: {'lo': 0.9, 'hi': 1.1}}  # 最终状态约束

        # 输入网格设置
        grd.Nu = {1: [5] * 10}  # 5个网格点，10个时间步
        grd.Un = {1: {'lo': [-1.0] * 10, 'hi': [1.0] * 10}}

        # 问题参数
        prb = Problem(Ts=0.1, N=10)
        prb.W = {}  # 无扰动

        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'on'
        options.Warnings = 'off'
        options.SaveMap = True
        options.Iter = 10
        options.Tol = 1e-8

        print("开始计算上边界线...")

        # 调用上边界线计算函数
        line_upper = dpm_boundary_line_upper(simple_test_model, None, grd, prb, options)

        print("上边界线计算完成!")

        # 检查结果
        print(f"边界状态轨迹形状: {line_upper.Xo.shape}")
        print(f"边界输入映射键: {list(line_upper.Uo.keys())}")
        print(f"边界代价轨迹形状: {line_upper.Jo.shape}")

        # 显示一些数值结果
        if hasattr(line_upper.Xo, 'shape') and line_upper.Xo.size > 0:
            print(f"边界状态轨迹前5个值: {line_upper.Xo.flat[:5]}")

        if hasattr(line_upper.Jo, 'shape') and line_upper.Jo.size > 0:
            print(f"边界代价轨迹前5个值: {line_upper.Jo.flat[:5]}")

        print("✓ dpm_boundary_line_upper 测试通过")
        return True

    except Exception as e:
        print(f"✗ dpm_boundary_line_upper 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_line_components():
    """测试边界线计算的组件"""
    print("\n" + "=" * 60)
    print("测试边界线计算的组件函数")
    print("=" * 60)
    
    try:
        # 测试 dpm_interpn_placeholder
        from dpm import dpm_interpn_placeholder
        
        x_grid = np.linspace(0, 1, 5)
        values = np.array([0, 0.25, 0.5, 0.75, 1.0])
        x_query = np.array([0.1, 0.3, 0.7])
        
        result = dpm_interpn_placeholder(x_grid, values, x_query)
        print(f"插值测试结果: {result}")
        print("✓ dpm_interpn_placeholder 测试通过")
        
        # 测试 dmp_model_inv
        from dpm import dmp_model_inv
        
        inp = Input()
        inp.X = {1: np.array([0.5])}
        inp.U = {1: np.array([0.1])}
        inp.W = {}
        inp.Ts = 0.1
        
        par = {'model': simple_test_model, 'options': {'Iter': 10, 'Tol': 1e-8}}
        
        X, C, I = dmp_model_inv(inp, par)
        print(f"反演模型测试 - X: {X}, C: {C}, I: {I}")
        print("✓ dmp_model_inv 测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 组件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("边界线函数测试程序")
    print("这个程序测试 dpm_boundary_line_lower 函数及其组件")
    
    success_count = 0
    total_tests = 3

    # 测试1: 下边界线计算
    if test_boundary_line_lower():
        success_count += 1

    # 测试2: 上边界线计算
    if test_boundary_line_upper():
        success_count += 1

    # 测试3: 组件函数
    if test_boundary_line_components():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！dmp_boundary_line_lower 函数工作正常。")
    else:
        print("⚠️  部分测试失败。这可能是由于某些高级功能尚未完全实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
