#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 DPM 新增函数的脚本

这个脚本测试 dpm_setallfield, dpm_code, dpm_findu, dpm_findl, 
dpm_sub2indr, dpm_sub2ind 等函数
"""

import numpy as np
from dpm import (dpm_setallfield, dpm_code, dpm_findu, dpm_findl, 
                 dpm_sub2indr, dpm_sub2ind, Grid, Input)


def test_dpm_setallfield():
    """测试 dpm_setallfield 函数"""
    print("=" * 60)
    print("测试 dpm_setallfield 函数")
    print("=" * 60)
    
    try:
        # 测试1: 字典结构
        print("测试1: 字典结构")
        test_dict = {
            'a': 1.0,
            'b': [1, 2, 3],
            'c': {'x': 5.0, 'y': np.array([1, 2, 3])},
            'd': 'string_value'  # 非数值类型
        }
        
        print(f"  原始字典: {test_dict}")
        
        # 设置为零
        result_zero = dpm_setallfield(test_dict, 'zero')
        print(f"  设置为零: {result_zero}")
        
        # 设置为NaN
        result_nan = dpm_setallfield(test_dict, 'nan')
        print(f"  设置为NaN: {result_nan}")
        
        # 设置为无穷大
        result_inf = dpm_setallfield(test_dict, 'inf')
        print(f"  设置为inf: {result_inf}")
        
        # 测试2: 嵌套结构
        print("\n测试2: 嵌套结构")
        nested_dict = {
            'level1': {
                'level2': {
                    'value': 42.0,
                    'array': np.array([1, 2, 3])
                },
                'simple': 3.14
            },
            'top_level': [1, 2, 3, 4]
        }
        
        print(f"  原始嵌套结构: {nested_dict}")
        result_nested = dpm_setallfield(nested_dict, 'zero')
        print(f"  设置为零后: {result_nested}")
        
        print("✓ dpm_setallfield 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_setallfield 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_dpm_code():
    """测试 dpm_code 函数"""
    print("\n" + "=" * 60)
    print("测试 dpm_code 函数")
    print("=" * 60)
    
    try:
        # 测试1: 基本功能
        print("测试1: 基本功能")
        template = 'inp.U{#}'
        indices = [1, 2, 3]
        separator = ','
        
        result = dpm_code(template, indices, separator)
        expected = 'inp.U{1},inp.U{2},inp.U{3}'
        
        print(f"  模板: {template}")
        print(f"  索引: {indices}")
        print(f"  分隔符: '{separator}'")
        print(f"  结果: {result}")
        print(f"  期望: {expected}")
        print(f"  匹配: {result == expected}")
        
        # 测试2: 无分隔符
        print("\n测试2: 无分隔符")
        result_no_sep = dpm_code('X{#}', [1, 2], '')
        expected_no_sep = 'X{1}X{2}'
        
        print(f"  结果: {result_no_sep}")
        print(f"  期望: {expected_no_sep}")
        print(f"  匹配: {result_no_sep == expected_no_sep}")
        
        # 测试3: 单个索引
        print("\n测试3: 单个索引")
        result_single = dpm_code('var{#}', [5])
        expected_single = 'var{5}'
        
        print(f"  结果: {result_single}")
        print(f"  期望: {expected_single}")
        print(f"  匹配: {result_single == expected_single}")
        
        print("✓ dpm_code 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_code 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_dpm_findu_findl():
    """测试 dmp_findu 和 dpm_findl 函数"""
    print("\n" + "=" * 60)
    print("测试 dpm_findu 和 dpm_findl 函数")
    print("=" * 60)
    
    try:
        # 创建等间距数组
        A = np.array([0, 1, 2, 3, 4])  # 间距为1
        
        print(f"等间距数组 A: {A}")
        
        # 测试值
        test_values = [0.3, 1.7, 2.5, 3.9, -0.5, 4.5]
        
        print("\n测试 dpm_findu (上界索引):")
        for val in test_values:
            upper_idx = dpm_findu(A, val)
            print(f"  值 {val:.1f}: 上界索引 = {upper_idx}")
        
        print("\n测试 dpm_findl (下界索引):")
        for val in test_values:
            lower_idx = dpm_findl(A, val)
            print(f"  值 {val:.1f}: 下界索引 = {lower_idx}")
        
        # 测试数组输入
        print("\n测试数组输入:")
        vec = np.array([0.5, 1.5, 2.5])
        upper_indices = dpm_findu(A, vec)
        lower_indices = dpm_findl(A, vec)
        
        print(f"  输入向量: {vec}")
        print(f"  上界索引: {upper_indices}")
        print(f"  下界索引: {lower_indices}")
        
        # 验证边界情况
        print("\n边界情况验证:")
        edge_cases = [A[0], A[-1]]  # 数组的第一个和最后一个值
        for val in edge_cases:
            upper_idx = dpm_findu(A, val)
            lower_idx = dpm_findl(A, val)
            print(f"  值 {val}: 上界={upper_idx}, 下界={lower_idx}")
        
        print("✓ dpm_findu 和 dpm_findl 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_findu 和 dpm_findl 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_dpm_sub2ind():
    """测试 dpm_sub2ind 函数"""
    print("\n" + "=" * 60)
    print("测试 dpm_sub2ind 函数")
    print("=" * 60)
    
    try:
        # 测试1: 二维情况
        print("测试1: 二维情况")
        siz = (3, 4)  # 3行4列
        row_indices = np.array([1, 2, 3])  # 1基索引
        col_indices = np.array([1, 2, 3])
        
        linear_indices = dpm_sub2ind(siz, row_indices, col_indices)
        
        print(f"  矩阵大小: {siz}")
        print(f"  行索引: {row_indices}")
        print(f"  列索引: {col_indices}")
        print(f"  线性索引: {linear_indices}")
        
        # 验证：对于3x4矩阵，(1,1)应该是1，(2,1)应该是2，(1,2)应该是4
        expected = np.array([1, 5, 9])  # MATLAB列优先顺序
        print(f"  期望索引: {expected}")
        
        # 测试2: 三维情况
        print("\n测试2: 三维情况")
        siz_3d = (2, 3, 4)  # 2x3x4
        idx1 = np.array([1, 2])
        idx2 = np.array([1, 2])
        idx3 = np.array([1, 2])
        
        linear_indices_3d = dpm_sub2ind(siz_3d, idx1, idx2, idx3)
        
        print(f"  3D矩阵大小: {siz_3d}")
        print(f"  索引1: {idx1}")
        print(f"  索引2: {idx2}")
        print(f"  索引3: {idx3}")
        print(f"  线性索引: {linear_indices_3d}")
        
        # 测试3: 标量输入
        print("\n测试3: 标量输入")
        scalar_idx = dpm_sub2ind((5, 5), 3, 2)
        print(f"  5x5矩阵中(3,2)的线性索引: {scalar_idx}")
        
        print("✓ dpm_sub2ind 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_sub2ind 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_dpm_sub2indr():
    """测试 dpm_sub2indr 函数"""
    print("\n" + "=" * 60)
    print("测试 dpm_sub2indr 函数")
    print("=" * 60)
    
    try:
        # 测试基本功能
        print("测试基本功能:")
        sze = [3, 4]  # 3x4矩阵
        vl = np.array([1, 2])  # 下界
        vu = np.array([2, 3])  # 上界
        dim = 2  # 第二维
        
        ind, col = dpm_sub2indr(sze, vl, vu, dim)
        
        print(f"  矩阵大小: {sze}")
        print(f"  下界: {vl}")
        print(f"  上界: {vu}")
        print(f"  维度: {dim}")
        print(f"  索引: {ind}")
        print(f"  列: {col}")
        
        print("✓ dpm_sub2indr 函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_sub2indr 函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """集成测试：测试函数之间的协作"""
    print("\n" + "=" * 60)
    print("集成测试：函数协作")
    print("=" * 60)
    
    try:
        # 创建一个简单的工作流
        print("测试1: 索引查找和转换工作流")
        
        # 创建网格
        A = np.linspace(0, 10, 11)  # [0, 1, 2, ..., 10]
        query_points = np.array([2.3, 5.7, 8.1])
        
        # 找到上下界索引
        upper_indices = dpm_findu(A, query_points)
        lower_indices = dpm_findl(A, query_points)
        
        print(f"  网格: {A}")
        print(f"  查询点: {query_points}")
        print(f"  上界索引: {upper_indices}")
        print(f"  下界索引: {lower_indices}")
        
        # 使用sub2ind转换为线性索引
        matrix_size = (len(A), len(A))
        linear_upper = dpm_sub2ind(matrix_size, upper_indices, upper_indices)
        linear_lower = dpm_sub2ind(matrix_size, lower_indices, lower_indices)
        
        print(f"  上界线性索引: {linear_upper}")
        print(f"  下界线性索引: {linear_lower}")
        
        # 测试2: 代码生成
        print("\n测试2: 代码生成")
        indices = [1, 2, 3]
        code_str = dpm_code('data[#]', indices, ' + ')
        print(f"  生成的代码: {code_str}")
        
        print("✓ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("DPM 新增函数测试程序")
    print("这个程序测试新翻译的辅助函数")
    
    success_count = 0
    total_tests = 6
    
    # 测试1: dpm_setallfield
    if test_dpm_setallfield():
        success_count += 1
    
    # 测试2: dpm_code
    if test_dpm_code():
        success_count += 1
    
    # 测试3: dpm_findu 和 dpm_findl
    if test_dpm_findu_findl():
        success_count += 1
    
    # 测试4: dpm_sub2ind
    if test_dpm_sub2ind():
        success_count += 1
    
    # 测试5: dpm_sub2indr
    if test_dpm_sub2indr():
        success_count += 1
    
    # 测试6: 集成测试
    if test_integration():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有新增函数测试都通过了！")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
