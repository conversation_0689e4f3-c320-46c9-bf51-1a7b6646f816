#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的PHEV测试
"""

import numpy as np
from dpm import dpm, Grid, Problem, Options
from phev_ht21_serip1p3 import phev_ht21_serip1p3

def test_simple_phev():
    """测试简化的PHEV模型"""
    print("开始简化PHEV测试...")
    
    # 创建简化的网格
    grd = Grid()
    
    # 状态变量1 - 电池荷电状态 (SOC) - 使用更少的网格点
    grd.Nx = {1: 21}  # 减少到21个点
    grd.Xn = {1: {'hi': 0.6, 'lo': 0.4}}
    
    # 控制变量1 - 扭矩分配比例 - 使用更少的网格点
    grd.Nu = {1: 11}  # 减少到11个点
    grd.Un = {1: {'hi': 1, 'lo': -2.5}}
    
    # 控制变量2 - 工作模式
    grd.Nu[2] = 3
    grd.Un[2] = {'hi': 2, 'lo': 0}
    
    # 初始状态
    grd.X0 = {1: 0.50}
    
    # 终端状态约束
    grd.XN = {1: {'hi': 0.5, 'lo': 0.495}}
    
    print(f"网格设置:")
    print(f"  SOC网格点数: {grd.Nx[1]}")
    print(f"  扭矩分配网格点数: {grd.Nu[1]}")
    print(f"  工作模式网格点数: {grd.Nu[2]}")
    
    # 创建简化的问题 - 只有10个时间步
    N = 10
    speed_vector = np.linspace(0, 20, N+1)  # 简单的速度曲线
    acceleration_vector = np.diff(speed_vector)  # 计算加速度
    acceleration_vector = np.append(acceleration_vector, 0)  # 添加最后一个点
    gearnumber_vector = np.ones(N+1) * 2  # 固定档位
    
    W_data = {1: speed_vector, 2: acceleration_vector, 3: gearnumber_vector}
    prb = Problem(Ts=1, N=N, W=W_data)
    
    print(f"问题设置:")
    print(f"  时间步数: {prb.N}")
    print(f"  速度范围: {np.min(speed_vector):.1f} - {np.max(speed_vector):.1f} m/s")
    
    # 设置选项
    options = Options()
    options.MyInf = 1000
    options.BoundaryMethod = 'none'  # 不使用边界线
    options.Verbose = 'on'
    options.Waitbar = 'on'
    
    print(f"选项设置:")
    print(f"  边界方法: {options.BoundaryMethod}")
    print(f"  详细输出: {options.Verbose}")
    
    try:
        print("\n开始动态规划计算...")
        # 调用动态规划求解器
        result = dpm(phev_ht21_serip1p3, None, grd, prb, options)
        
        if isinstance(result, tuple) and len(result) == 2:
            res, dyn = result
            print("✓ 动态规划计算成功完成!")
            
            # 显示结果
            if hasattr(res, 'J') and res.J is not None:
                print(f"最优成本: {res.J:.6f}")
            
            if hasattr(res, 'X') and res.X is not None and 1 in res.X:
                print(f"初始SOC: {res.X[1][0]:.3f}")
                print(f"最终SOC: {res.X[1][-1]:.3f}")
                print(f"SOC变化: {res.X[1][-1] - res.X[1][0]:.3f}")
            
            return True
        else:
            print("✗ 返回结果格式不正确")
            return False
            
    except Exception as e:
        print(f"✗ 动态规划计算失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_phev()
    if success:
        print("\n✓ 简化PHEV测试成功!")
    else:
        print("\n✗ 简化PHEV测试失败!")
