#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 DPM 辅助函数的脚本

这个脚本快速验证辅助函数的基本功能
"""

import numpy as np
from dpm import get_size, input_check_grd, notify_user_of_error, clear_waitbars, Grid


def main():
    """主验证函数"""
    print("快速验证 DPM 辅助函数")
    print("=" * 40)
    
    try:
        # 测试 get_size
        print("1. 测试 get_size 函数:")
        current_grd = {
            'X': {
                1: [0, 1, 2, 3],  # 4个点
                2: [0, 1, 2]      # 3个点
            }
        }
        sze = get_size(current_grd)
        print(f"   网格大小: {sze}")
        print(f"   期望: [4, 3]")
        print(f"   ✓ 正确" if sze == [4, 3] else f"   ✗ 错误")
        
        # 测试 input_check_grd
        print("\n2. 测试 input_check_grd 函数:")
        grd = Grid()
        grd.Nx = {1: [5]}  # 只有一个值，需要扩展
        grd.Nu = {1: [3, 4, 5]}  # 太长，需要截断
        
        T = 2  # 2个时间步
        print(f"   修正前: Nx[1] = {grd.Nx[1]} (长度 {len(grd.Nx[1])})")
        print(f"   修正前: Nu[1] = {grd.Nu[1]} (长度 {len(grd.Nu[1])})")
        
        grd_checked = input_check_grd(grd, T)
        
        print(f"   修正后: Nx[1] = {grd_checked.Nx[1]} (长度 {len(grd_checked.Nx[1])})")
        print(f"   修正后: Nu[1] = {grd_checked.Nu[1]} (长度 {len(grd_checked.Nu[1])})")
        
        nx_correct = len(grd_checked.Nx[1]) == T + 1
        nu_correct = len(grd_checked.Nu[1]) == T
        print(f"   ✓ 状态网格长度正确" if nx_correct else f"   ✗ 状态网格长度错误")
        print(f"   ✓ 输入网格长度正确" if nu_correct else f"   ✗ 输入网格长度错误")
        
        # 测试 clear_waitbars
        print("\n3. 测试 clear_waitbars 函数:")
        print("   调用 clear_waitbars()...")
        clear_waitbars()
        print("   ✓ 清理完成")
        
        # 测试 notify_user_of_error
        print("\n4. 测试 notify_user_of_error 函数:")
        print("   创建测试错误...")
        test_error = ValueError("这是一个测试错误")
        print("   调用 notify_user_of_error:")
        notify_user_of_error(test_error)
        
        print("\n验证完成!")
        print("✓ 所有辅助函数基本功能正常")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
