#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的dpm_interpn三维插值函数
验证参数顺序与MATLAB的dpm_interpn保持一致

MATLAB参数顺序: dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
Python参数顺序: dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
"""

import numpy as np
from dpm import dpm_interpn


def test_simple_3d_case():
    """测试简单的三维插值案例"""
    print("测试简单的三维插值案例")
    print("=" * 50)
    
    # 创建简单的2x2x2网格
    xx1 = np.array([0, 1])  # 第一维网格（第三维索引）
    xx2 = np.array([0, 1])  # 第二维网格（第二维索引）
    xx3 = np.array([0, 1])  # 第三维网格（第一维索引）
    
    # 创建简单的值矩阵
    # YY[i,j,k] 表示在 (xx3[i], xx2[j], xx1[k]) 处的值
    YY = np.array([[[0, 1],    # xx3=0, xx2=0时：(0,0,0)=0, (0,0,1)=1
                    [2, 3]],   # xx3=0, xx2=1时：(0,1,0)=2, (0,1,1)=3
                   [[4, 5],    # xx3=1, xx2=0时：(1,0,0)=4, (1,0,1)=5
                    [6, 7]]])  # xx3=1, xx2=1时：(1,1,0)=6, (1,1,1)=7
    
    print(f"xx1 (第三维): {xx1}")
    print(f"xx2 (第二维): {xx2}")
    print(f"xx3 (第一维): {xx3}")
    print(f"YY 矩阵形状: {YY.shape}")
    print(f"YY[0,0,0] = {YY[0,0,0]} (xx3=0, xx2=0, xx1=0)")
    print(f"YY[0,0,1] = {YY[0,0,1]} (xx3=0, xx2=0, xx1=1)")
    print(f"YY[1,1,1] = {YY[1,1,1]} (xx3=1, xx2=1, xx1=1)")
    
    # 测试插值点
    A1 = np.array([0.5])  # 第一维查询点（第三维方向）
    A2 = np.array([0.5])  # 第二维查询点（第二维方向）
    A3 = np.array([0.5])  # 第三维查询点（第一维方向）
    
    print(f"\n查询点: A1={A1[0]} (第一维), A2={A2[0]} (第二维), A3={A3[0]} (第三维)")
    
    # 使用新的参数顺序调用：dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    
    print(f"插值结果: {result[0]}")
    
    # 手动计算三线性插值
    # 在点 (A3=0.5, A2=0.5, A1=0.5) 处
    # 8个角点的值：
    # (0,0,0) = 0, (0,0,1) = 1, (0,1,0) = 2, (0,1,1) = 3
    # (1,0,0) = 4, (1,0,1) = 5, (1,1,0) = 6, (1,1,1) = 7
    # 三线性插值：
    # v = (1-A3)*(1-A2)*(1-A1)*0 + (1-A3)*(1-A2)*A1*1 + (1-A3)*A2*(1-A1)*2 + (1-A3)*A2*A1*3 +
    #     A3*(1-A2)*(1-A1)*4 + A3*(1-A2)*A1*5 + A3*A2*(1-A1)*6 + A3*A2*A1*7
    # v = 0.5*0.5*0.5*(0+1+2+3+4+5+6+7) = 0.125*28 = 3.5
    expected = 0.125 * (0 + 1 + 2 + 3 + 4 + 5 + 6 + 7)
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ 简单三维测试通过")
        return True
    else:
        print("✗ 简单三维测试失败")
        return False


def test_boundary_points_3d():
    """测试三维边界点"""
    print("\n测试三维边界点")
    print("=" * 50)
    
    xx1 = np.array([0, 1])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    YY = np.array([[[0, 1],
                    [2, 3]],
                   [[4, 5],
                    [6, 7]]])
    
    # 测试8个角点
    test_points = [
        (np.array([0]), np.array([0]), np.array([0]), 0),  # (A3=0, A2=0, A1=0) -> YY[0,0,0] = 0
        (np.array([1]), np.array([0]), np.array([0]), 1),  # (A3=0, A2=0, A1=1) -> YY[0,0,1] = 1
        (np.array([0]), np.array([1]), np.array([0]), 2),  # (A3=0, A2=1, A1=0) -> YY[0,1,0] = 2
        (np.array([1]), np.array([1]), np.array([0]), 3),  # (A3=0, A2=1, A1=1) -> YY[0,1,1] = 3
        (np.array([0]), np.array([0]), np.array([1]), 4),  # (A3=1, A2=0, A1=0) -> YY[1,0,0] = 4
        (np.array([1]), np.array([0]), np.array([1]), 5),  # (A3=1, A2=0, A1=1) -> YY[1,0,1] = 5
        (np.array([0]), np.array([1]), np.array([1]), 6),  # (A3=1, A2=1, A1=0) -> YY[1,1,0] = 6
        (np.array([1]), np.array([1]), np.array([1]), 7),  # (A3=1, A2=1, A1=1) -> YY[1,1,1] = 7
    ]
    
    all_passed = True
    for i, (A1, A2, A3, expected) in enumerate(test_points):
        result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
        error = abs(result[0] - expected)
        print(f"点 {i+1}: A1={A1[0]}, A2={A2[0]}, A3={A3[0]} -> 结果={result[0]}, 期望={expected}, 误差={error}")
        if error > 1e-10:
            all_passed = False
    
    if all_passed:
        print("✓ 三维边界点测试通过")
        return True
    else:
        print("✗ 三维边界点测试失败")
        return False


def test_matlab_consistency_3d():
    """测试与MATLAB的一致性"""
    print("\n测试与MATLAB的一致性")
    print("=" * 50)
    
    # 使用与MATLAB测试相同的数据
    xx1 = np.array([0, 1, 2])
    xx2 = np.array([0, 1])
    xx3 = np.array([0, 1])
    
    # 创建测试矩阵
    YY = np.zeros((len(xx3), len(xx2), len(xx1)))
    for i in range(len(xx3)):
        for j in range(len(xx2)):
            for k in range(len(xx1)):
                YY[i, j, k] = xx3[i] + xx2[j] + xx1[k]  # 简单的线性函数
    
    print(f"xx1: {xx1}")
    print(f"xx2: {xx2}")
    print(f"xx3: {xx3}")
    print(f"YY 矩阵形状: {YY.shape}")
    print(f"YY 矩阵:\n{YY}")
    
    # 测试中点插值
    A1 = np.array([1.0])  # xx1方向的中点
    A2 = np.array([0.5])  # xx2方向的中点
    A3 = np.array([0.5])  # xx3方向的中点
    
    result = dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)
    
    # 期望值：A3 + A2 + A1 = 0.5 + 0.5 + 1.0 = 2.0
    expected = A3[0] + A2[0] + A1[0]
    
    print(f"查询点: A1={A1[0]}, A2={A2[0]}, A3={A3[0]}")
    print(f"插值结果: {result[0]}")
    print(f"期望结果: {expected}")
    print(f"误差: {abs(result[0] - expected)}")
    
    if abs(result[0] - expected) < 1e-10:
        print("✓ MATLAB一致性测试通过")
        return True
    else:
        print("✗ MATLAB一致性测试失败")
        return False


def main():
    """主测试函数"""
    print("测试修改后的dpm_interpn三维插值函数")
    print("验证参数顺序：dpm_interpn(xx3, xx2, xx1, YY, A3, A2, A1)")
    print()
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 简单案例
    if test_simple_3d_case():
        success_count += 1
    
    # 测试2: 边界点
    if test_boundary_points_3d():
        success_count += 1
    
    # 测试3: MATLAB一致性
    if test_matlab_consistency_3d():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！三维插值参数顺序修改成功。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
