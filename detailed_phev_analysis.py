#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析 phev_ht21_serip1p3.py 函数各部分的性能
"""

import numpy as np
import time
from scipy.interpolate import interp1d, RegularGridInterpolator
from dpm import Input


def create_test_input(grid_size=50):
    """创建测试输入数据"""
    inp = Input()
    
    # 创建网格化的输入数据
    inp.X = {1: np.random.uniform(0.4, 0.6, (grid_size, grid_size))}  # SOC
    inp.U = {
        1: np.random.uniform(-2.5, 1.0, (grid_size, grid_size)),  # 扭矩分配
        2: np.random.randint(0, 3, (grid_size, grid_size))        # 工作模式
    }
    inp.W = {
        1: np.random.uniform(0, 30, (grid_size, grid_size)),      # 速度 m/s
        2: np.random.uniform(-3, 3, (grid_size, grid_size))       # 加速度 m/s²
    }
    inp.Ts = 1.0
    
    return inp


def analyze_vehicle_calculations(inp):
    """分析车辆参数计算部分"""
    print("=== 车辆参数计算分析 ===")
    
    start_time = time.time()
    
    # 车轮转速 (rad/s)
    wv = inp.W[1] / 0.324
    # 车轮角加速度 (rad/s^2)
    dwv = inp.W[2] / 0.324
    # 车轮扭矩 (Nm)
    Tv = (100.95 + 0.7667 * (inp.W[1] * 3.6) +
          0.0271 * (inp.W[1] * 3.6)**2 + 1820 * inp.W[2]) * 0.324
    
    end_time = time.time()
    print(f"车辆参数计算耗时: {(end_time - start_time)*1000:.2f}毫秒")
    
    return wv, dwv, Tv


def analyze_transmission_calculations(inp, wv, dwv):
    """分析传动系统计算部分"""
    print("=== 传动系统计算分析 ===")
    
    start_time = time.time()
    
    # 传动系统参数
    ateff = 0.94
    p3eff = 0.96
    r_gear = np.array([2.75, 2.75, 2.75, 2.75])
    p1_gear = 1.0
    p3_gear = 10.03
    
    # 串联充电功率
    Pchrg = (1 - inp.U[1]) * 10000
    Pchrg = Pchrg * (Pchrg > 10000)
    
    # 发动机工作点数据
    oolspd = np.pi/30 * np.array([0, 800, 1000, 1200, 1400, 1600, 1800, 2000,
                                  2200, 2400, 2600, 2800, 3000, 3200, 3400, 3600, 3800])
    ooltrq = np.array([0, 90, 100, 100, 110, 100, 100, 100, 100, 110, 110, 110, 110, 100, 100, 100, 100])
    oolpwr = 1000 * np.array([0.00, 7.54, 10.47, 12.57, 16.13, 16.76, 18.85, 20.94,
                              23.04, 27.65, 29.95, 32.25, 34.56, 33.51, 35.61, 37.70, 40])
    
    # 插值计算
    ool_spd_interp = interp1d(oolpwr, oolspd, kind='linear',
                              bounds_error=False, fill_value='extrapolate')
    wg = ((inp.U[1] != 1) * (inp.U[2] == 0) *
          ool_spd_interp(Pchrg) +
          (inp.U[1] != 1) * ((inp.U[2] == 1) + (inp.U[2] == 2)) *
          r_gear[0] * wv)
    
    # P1/P3电机转速
    wm1 = (inp.U[1] != 1) * (p1_gear * wg)
    wm3 = p3_gear * wv
    
    end_time = time.time()
    print(f"传动系统计算耗时: {(end_time - start_time)*1000:.2f}毫秒")
    
    return wg, wm1, wm3


def analyze_engine_calculations(inp, wg):
    """分析发动机计算部分"""
    print("=== 发动机计算分析 ===")
    
    start_time = time.time()
    
    # 发动机效率矩阵 (简化版)
    we_list = np.pi/30 * np.concatenate([[0], np.arange(800, 4001, 200)])
    Te_list = np.arange(0, 141, 10)
    eta = 0.01 * np.random.uniform(10, 40, (len(Te_list), len(we_list)))  # 简化的效率矩阵
    
    # 创建插值器
    eta_interp = RegularGridInterpolator((Te_list, we_list), eta,
                                         bounds_error=False, fill_value=None)
    
    # 模拟扭矩值
    Te = np.random.uniform(0, 100, wg.shape)
    
    # 创建查询点
    query_points = np.column_stack([Te.flatten(), (wg * np.ones_like(Te)).flatten()])
    e_th = eta_interp(query_points).reshape(Te.shape)
    
    # 燃油质量流量计算
    m_dot_fuel = ((wg > 100) * ((Te >= 1) * Te * wg / e_th / 42750000 +
                                (Te < 1) * (Te > 0) * 0.14/1000))
    
    end_time = time.time()
    print(f"发动机计算耗时: {(end_time - start_time)*1000:.2f}毫秒")
    
    return m_dot_fuel, Te


def analyze_motor_calculations(wm1, wm3):
    """分析电机计算部分"""
    print("=== 电机计算分析 ===")
    
    start_time = time.time()
    
    # P1电机参数
    wm1_list = np.pi/30 * np.arange(0, 6001, 500)
    Tm1_list = np.concatenate([np.arange(-150, -19, 10), [-15, -10, -5, 5, 10, 15],
                               np.arange(20, 151, 10)])
    etam1 = 0.01 * np.random.uniform(50, 95, (len(Tm1_list), len(wm1_list)))  # 简化的效率矩阵
    
    # P3电机参数
    wm3_list = np.pi/30 * np.arange(0, 12001, 500)
    Tm3_list = np.arange(-180, 181, 10)
    etam3 = 0.01 * np.random.uniform(50, 95, (len(Tm3_list), len(wm3_list)))  # 简化的效率矩阵
    
    # 创建插值器
    etam1_interp = RegularGridInterpolator((Tm1_list, wm1_list), etam1,
                                           bounds_error=False, fill_value=None)
    etam3_interp = RegularGridInterpolator((Tm3_list, wm3_list), etam3,
                                           bounds_error=False, fill_value=None)
    
    # 模拟扭矩值
    Tm1 = np.random.uniform(-100, 100, wm1.shape)
    Tm3 = np.random.uniform(-150, 150, wm3.shape)
    
    # 计算电机效率
    query_points_m1 = np.column_stack([Tm1.flatten(), (wm1 * np.ones_like(Tm1)).flatten()])
    query_points_m3 = np.column_stack([Tm3.flatten(), (wm3 * np.ones_like(Tm3)).flatten()])
    
    e1_raw = etam1_interp(query_points_m1).reshape(Tm1.shape)
    e3_raw = etam3_interp(query_points_m3).reshape(Tm3.shape)
    
    # 处理零转速情况
    e1 = np.where(wm1 != 0, e1_raw, 1.0)
    e3 = np.where(wm3 != 0, e3_raw, 1.0)
    
    # 处理NaN值
    e1 = np.where(np.isnan(e1), 1, e1)
    e3 = np.where(np.isnan(e3), 1, e3)
    
    # 计算电功率消耗
    Pm = ((Tm1 < 0) * wm1 * Tm1 * e1 +
          (Tm1 >= 0) * wm1 * Tm1 / e1 +
          (Tm3 < 0) * wm3 * Tm3 * e3 +
          (Tm3 >= 0) * wm3 * Tm3 / e3 + 1350)
    
    end_time = time.time()
    print(f"电机计算耗时: {(end_time - start_time)*1000:.2f}毫秒")
    
    return Pm, Tm1, Tm3


def analyze_battery_calculations(inp, Pm):
    """分析电池计算部分"""
    print("=== 电池计算分析 ===")
    
    start_time = time.time()
    
    # 电池参数
    soc_list = np.arange(0, 1.1, 0.1)
    R_dis = np.array([0.25, 0.2443, 0.1835, 0.1522, 0.1428, 0.1405, 0.1406,
                      0.1426, 0.1428, 0.1421, 0.1410])
    R_chg = np.array([0.14, 0.1390, 0.1282, 0.1259, 0.1304, 0.1352, 0.1356,
                      0.1349, 0.1339, 0.1340, 0.1349])
    V_oc = np.array([326.1, 336.2, 343.1, 347.3, 349.7, 353.2, 360.5,
                     368.2, 377.5, 387.8, 401.3])
    
    # 电池效率
    e = np.where(Pm > 0, 1, 0.98)
    
    # 电池内阻插值
    r_dis_interp = interp1d(soc_list, R_dis, kind='linear',
                            bounds_error=False, fill_value='extrapolate')
    r_chg_interp = interp1d(soc_list, R_chg, kind='linear',
                            bounds_error=False, fill_value='extrapolate')
    r = np.where(Pm > 0,
                 r_dis_interp(inp.X[1]),
                 r_chg_interp(inp.X[1]))
    
    # 电池电压插值
    v_oc_interp = interp1d(soc_list, V_oc, kind='linear',
                           bounds_error=False, fill_value='extrapolate')
    v = v_oc_interp(inp.X[1])
    
    # 电池电流计算
    with np.errstate(invalid='ignore'):  # 忽略sqrt的警告
        Ib = e * (v - np.sqrt(v**2 - 4 * r * Pm)) / (2 * r)
    
    # 新的电池荷电状态
    X = {1: -Ib / (60 * 3600) + inp.X[1]}
    
    # 将新的荷电状态设置为实数值
    X[1] = np.real(X[1])
    Ib = np.real(Ib)
    
    end_time = time.time()
    print(f"电池计算耗时: {(end_time - start_time)*1000:.2f}毫秒")
    
    return X, Ib


def main():
    """主函数"""
    print("PHEV HT21 SerIP1P3 详细性能分析")
    print("=" * 50)
    
    # 创建测试输入
    grid_size = 50
    print(f"测试网格大小: {grid_size}x{grid_size} = {grid_size**2} 点")
    inp = create_test_input(grid_size)
    
    total_start_time = time.time()
    
    # 1. 车辆参数计算
    wv, dwv, Tv = analyze_vehicle_calculations(inp)
    
    # 2. 传动系统计算
    wg, wm1, wm3 = analyze_transmission_calculations(inp, wv, dwv)
    
    # 3. 发动机计算
    m_dot_fuel, Te = analyze_engine_calculations(inp, wg)
    
    # 4. 电机计算
    Pm, Tm1, Tm3 = analyze_motor_calculations(wm1, wm3)
    
    # 5. 电池计算
    X, Ib = analyze_battery_calculations(inp, Pm)
    
    total_end_time = time.time()
    
    print(f"\n总计算耗时: {(total_end_time - total_start_time)*1000:.2f}毫秒")
    print(f"每点平均耗时: {(total_end_time - total_start_time)*1000/(grid_size**2):.3f}毫秒")
    
    print("\n=== 性能优化建议 ===")
    print("1. 插值操作是主要瓶颈，考虑:")
    print("   - 预计算插值器并重用")
    print("   - 使用更快的插值方法")
    print("   - 减少插值调用次数")
    print("2. 数组操作优化:")
    print("   - 避免重复的条件判断")
    print("   - 使用向量化操作")
    print("   - 减少临时数组创建")
    print("3. 内存优化:")
    print("   - 重用数组空间")
    print("   - 避免不必要的数组复制")


if __name__ == "__main__":
    main()
