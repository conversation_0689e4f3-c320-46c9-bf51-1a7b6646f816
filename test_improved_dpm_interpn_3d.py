#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的 dpm_interpn 三维插值功能的脚本

这个脚本专门测试根据MATLAB版本改进后的三维插值实现
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from dpm import dpm_interpn


def test_improved_3d_basic():
    """测试改进后的基本三维插值功能"""
    print("=" * 60)
    print("测试改进后的三维插值基本功能")
    print("=" * 60)
    
    try:
        # 创建简单的三维测试数据
        xx1 = np.array([0, 1, 2])  # 3个点
        xx2 = np.array([0, 1])     # 2个点
        xx3 = np.array([0, 1, 2])  # 3个点
        
        # 创建三维值矩阵，使用简单的线性函数
        # f(x1,x2,x3) = x1 + 2*x2 + 3*x3
        YY = np.zeros((3, 2, 3))  # (xx3, xx2, xx1) 顺序
        for i3, x3 in enumerate(xx3):
            for i2, x2 in enumerate(xx2):
                for i1, x1 in enumerate(xx1):
                    YY[i3, i2, i1] = x1 + 2*x2 + 3*x3
        
        print(f"第一维网格 xx1: {xx1}")
        print(f"第二维网格 xx2: {xx2}")
        print(f"第三维网格 xx3: {xx3}")
        print(f"值矩阵 YY 形状: {YY.shape}")
        
        # 显示矩阵内容
        print("值矩阵内容:")
        for i3 in range(len(xx3)):
            print(f"  YY[{i3},:,:] (x3={xx3[i3]}):")
            print(f"    {YY[i3,:,:]}")
        
        # 测试插值点
        test_cases = [
            (0.5, 0.5, 0.5, "中心点"),
            (1.0, 0.0, 1.0, "网格点"),
            (1.5, 0.3, 1.7, "一般插值点"),
            (0.0, 0.0, 0.0, "原点"),
            (2.0, 1.0, 2.0, "边界点"),
        ]
        
        print("\n插值测试:")
        for a1, a2, a3, description in test_cases:
            A1 = np.array([a1])
            A2 = np.array([a2])
            A3 = np.array([a3])
            
            y = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
            expected = a1 + 2*a2 + 3*a3  # 线性函数的期望值
            error = abs(y[0] - expected)
            
            print(f"  ({a1:.1f}, {a2:.1f}, {a3:.1f}): y = {y[0]:.4f}, 期望 = {expected:.4f}, 误差 = {error:.4f} ({description})")
        
        print("✓ 改进后的三维插值基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的三维插值基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_improved_3d_corner_points():
    """测试改进后的角点精确性"""
    print("\n" + "=" * 60)
    print("测试改进后的三维插值角点精确性")
    print("=" * 60)
    
    try:
        # 创建2x2x2的最小测试网格
        xx1 = np.array([0, 1])
        xx2 = np.array([0, 1])
        xx3 = np.array([0, 1])
        
        # 创建2x2x2矩阵，每个角点有唯一值
        YY = np.array([[[1, 2], [3, 4]], [[5, 6], [7, 8]]])
        
        print(f"2x2x2网格:")
        print(f"  xx1: {xx1}")
        print(f"  xx2: {xx2}")
        print(f"  xx3: {xx3}")
        print(f"  YY形状: {YY.shape}")
        print(f"  YY[0,:,:] (x3=0):\n{YY[0,:,:]}")
        print(f"  YY[1,:,:] (x3=1):\n{YY[1,:,:]}")
        
        # 测试所有8个角点
        corner_points = [
            (0, 0, 0, YY[0, 0, 0]),  # 1
            (1, 0, 0, YY[0, 0, 1]),  # 2
            (0, 1, 0, YY[0, 1, 0]),  # 3
            (1, 1, 0, YY[0, 1, 1]),  # 4
            (0, 0, 1, YY[1, 0, 0]),  # 5
            (1, 0, 1, YY[1, 0, 1]),  # 6
            (0, 1, 1, YY[1, 1, 0]),  # 7
            (1, 1, 1, YY[1, 1, 1]),  # 8
        ]
        
        print("\n角点精确性测试:")
        max_error = 0
        for a1, a2, a3, expected in corner_points:
            A1, A2, A3 = np.array([a1]), np.array([a2]), np.array([a3])
            y = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
            error = abs(y[0] - expected)
            max_error = max(max_error, error)
            print(f"  ({a1}, {a2}, {a3}): y = {y[0]:.6f}, 期望 = {expected:.6f}, 误差 = {error:.8f}")
        
        print(f"\n最大角点误差: {max_error:.8f}")
        
        # 测试中心点
        print("\n中心点测试:")
        A1, A2, A3 = np.array([0.5]), np.array([0.5]), np.array([0.5])
        y_center = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
        expected_center = np.mean(YY)  # 理论上应该是所有值的平均
        
        print(f"  中心点 (0.5, 0.5, 0.5): y = {y_center[0]:.6f}")
        print(f"  期望 (平均值): {expected_center:.6f}")
        print(f"  误差: {abs(y_center[0] - expected_center):.8f}")
        
        print("✓ 改进后的三维插值角点精确性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的三维插值角点精确性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_improved_3d_boundary_handling():
    """测试改进后的边界处理"""
    print("\n" + "=" * 60)
    print("测试改进后的三维插值边界处理")
    print("=" * 60)
    
    try:
        # 创建较大的网格
        xx1 = np.array([0, 1, 2, 3])
        xx2 = np.array([0, 1, 2])
        xx3 = np.array([0, 1])
        
        # 创建随机值矩阵
        np.random.seed(42)
        YY = np.random.rand(2, 3, 4) * 10
        
        print(f"网格大小: {len(xx1)} x {len(xx2)} x {len(xx3)}")
        print(f"值矩阵形状: {YY.shape}")
        
        # 测试边界外的点
        boundary_tests = [
            (-0.5, 1.0, 0.5, "第一维下边界外"),
            (3.5, 1.0, 0.5, "第一维上边界外"),
            (1.5, -0.5, 0.5, "第二维下边界外"),
            (1.5, 2.5, 0.5, "第二维上边界外"),
            (1.5, 1.0, -0.5, "第三维下边界外"),
            (1.5, 1.0, 1.5, "第三维上边界外"),
            (-1.0, -1.0, -1.0, "所有维度下边界外"),
            (4.0, 3.0, 2.0, "所有维度上边界外"),
        ]
        
        print("\n边界外点测试:")
        for a1, a2, a3, description in boundary_tests:
            A1, A2, A3 = np.array([a1]), np.array([a2]), np.array([a3])
            y = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
            print(f"  ({a1:4.1f}, {a2:4.1f}, {a3:4.1f}): y = {y[0]:8.4f} ({description})")
        
        # 测试边界上的点
        print("\n边界上点测试:")
        boundary_exact = [
            (0.0, 1.0, 0.5, "第一维下边界"),
            (3.0, 1.0, 0.5, "第一维上边界"),
            (1.5, 0.0, 0.5, "第二维下边界"),
            (1.5, 2.0, 0.5, "第二维上边界"),
            (1.5, 1.0, 0.0, "第三维下边界"),
            (1.5, 1.0, 1.0, "第三维上边界"),
        ]
        
        for a1, a2, a3, description in boundary_exact:
            A1, A2, A3 = np.array([a1]), np.array([a2]), np.array([a3])
            y = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
            print(f"  ({a1:4.1f}, {a2:4.1f}, {a3:4.1f}): y = {y[0]:8.4f} ({description})")
        
        print("✓ 改进后的三维插值边界处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的三维插值边界处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_improved_3d_vectorized():
    """测试改进后的向量化处理"""
    print("\n" + "=" * 60)
    print("测试改进后的三维插值向量化处理")
    print("=" * 60)
    
    try:
        # 创建测试网格
        xx1 = np.linspace(0, 2, 3)
        xx2 = np.linspace(0, 2, 3)
        xx3 = np.linspace(0, 2, 3)
        
        # 创建线性函数值矩阵
        YY = np.zeros((3, 3, 3))
        for i3, x3 in enumerate(xx3):
            for i2, x2 in enumerate(xx2):
                for i1, x1 in enumerate(xx1):
                    YY[i3, i2, i1] = x1 + x2 + x3
        
        print(f"网格: xx1={xx1}, xx2={xx2}, xx3={xx3}")
        print(f"值矩阵形状: {YY.shape}")
        
        # 测试向量化输入
        A1 = np.array([0.5, 1.0, 1.5])
        A2 = np.array([0.3, 0.8, 1.2])
        A3 = np.array([0.7, 1.1, 1.6])
        
        print(f"\n向量化输入:")
        print(f"A1: {A1}")
        print(f"A2: {A2}")
        print(f"A3: {A3}")
        
        y_vector = dpm_interpn(xx1, xx2, xx3, YY, A1, A2, A3)
        expected_vector = A1 + A2 + A3
        
        print(f"\n向量化结果:")
        print(f"插值结果: {y_vector}")
        print(f"期望结果: {expected_vector}")
        print(f"误差: {np.abs(y_vector - expected_vector)}")
        
        # 测试2D数组输入
        print(f"\n2D数组输入测试:")
        A1_2d = np.array([[0.5, 1.0], [1.5, 0.8]])
        A2_2d = np.array([[0.3, 0.8], [1.2, 0.6]])
        A3_2d = np.array([[0.7, 1.1], [1.6, 0.9]])
        
        y_2d = dpm_interpn(xx1, xx2, xx3, YY, A1_2d, A2_2d, A3_2d)
        expected_2d = A1_2d + A2_2d + A3_2d
        
        print(f"2D输入形状: {A1_2d.shape}")
        print(f"2D输出形状: {y_2d.shape}")
        print(f"2D插值结果:\n{y_2d}")
        print(f"2D期望结果:\n{expected_2d}")
        print(f"2D误差:\n{np.abs(y_2d - expected_2d)}")
        
        print("✓ 改进后的三维插值向量化处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 改进后的三维插值向量化处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("改进后的三维插值函数专项测试程序")
    print("这个程序测试根据MATLAB版本改进后的 dpm_interpn 三维插值功能")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 基本功能
    if test_improved_3d_basic():
        success_count += 1
    
    # 测试2: 角点精确性
    if test_improved_3d_corner_points():
        success_count += 1
    
    # 测试3: 边界处理
    if test_improved_3d_boundary_handling():
        success_count += 1
    
    # 测试4: 向量化处理
    if test_improved_3d_vectorized():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有改进后的三维插值测试都通过了！函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查改进后的三维插值实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
