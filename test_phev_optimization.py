#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的 phev_ht21_serip1p3.py 函数性能
"""

import numpy as np
import time
import matplotlib.pyplot as plt
from dpm import Input
from phev_ht21_serip1p3 import phev_ht21_serip1p3


def create_test_input(grid_size=10):
    """创建测试输入数据"""
    inp = Input()
    
    # 创建网格化的输入数据
    inp.X = {1: np.random.uniform(0.4, 0.6, (grid_size, grid_size))}  # SOC
    inp.U = {
        1: np.random.uniform(-2.5, 1.0, (grid_size, grid_size)),  # 扭矩分配
        2: np.random.randint(0, 3, (grid_size, grid_size))        # 工作模式
    }
    inp.W = {
        1: np.random.uniform(0, 30, (grid_size, grid_size)),      # 速度 m/s
        2: np.random.uniform(-3, 3, (grid_size, grid_size))       # 加速度 m/s²
    }
    inp.Ts = 1.0
    
    return inp


def benchmark_function(func, inp, n_runs=10, warmup=2):
    """基准测试函数"""
    # 预热
    for _ in range(warmup):
        try:
            _ = func(inp, None)
        except Exception as e:
            print(f"预热时出错: {e}")
            return float('inf'), None
    
    # 计时测试
    times = []
    result = None
    
    for _ in range(n_runs):
        start_time = time.time()
        try:
            result = func(inp, None)
            end_time = time.time()
            times.append(end_time - start_time)
        except Exception as e:
            print(f"测试时出错: {e}")
            return float('inf'), None
    
    return np.mean(times), result


def test_performance():
    """性能测试"""
    print("PHEV 优化后函数性能测试")
    print("=" * 50)
    
    grid_sizes = [5, 10, 20, 30, 50]
    times = []
    throughputs = []
    
    for grid_size in grid_sizes:
        print(f"\n测试网格大小: {grid_size}x{grid_size} = {grid_size**2} 点")
        
        # 创建测试输入
        inp = create_test_input(grid_size)
        
        # 测试优化后的函数
        avg_time, result = benchmark_function(phev_ht21_serip1p3, inp)
        
        if avg_time != float('inf'):
            time_per_point = avg_time / (grid_size**2)
            throughput = (grid_size**2) / avg_time
            
            times.append(avg_time)
            throughputs.append(throughput)
            
            print(f"  平均耗时: {avg_time*1000:.2f} 毫秒")
            print(f"  每点耗时: {time_per_point*1000:.3f} 毫秒")
            print(f"  吞吐量: {throughput:.1f} 点/秒")
            
            # 验证结果正确性
            if result is not None:
                X, C, I, out = result
                print(f"  输出验证: X形状={X[1].shape}, C形状={C[1].shape}, I形状={I.shape}")
        else:
            print("  测试失败!")
            times.append(0)
            throughputs.append(0)
    
    return grid_sizes, times, throughputs


def analyze_scalability(grid_sizes, times):
    """分析可扩展性"""
    print("\n=== 可扩展性分析 ===")
    
    # 计算理论复杂度
    points = [size**2 for size in grid_sizes]
    
    # 线性拟合
    if len(times) > 1 and all(t > 0 for t in times):
        coeffs = np.polyfit(points, times, 1)
        print(f"线性拟合: 时间 = {coeffs[0]*1000:.3f} * 点数 + {coeffs[1]*1000:.3f} 毫秒")
        print(f"每点平均耗时: {coeffs[0]*1000:.3f} 毫秒")
        
        # 预测大规模性能
        large_grid = 100
        large_points = large_grid**2
        predicted_time = coeffs[0] * large_points + coeffs[1]
        print(f"预测 {large_grid}x{large_grid} 网格耗时: {predicted_time:.2f} 秒")


def plot_performance(grid_sizes, times, throughputs):
    """绘制性能图表"""
    if not any(t > 0 for t in times):
        print("没有有效的性能数据，跳过绘图")
        return
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 执行时间
    valid_indices = [i for i, t in enumerate(times) if t > 0]
    valid_sizes = [grid_sizes[i] for i in valid_indices]
    valid_times = [times[i] for i in valid_indices]
    valid_throughputs = [throughputs[i] for i in valid_indices]
    
    if valid_times:
        ax1.plot(valid_sizes, [t*1000 for t in valid_times], 'o-', 
                linewidth=2, markersize=8, color='blue')
        ax1.set_xlabel('网格大小')
        ax1.set_ylabel('执行时间 (毫秒)')
        ax1.set_title('优化后函数执行时间')
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 吞吐量
        ax2.plot(valid_sizes, valid_throughputs, 's-', 
                linewidth=2, markersize=8, color='green')
        ax2.set_xlabel('网格大小')
        ax2.set_ylabel('吞吐量 (点/秒)')
        ax2.set_title('优化后函数吞吐量')
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('phev_optimized_performance.png', dpi=300, bbox_inches='tight')
    print(f"\n性能图表已保存为: phev_optimized_performance.png")


def test_correctness():
    """测试结果正确性"""
    print("\n=== 结果正确性测试 ===")
    
    # 创建小规模测试
    inp = create_test_input(5)
    
    try:
        X, C, I, out = phev_ht21_serip1p3(inp, None)
        
        print("✅ 函数执行成功")
        print(f"状态输出 X[1] 范围: [{np.min(X[1]):.3f}, {np.max(X[1]):.3f}]")
        print(f"成本输出 C[1] 范围: [{np.min(C[1]):.6f}, {np.max(C[1]):.6f}]")
        print(f"不可行点比例: {np.sum(I)/I.size*100:.1f}%")
        
        # 检查输出信号
        expected_signals = ['Tv', 'Te', 'Tm1', 'Tm3', 'wg', 'wm1', 'wm3', 
                           'Ib', 'Pb', 'm_dot_fuel', 'u1', 'u2', 'Ttotclu', 'e_th']
        missing_signals = [sig for sig in expected_signals if sig not in out]
        
        if missing_signals:
            print(f"⚠️  缺少输出信号: {missing_signals}")
        else:
            print("✅ 所有预期输出信号都存在")
            
        # 检查数值合理性
        if np.any(np.isnan(X[1])) or np.any(np.isinf(X[1])):
            print("⚠️  状态输出包含NaN或Inf值")
        else:
            print("✅ 状态输出数值正常")
            
        if np.any(np.isnan(C[1])) or np.any(np.isinf(C[1])):
            print("⚠️  成本输出包含NaN或Inf值")
        else:
            print("✅ 成本输出数值正常")
            
    except Exception as e:
        print(f"❌ 函数执行失败: {e}")


def main():
    """主函数"""
    print("开始PHEV优化后函数测试...")
    
    # 1. 结果正确性测试
    test_correctness()
    
    # 2. 性能测试
    grid_sizes, times, throughputs = test_performance()
    
    # 3. 可扩展性分析
    analyze_scalability(grid_sizes, times)
    
    # 4. 绘制性能图表
    plot_performance(grid_sizes, times, throughputs)
    
    # 5. 总结
    print("\n=== 优化效果总结 ===")
    if times and any(t > 0 for t in times):
        valid_times = [t for t in times if t > 0]
        avg_time_per_point = np.mean([t/(grid_sizes[i]**2) for i, t in enumerate(times) if t > 0])
        
        print(f"平均每点耗时: {avg_time_per_point*1000:.3f} 毫秒")
        print(f"最大吞吐量: {max(throughputs):.1f} 点/秒")
        
        # 性能等级评估
        if avg_time_per_point < 0.001:  # < 1ms per point
            print("🚀 性能等级: 优秀")
        elif avg_time_per_point < 0.005:  # < 5ms per point
            print("✅ 性能等级: 良好")
        elif avg_time_per_point < 0.01:  # < 10ms per point
            print("⚠️  性能等级: 一般")
        else:
            print("❌ 性能等级: 需要进一步优化")
    
    print("\n优化建议:")
    print("1. 插值器缓存已实现，减少了重复创建开销")
    print("2. 条件判断已优化，使用预计算掩码")
    print("3. 数组操作已向量化，提高了计算效率")
    print("4. 如需进一步优化，可考虑使用Numba JIT编译")


if __name__ == "__main__":
    main()
