#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm.py 中的耗时统计功能
"""

import time
import numpy as np

def test_timing_output():
    """测试耗时统计输出格式"""
    print("=== 测试耗时统计功能 ===")
    
    # 模拟一个时间步的各个阶段
    step_start_time = time.time()
    n = 5
    
    print(f"\n=== 时间步 {n} 开始 ===")
    
    # 模拟各个阶段的耗时
    init_start_time = time.time()
    time.sleep(0.001)  # 模拟初始化耗时
    init_time = time.time() - init_start_time
    print(f"初始化耗时: {init_time:.4f}秒")
    
    grid_start_time = time.time()
    time.sleep(0.002)  # 模拟网格更新耗时
    grid_time = time.time() - grid_start_time
    print(f"网格更新耗时: {grid_time:.4f}秒")
    
    mesh_start_time = time.time()
    time.sleep(0.001)  # 模拟网格组合生成耗时
    mesh_time = time.time() - mesh_start_time
    print(f"网格组合生成耗时: {mesh_time:.4f}秒")
    
    model_start_time = time.time()
    time.sleep(0.005)  # 模拟模型函数调用耗时（通常是最耗时的部分）
    model_time = time.time() - model_start_time
    print(f"模型函数调用耗时: {model_time:.4f}秒")
    
    # 计算总耗时和占比
    step_total_time = time.time() - step_start_time
    print(f"--- 时间步 {n} 总耗时: {step_total_time:.4f}秒 ---")
    print(f"各部分耗时占比:")
    print(f"  初始化: {init_time/step_total_time*100:.1f}%")
    print(f"  网格更新: {grid_time/step_total_time*100:.1f}%")
    print(f"  网格组合生成: {mesh_time/step_total_time*100:.1f}%")
    print(f"  模型函数调用: {model_time/step_total_time*100:.1f}%")
    
    print("\n耗时统计功能测试完成！")

if __name__ == "__main__":
    test_timing_output()
