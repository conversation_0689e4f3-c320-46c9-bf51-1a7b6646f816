#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 PHEV HT21 Series P1P3 使用 Input 和 Output 类

验证混合动力车辆模型函数使用dpm.py中定义的Input和Output类的正确性
"""

import numpy as np
from phev_ht21_serip1p3 import phev_ht21_serip1p3
from dpm import Input, Output


def test_input_output_classes():
    """测试Input和Output类的使用"""
    print("=" * 60)
    print("测试Input和Output类的使用")
    print("=" * 60)
    
    try:
        # 创建Input对象
        inp = Input()
        
        # 设置输入数据
        inp.W = {0: 20.0, 1: 0.5}  # 车轮速度和加速度
        inp.U = {0: 0.5, 1: 1}     # 扭矩分配比例和工作模式
        inp.X = {0: 0.6}           # 电池SOC
        
        print(f"Input对象验证:")
        print(f"  对象类型: {type(inp)}")
        print(f"  是否为Input实例: {isinstance(inp, Input)}")
        print(f"  W (驾驶需求): {inp.W}")
        print(f"  U (控制输入): {inp.U}")
        print(f"  X (状态): {inp.X}")
        
        # 调用车辆模型
        output, out = phev_ht21_serip1p3(inp)
        
        print(f"\nOutput对象验证:")
        print(f"  对象类型: {type(output)}")
        print(f"  是否为Output实例: {isinstance(output, Output)}")
        print(f"  X (结果状态): {output.X}")
        print(f"  C (成本): {output.C}")
        print(f"  I (不可行标志): {output.I}")
        
        print(f"\n详细输出信号:")
        print(f"  车轮扭矩: {out['Tv']:.2f} Nm")
        print(f"  发动机扭矩: {out['Te']:.2f} Nm")
        print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
        print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
        print(f"  电池电流: {out['Ib']:.2f} A")
        print(f"  电池功率: {out['Pb']:.2f} W")
        
        # 验证输出合理性
        if isinstance(output, Output):
            print("✓ 返回正确的Output对象")
        else:
            print("✗ 返回的不是Output对象")
            return False
        
        if hasattr(output, 'X') and 0 in output.X:
            print("✓ Output包含状态X")
        else:
            print("✗ Output缺少状态X")
            return False
        
        if hasattr(output, 'C') and 0 in output.C:
            print("✓ Output包含成本C")
        else:
            print("✗ Output缺少成本C")
            return False
        
        if hasattr(output, 'I'):
            print("✓ Output包含不可行标志I")
        else:
            print("✗ Output缺少不可行标志I")
            return False
        
        print("✓ Input和Output类测试通过")
        return True
        
    except Exception as e:
        print(f"✗ Input和Output类测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_different_operating_modes():
    """测试不同工作模式下的Input/Output使用"""
    print("\n" + "=" * 60)
    print("测试不同工作模式下的Input/Output使用")
    print("=" * 60)
    
    try:
        modes = [
            (0, "串联充电模式"),
            (1, "并联模式"),
            (2, "混合模式")
        ]
        
        print("不同工作模式测试:")
        for mode, description in modes:
            # 创建Input对象
            inp = Input()
            inp.W = {0: 15.0, 1: 0.0}  # 恒定速度
            inp.U = {0: 0.5, 1: mode}  # 不同工作模式
            inp.X = {0: 0.5}           # 50% SOC
            
            # 调用函数
            output, out = phev_ht21_serip1p3(inp)
            
            print(f"\n{description} (模式 {mode}):")
            print(f"  输入: W={inp.W}, U={inp.U}, X={inp.X}")
            print(f"  SOC变化: {inp.X[0]:.3f} → {output.X[0]:.3f}")
            print(f"  燃油消耗: {output.C[0]:.6f} kg/s")
            print(f"  发动机扭矩: {out['Te']:.2f} Nm")
            print(f"  P1电机扭矩: {out['Tm1']:.2f} Nm")
            print(f"  P3电机扭矩: {out['Tm3']:.2f} Nm")
            print(f"  不可行: {output.I}")
        
        print("\n✓ 不同工作模式测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 不同工作模式测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_vectorized_inputs():
    """测试向量化输入"""
    print("\n" + "=" * 60)
    print("测试向量化输入")
    print("=" * 60)
    
    try:
        # 创建向量化Input对象
        inp = Input()
        
        # 设置向量化输入（多个时间点）
        inp.W = {
            0: np.array([10.0, 20.0, 30.0]),  # 不同速度
            1: np.array([0.0, 1.0, -0.5])     # 不同加速度
        }
        inp.U = {
            0: np.array([0.3, 0.5, 0.7]),     # 不同扭矩分配
            1: np.array([1, 1, 1])            # 相同工作模式
        }
        inp.X = {
            0: np.array([0.6, 0.5, 0.4])      # 不同SOC
        }
        
        print(f"向量化输入:")
        print(f"  W[0] (速度): {inp.W[0]}")
        print(f"  W[1] (加速度): {inp.W[1]}")
        print(f"  U[0] (扭矩分配): {inp.U[0]}")
        print(f"  U[1] (工作模式): {inp.U[1]}")
        print(f"  X[0] (SOC): {inp.X[0]}")
        
        # 调用函数
        output, out = phev_ht21_serip1p3(inp)
        
        print(f"\n向量化输出:")
        print(f"  X[0] (结果SOC): {output.X[0]}")
        print(f"  C[0] (燃油消耗): {output.C[0]}")
        print(f"  I (不可行标志): {output.I}")
        
        # 验证输出维度
        if hasattr(output.X[0], '__len__') and len(output.X[0]) == 3:
            print("✓ 输出SOC维度正确")
        else:
            print("✗ 输出SOC维度错误")
            return False
        
        if hasattr(output.C[0], '__len__') and len(output.C[0]) == 3:
            print("✓ 输出成本维度正确")
        else:
            print("✗ 输出成本维度错误")
            return False
        
        print("✓ 向量化输入测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 向量化输入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_conditions():
    """测试边界条件"""
    print("\n" + "=" * 60)
    print("测试边界条件")
    print("=" * 60)
    
    try:
        boundary_cases = [
            ({0: 0.0, 1: 0.0}, {0: 1.0, 1: 0}, {0: 0.2}, "怠速低SOC"),
            ({0: 50.0, 1: 0.0}, {0: 0.0, 1: 1}, {0: 0.9}, "高速高SOC"),
            ({0: 25.0, 1: 3.0}, {0: 0.5, 1: 2}, {0: 0.5}, "急加速中SOC"),
            ({0: 30.0, 1: -2.0}, {0: 0.8, 1: 1}, {0: 0.7}, "急减速高SOC"),
        ]
        
        print("边界条件测试:")
        for W_data, U_data, X_data, description in boundary_cases:
            # 创建Input对象
            inp = Input()
            inp.W = W_data
            inp.U = U_data
            inp.X = X_data
            
            try:
                # 调用函数
                output, out = phev_ht21_serip1p3(inp)
                
                print(f"\n{description}:")
                print(f"  输入: W={inp.W}, U={inp.U}, X={inp.X}")
                print(f"  SOC: {inp.X[0]:.3f} → {output.X[0]:.3f}")
                print(f"  燃油: {output.C[0]:.6f} kg/s")
                print(f"  不可行: {output.I}")
                print(f"  状态: {'正常' if not output.I else '不可行'}")
                
            except Exception as e:
                print(f"  {description}: 计算出错 - {str(e)}")
        
        print("\n✓ 边界条件测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 边界条件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("PHEV HT21 Series P1P3 Input/Output类测试程序")
    print("验证使用dpm.py中定义的Input和Output类的正确性")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: Input和Output类基本功能
    if test_input_output_classes():
        success_count += 1
    
    # 测试2: 不同工作模式
    if test_different_operating_modes():
        success_count += 1
    
    # 测试3: 向量化输入
    if test_vectorized_inputs():
        success_count += 1
    
    # 测试4: 边界条件
    if test_boundary_conditions():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有Input/Output类测试都通过了！")
        print("✓ Input类使用正确")
        print("✓ Output类使用正确")
        print("✓ 不同工作模式正常")
        print("✓ 向量化处理正常")
        print("✓ 边界条件处理正常")
        print("\nPHEV函数已准备好与DPM系统集成！")
    else:
        print("⚠️  部分测试失败。请检查Input/Output类的使用。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
