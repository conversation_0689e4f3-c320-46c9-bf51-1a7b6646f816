#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试create_grid函数与MATLAB版本phev_main_ht21.m的一致性

验证Python版本的create_grid函数是否与MATLAB版本完全一致
"""

from phev_main_ht21 import create_grid
from dpm import Grid

def test_grid_structure():
    """测试网格结构的一致性"""
    print("=" * 60)
    print("测试1: 网格结构与MATLAB版本的一致性")
    print("=" * 60)
    
    try:
        grd = create_grid()
        
        # 验证Grid对象类型
        if not isinstance(grd, Grid):
            print(f"✗ 返回对象类型错误: {type(grd)}")
            return False
        
        print("✓ 返回Grid对象类型正确")
        
        # 验证状态变量结构
        print("\n状态变量验证:")
        
        # 检查Nx{1}
        if 1 in grd.Nx and grd.Nx[1] == [401]:
            print("✓ grd.Nx{1} = 401 (与MATLAB一致)")
        else:
            print(f"✗ grd.Nx[1]错误: {grd.Nx.get(1, 'missing')}")
            return False
        
        # 检查Xn{1}
        if (1 in grd.Xn and 
            grd.Xn[1]['hi'] == [0.6] and 
            grd.Xn[1]['lo'] == [0.4]):
            print("✓ grd.Xn{1}.hi = 0.6, grd.Xn{1}.lo = 0.4 (与MATLAB一致)")
        else:
            print(f"✗ grd.Xn[1]错误: {grd.Xn.get(1, 'missing')}")
            return False
        
        # 验证控制变量结构
        print("\n控制变量验证:")
        
        # 检查Nu{1}
        if 1 in grd.Nu and grd.Nu[1] == [351]:
            print("✓ grd.Nu{1} = 351 (与MATLAB一致)")
        else:
            print(f"✗ grd.Nu[1]错误: {grd.Nu.get(1, 'missing')}")
            return False
        
        # 检查Un{1}
        if (1 in grd.Un and 
            grd.Un[1]['hi'] == [1] and 
            grd.Un[1]['lo'] == [-2.5]):
            print("✓ grd.Un{1}.hi = 1, grd.Un{1}.lo = -2.5 (与MATLAB一致)")
        else:
            print(f"✗ grd.Un[1]错误: {grd.Un.get(1, 'missing')}")
            return False
        
        # 检查Nu{2}
        if 2 in grd.Nu and grd.Nu[2] == [3]:
            print("✓ grd.Nu{2} = 3 (与MATLAB一致)")
        else:
            print(f"✗ grd.Nu[2]错误: {grd.Nu.get(2, 'missing')}")
            return False
        
        # 检查Un{2}
        if (2 in grd.Un and 
            grd.Un[2]['hi'] == [2] and 
            grd.Un[2]['lo'] == [0]):
            print("✓ grd.Un{2}.hi = 2, grd.Un{2}.lo = 0 (与MATLAB一致)")
        else:
            print(f"✗ grd.Un[2]错误: {grd.Un.get(2, 'missing')}")
            return False
        
        # 验证初始状态
        print("\n初始状态验证:")
        if 1 in grd.X0 and grd.X0[1] == 0.50:
            print("✓ grd.X0{1} = 0.50 (与MATLAB一致)")
        else:
            print(f"✗ grd.X0[1]错误: {grd.X0.get(1, 'missing')}")
            return False
        
        # 验证最终状态约束
        print("\n最终状态约束验证:")
        expected_hi = 0.5 - 0*0.01  # 0.5
        expected_lo = 0.495 - 0*0.01  # 0.495
        
        if (1 in grd.XN and 
            abs(grd.XN[1]['hi'] - expected_hi) < 1e-10 and 
            abs(grd.XN[1]['lo'] - expected_lo) < 1e-10):
            print("✓ grd.XN{1}.hi = 0.5, grd.XN{1}.lo = 0.495 (与MATLAB一致)")
        else:
            print(f"✗ grd.XN[1]错误: {grd.XN.get(1, 'missing')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_matlab_values():
    """测试具体数值与MATLAB版本的一致性"""
    print("\n测试2: 具体数值验证")
    print("=" * 60)
    
    try:
        grd = create_grid()
        
        # MATLAB版本的具体值
        matlab_values = {
            'Nx1': 401,
            'Xn1_hi': 0.6,
            'Xn1_lo': 0.4,
            'Nu1': 351,
            'Un1_hi': 1,
            'Un1_lo': -2.5,
            'Nu2': 3,
            'Un2_hi': 2,
            'Un2_lo': 0,
            'X0_1': 0.50,
            'XN1_hi': 0.5,
            'XN1_lo': 0.495
        }
        
        # 验证每个值
        checks = [
            ('Nx1', grd.Nx[1][0], matlab_values['Nx1']),
            ('Xn1_hi', grd.Xn[1]['hi'][0], matlab_values['Xn1_hi']),
            ('Xn1_lo', grd.Xn[1]['lo'][0], matlab_values['Xn1_lo']),
            ('Nu1', grd.Nu[1][0], matlab_values['Nu1']),
            ('Un1_hi', grd.Un[1]['hi'][0], matlab_values['Un1_hi']),
            ('Un1_lo', grd.Un[1]['lo'][0], matlab_values['Un1_lo']),
            ('Nu2', grd.Nu[2][0], matlab_values['Nu2']),
            ('Un2_hi', grd.Un[2]['hi'][0], matlab_values['Un2_hi']),
            ('Un2_lo', grd.Un[2]['lo'][0], matlab_values['Un2_lo']),
            ('X0_1', grd.X0[1], matlab_values['X0_1']),
            ('XN1_hi', grd.XN[1]['hi'], matlab_values['XN1_hi']),
            ('XN1_lo', grd.XN[1]['lo'], matlab_values['XN1_lo'])
        ]
        
        all_passed = True
        for name, python_val, matlab_val in checks:
            if abs(python_val - matlab_val) < 1e-10:
                print(f"✓ {name}: {python_val} (与MATLAB一致)")
            else:
                print(f"✗ {name}: {python_val} != {matlab_val} (MATLAB)")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"✗ 数值验证失败: {str(e)}")
        return False

def test_grid_compatibility():
    """测试网格与DPM库的兼容性"""
    print("\n测试3: 与DPM库的兼容性")
    print("=" * 60)
    
    try:
        grd = create_grid()
        
        # 检查是否符合DPM库的期望格式
        print("检查数据结构兼容性:")
        
        # 检查索引从1开始
        if 1 in grd.Nx and 1 in grd.Xn and 1 in grd.Nu and 1 in grd.Un:
            print("✓ 使用从1开始的索引 (符合DPM库约定)")
        else:
            print("✗ 索引不符合DPM库约定")
            return False
        
        # 检查数据类型
        if (isinstance(grd.Nx[1], list) and 
            isinstance(grd.Xn[1], dict) and
            isinstance(grd.Nu[1], list) and
            isinstance(grd.Un[1], dict)):
            print("✓ 数据类型正确")
        else:
            print("✗ 数据类型错误")
            return False
        
        # 检查字典键
        if ('hi' in grd.Xn[1] and 'lo' in grd.Xn[1] and
            'hi' in grd.Un[1] and 'lo' in grd.Un[1]):
            print("✓ 字典键结构正确")
        else:
            print("✗ 字典键结构错误")
            return False
        
        # 检查分离的输入变量
        if 2 in grd.Nu and 2 in grd.Un:
            print("✓ 输入变量正确分离为独立索引")
        else:
            print("✗ 输入变量未正确分离")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 兼容性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("测试create_grid函数与MATLAB版本的一致性")
    print("对比MATLAB phev_main_ht21.m 第4-24行的网格创建逻辑")
    print()
    
    tests = [
        test_grid_structure,
        test_matlab_values,
        test_grid_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        print("✓ Python版本与MATLAB版本完全一致")
        print("✓ 网格结构正确")
        print("✓ 数值精确匹配")
        print("✓ 与DPM库兼容")
        print("✓ 使用正确的索引约定 (从1开始)")
        print("✓ 输入变量正确分离")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
