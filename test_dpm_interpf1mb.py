#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dpm_interpf1mb 函数的脚本

这个脚本测试 dpm_interpf1mb 函数的各种功能
"""

import numpy as np
import matplotlib.pyplot as plt
from dpm import dpm_interpf1mb


def test_basic_interpolation():
    """测试基本插值功能"""
    print("=" * 60)
    print("测试 dpm_interpf1mb 基本插值功能")
    print("=" * 60)
    
    try:
        # 创建测试数据
        xx = np.linspace(0, 10, 11)  # 0, 1, 2, ..., 10
        yy = xx**2  # 二次函数
        
        # 设置边界
        xlim = np.array([2.5, 7.5])
        ylim = np.array([2.5**2, 7.5**2])  # 对应的y值
        
        # 测试插值点
        A = np.array([1.0, 3.0, 5.0, 8.0, 11.0])  # 包含边界内外的点
        
        myInf = 1e6
        
        print(f"输入网格 xx: {xx}")
        print(f"输入值 yy: {yy}")
        print(f"边界 xlim: {xlim}")
        print(f"边界值 ylim: {ylim}")
        print(f"插值点 A: {A}")
        
        # 调用插值函数
        y = dpm_interpf1mb(xx, yy, A, xlim, ylim, myInf)
        
        print(f"插值结果 y: {y}")
        
        # 验证结果
        expected_results = []
        for a in A:
            if a < xlim[0] or a > xlim[1]:
                expected_results.append("应该是 myInf")
            else:
                expected_results.append(f"应该接近 {a**2}")
        
        for i, (a, y_val, expected) in enumerate(zip(A, y, expected_results)):
            print(f"  A[{i}] = {a:.1f}, y[{i}] = {y_val:.2f}, {expected}")
        
        print("✓ dpm_interpf1mb 基本插值测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1mb 基本插值测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf1mb 边界情况")
    print("=" * 60)
    
    try:
        # 测试1: 边界内没有网格点的情况
        print("测试1: 边界内没有网格点")
        xx = np.array([0, 1, 5, 6])
        yy = np.array([0, 1, 25, 36])
        xlim = np.array([2, 4])
        ylim = np.array([4, 16])  # 在边界处的期望值
        A = np.array([2.5, 3.0, 3.5])
        myInf = 1e6
        
        y1 = dpm_interpf1mb(xx, yy, A, xlim, ylim, myInf)
        print(f"  结果: {y1}")
        
        # 测试2: 单点插值
        print("测试2: 单点插值")
        A_single = np.array([3.0])
        y2 = dpm_interpf1mb(xx, yy, A_single, xlim, ylim, myInf)
        print(f"  单点结果: {y2}")
        
        # 测试3: 边界外的点
        print("测试3: 边界外的点")
        A_outside = np.array([1.0, 5.0])
        y3 = dpm_interpf1mb(xx, yy, A_outside, xlim, ylim, myInf)
        print(f"  边界外结果: {y3}")
        print(f"  应该都是 myInf ({myInf})")
        
        # 测试4: 多维数组输入
        print("测试4: 多维数组输入")
        A_2d = np.array([[2.5, 3.0], [3.5, 2.8]])
        y4 = dpm_interpf1mb(xx, yy, A_2d, xlim, ylim, myInf)
        print(f"  2D输入形状: {A_2d.shape}")
        print(f"  2D输出形状: {y4.shape}")
        print(f"  2D结果: {y4}")
        
        print("✓ dpm_interpf1mb 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1mb 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_with_visualization():
    """测试并可视化结果"""
    print("\n" + "=" * 60)
    print("测试 dpm_interpf1mb 并可视化")
    print("=" * 60)
    
    try:
        # 创建更详细的测试数据
        xx = np.linspace(0, 10, 21)  # 更密集的网格
        yy = np.sin(xx) + 0.1 * xx  # 正弦函数加线性趋势
        
        # 设置边界
        xlim = np.array([3.0, 8.0])
        ylim = np.array([np.sin(3.0) + 0.1*3.0, np.sin(8.0) + 0.1*8.0])
        
        # 创建密集的插值点
        A = np.linspace(-1, 12, 100)
        
        myInf = 10.0  # 使用较小的无穷大值以便可视化
        
        # 调用插值函数
        y = dpm_interpf1mb(xx, yy, A, xlim, ylim, myInf)
        
        # 创建可视化
        try:
            plt.figure(figsize=(12, 8))
            
            # 绘制原始数据点
            plt.plot(xx, yy, 'bo-', label='原始数据点', markersize=4)
            
            # 绘制插值结果
            valid_mask = y < myInf
            plt.plot(A[valid_mask], y[valid_mask], 'r-', label='插值结果', linewidth=2)
            
            # 绘制边界
            plt.axvline(x=xlim[0], color='g', linestyle='--', label=f'下边界 x={xlim[0]}')
            plt.axvline(x=xlim[1], color='g', linestyle='--', label=f'上边界 x={xlim[1]}')
            
            # 绘制边界值
            plt.plot(xlim[0], ylim[0], 'gs', markersize=8, label='边界值')
            plt.plot(xlim[1], ylim[1], 'gs', markersize=8)
            
            # 标记无穷大区域
            inf_mask = y >= myInf
            if np.any(inf_mask):
                plt.scatter(A[inf_mask], np.full(np.sum(inf_mask), myInf), 
                           c='red', marker='x', s=20, label='无穷大区域')
            
            plt.xlabel('x')
            plt.ylabel('y')
            plt.title('dpm_interpf1mb 插值结果可视化')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.ylim(-2, 12)
            
            plt.tight_layout()
            plt.savefig('dpm_interpf1mb_test.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            print("✓ 可视化图表已保存为 'dpm_interpf1mb_test.png'")
            
        except Exception as plot_error:
            print(f"可视化失败（但插值成功）: {plot_error}")
        
        # 验证一些关键点
        test_points = [2.0, 3.0, 5.0, 8.0, 9.0]  # 边界外、边界上、内部、边界上、边界外
        test_results = dpm_interpf1mb(xx, yy, np.array(test_points), xlim, ylim, myInf)
        
        print("关键点验证:")
        for point, result in zip(test_points, test_results):
            status = "边界外" if (point < xlim[0] or point > xlim[1]) else "边界内"
            print(f"  x = {point:.1f}: y = {result:.4f} ({status})")
        
        print("✓ dpm_interpf1mb 可视化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ dpm_interpf1mb 可视化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("dpm_interpf1mb 函数测试程序")
    print("这个程序测试一维边界插值函数的各种功能")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 基本插值
    if test_basic_interpolation():
        success_count += 1
    
    # 测试2: 边界情况
    if test_boundary_cases():
        success_count += 1
    
    # 测试3: 可视化测试
    if test_with_visualization():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！dpm_interpf1mb 函数工作正常。")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
