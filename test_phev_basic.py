#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本测试脚本 - 测试PHEV模型的基本功能
"""

import numpy as np
import pandas as pd
from dpm import dpm, Grid, Problem, Options
from phev_ht21_serip1p3 import phev_ht21_serip1p3

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("PHEV 基本功能测试")
    print("=" * 60)
    
    try:
        # 测试1: 加载驾驶循环数据
        print("\n测试1: 加载驾驶循环数据")
        print("-" * 30)
        
        # 读取速度数据
        speed_df = pd.read_csv('speed_vector.csv', header=None)
        speed_vector = speed_df.iloc[0].values[:10]  # 只取前10个点进行测试
        
        # 读取加速度数据
        accel_df = pd.read_csv('acceleration_vector.csv', header=None)
        acceleration_vector = accel_df.iloc[0].values[:10]
        
        # 读取档位数据
        gear_df = pd.read_csv('gearnumber_vector.csv', header=None)
        gearnumber_vector = gear_df.iloc[0].values[:10]
        
        print(f"数据加载成功，测试数据点数: {len(speed_vector)}")
        
        # 测试2: 创建简化网格
        print("\n测试2: 创建简化网格")
        print("-" * 30)
        
        grd = Grid()
        
        # 状态变量1 - 电池荷电状态 (SOC) - 简化版本
        grd.Nx = {1: [11]}  # 减少网格点数
        grd.Xn = {1: {'hi': [0.6], 'lo': [0.4]}}
        
        # 控制变量1 - 扭矩分配比例 - 简化版本
        grd.Nu = {1: [11]}  # 减少网格点数
        grd.Un = {1: {'hi': [1], 'lo': [-2.5]}}
        
        # 控制变量2 - 工作模式
        grd.Nu[2] = [3]
        grd.Un[2] = {'hi': [2], 'lo': [0]}
        
        # 初始状态
        grd.X0 = {1: 0.50}
        
        # 终端状态约束
        grd.XN = {1: {'hi': 0.5, 'lo': 0.495}}
        
        print("简化网格创建成功")
        
        # 测试3: 定义简化问题
        print("\n测试3: 定义简化问题")
        print("-" * 30)
        
        W_data = {0: speed_vector, 1: acceleration_vector, 2: gearnumber_vector}
        Ts = 1
        N = len(speed_vector)
        
        prb = Problem(Ts=Ts, N=N, W=W_data)
        print(f"问题定义成功，时间步数: {prb.N}")
        
        # 测试4: 设置选项（不使用边界线）
        print("\n测试4: 设置选项")
        print("-" * 30)
        
        options = dpm()
        options.MyInf = 1000
        options.BoundaryMethod = 'none'  # 不使用边界线方法
        options.Verbose = 'on'
        
        print("选项设置成功")
        
        # 测试5: 测试模型函数
        print("\n测试5: 测试模型函数")
        print("-" * 30)
        
        # 创建测试输入
        from dpm import Input
        test_inp = Input()
        test_inp.X = {1: np.array([0.5])}  # SOC = 50%
        test_inp.U = {1: np.array([0.0]), 2: np.array([1])}  # 扭矩分配=0, 模式=P1
        test_inp.W = {0: speed_vector[0], 1: acceleration_vector[0], 2: gearnumber_vector[0]}
        test_inp.Ts = 1.0
        
        # 调用模型函数
        result = phev_ht21_serip1p3(test_inp, None)
        print(f"模型函数调用成功")
        print(f"输出状态维度: {len(result.X) if hasattr(result, 'X') else 'N/A'}")
        print(f"输出成本维度: {len(result.C) if hasattr(result, 'C') else 'N/A'}")
        
        # 测试6: 尝试简单的动态规划（不使用边界线）
        print("\n测试6: 简单动态规划测试")
        print("-" * 30)
        
        try:
            result = dpm(phev_ht21_serip1p3, None, grd, prb, options)
            print("动态规划计算成功!")
            return True
        except Exception as e:
            print(f"动态规划计算失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n✓ 基本功能测试通过!")
    else:
        print("\n✗ 基本功能测试失败!")
