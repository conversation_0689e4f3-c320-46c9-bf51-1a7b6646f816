#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试 dpm_forward 函数的脚本

这个脚本全面测试 dpm_forward 函数的各种功能：
- 基本前向仿真
- 不同维度的插值处理
- 边界线方法
- 离散和连续输入
- 错误处理
- 进度显示
"""

import numpy as np
import warnings
from dpm import (dpm_forward, DynamicResult, Grid, Problem, Options, Input, Output,
                 dpm_get_empty_inp, dpm_get_empty_out)


def simple_1d_model(inp, par=None):
    """
    简单的一维测试模型

    系统方程: x[k+1] = x[k] + u[k]*Ts
    代价函数: J = u[k]^2 * Ts
    """
    # 状态更新
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}

    # 代价函数
    C = {1: inp.U[1]**2 * inp.Ts}

    # 不可行性检查
    I = np.zeros_like(inp.X[1])

    return X, C, I


def simple_2d_model(inp, par=None):
    """
    简单的二维测试模型

    系统方程:
    x1[k+1] = x1[k] + u1[k]*Ts
    x2[k+1] = x2[k] + u2[k]*Ts
    代价函数: J = (u1[k]^2 + u2[k]^2) * Ts
    """
    # 状态更新
    X = {
        1: inp.X[1] + inp.U[1] * inp.Ts,
        2: inp.X[2] + inp.U[2] * inp.Ts
    }

    # 代价函数
    C = {1: (inp.U[1]**2 + inp.U[2]**2) * inp.Ts}

    # 不可行性检查
    I = np.zeros_like(inp.X[1])

    return X, C, I


def simple_3d_model(inp, par=None):
    """
    简单的三维测试模型

    系统方程:
    x1[k+1] = x1[k] + u1[k]*Ts
    x2[k+1] = x2[k] + u2[k]*Ts
    x3[k+1] = x3[k] + u3[k]*Ts
    代价函数: J = (u1[k]^2 + u2[k]^2 + u3[k]^2) * Ts
    """
    # 状态更新
    X = {
        1: inp.X[1] + inp.U[1] * inp.Ts,
        2: inp.X[2] + inp.U[2] * inp.Ts,
        3: inp.X[3] + inp.U[3] * inp.Ts
    }

    # 代价函数
    C = {1: (inp.U[1]**2 + inp.U[2]**2 + inp.U[3]**2) * inp.Ts}

    # 不可行性检查
    I = np.zeros_like(inp.X[1])

    return X, C, I


def model_with_constraints(inp, par=None):
    """
    带约束的测试模型
    """
    # 状态更新
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}

    # 代价函数
    C = {1: inp.U[1]**2 * inp.Ts}

    # 不可行性检查 - 如果状态超出范围则不可行
    I = np.logical_or(X[1] > 1.0, X[1] < 0.0)

    return X, C, I


def create_1d_dyn_result(N=5, Nx=5):
    """创建一维动态规划结果"""
    dyn = DynamicResult()

    # 创建最优输入映射
    dyn.Uo = {1: {}}

    # 为每个时间步创建最优输入
    for n in range(1, N + 1):
        dyn.Uo[1][n] = np.linspace(-0.2, 0.2, Nx)  # 网格点的最优输入

    # 创建代价函数
    dyn.Jo = {1: np.linspace(0.0, 1.0, Nx)}  # 网格点的代价

    return dyn


def create_2d_dyn_result(N=3, Nx1=3, Nx2=3):
    """创建二维动态规划结果"""
    dyn = DynamicResult()

    # 创建最优输入映射
    dyn.Uo = {1: {}, 2: {}}

    # 为每个时间步创建最优输入
    for n in range(1, N + 1):
        dyn.Uo[1][n] = np.random.uniform(-0.1, 0.1, (Nx2, Nx1))  # 二维网格的最优输入1
        dyn.Uo[2][n] = np.random.uniform(-0.1, 0.1, (Nx2, Nx1))  # 二维网格的最优输入2

    # 创建代价函数
    dyn.Jo = {1: np.random.uniform(0.0, 0.5, (Nx2, Nx1))}  # 二维网格的代价

    return dyn


def create_3d_dyn_result(N=2, Nx1=2, Nx2=2, Nx3=2):
    """创建三维动态规划结果"""
    dyn = DynamicResult()

    # 创建最优输入映射
    dyn.Uo = {1: {}, 2: {}, 3: {}}

    # 为每个时间步创建最优输入
    for n in range(1, N + 1):
        dyn.Uo[1][n] = np.random.uniform(-0.1, 0.1, (Nx3, Nx2, Nx1))  # 三维网格的最优输入1
        dyn.Uo[2][n] = np.random.uniform(-0.1, 0.1, (Nx3, Nx2, Nx1))  # 三维网格的最优输入2
        dyn.Uo[3][n] = np.random.uniform(-0.1, 0.1, (Nx3, Nx2, Nx1))  # 三维网格的最优输入3

    # 创建代价函数
    dyn.Jo = {1: np.random.uniform(0.0, 0.3, (Nx3, Nx2, Nx1))}  # 三维网格的代价

    return dyn


def create_boundary_line_dyn_result():
    """创建带边界线的动态规划结果"""
    dyn = DynamicResult()

    # 创建边界线结构
    class Boundary:
        def __init__(self, trajectory):
            self.Xo = trajectory
            self.Jo = np.zeros_like(trajectory)

    dyn.B = {
        'lo': Boundary(np.linspace(0.2, 0.25, 5)),  # 下边界轨迹
        'hi': Boundary(np.linspace(0.75, 0.8, 5))   # 上边界轨迹
    }

    # 创建简单的最优输入映射
    dyn.Uo = {1: {}}
    for n in range(1, 6):
        dyn.Uo[1][n] = np.array([0.0, 0.1, 0.2, 0.1, 0.0])

    dyn.Jo = {1: np.array([0.0, 0.1, 0.2, 0.1, 0.0])}

    return dyn


def test_1d_forward():
    """测试一维前向仿真"""
    print("=" * 60)
    print("测试一维前向仿真")
    print("=" * 60)

    try:
        # 创建一维网格结构
        grd = Grid()
        grd.Nx = {1: [5] * 6}  # 5个网格点，5个时间步+1
        grd.Xn = {1: {'lo': [0.0] * 6, 'hi': [1.0] * 6}}
        grd.X0 = {1: 0.5}  # 初始状态
        grd.Nu = {1: [5] * 5}  # 5个网格点，5个时间步
        grd.Un = {1: {'lo': [-0.3] * 5, 'hi': [0.3] * 5}}

        # 问题参数
        prb = Problem(Ts=0.1, N=5)
        prb.N0 = 1
        prb.W = {}

        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'on'
        options.Warnings = 'off'
        options.UseUmap = True
        options.InputType = ['c']  # 连续输入

        # 创建一维动态规划结果
        dyn = create_1d_dyn_result(N=5, Nx=5)

        print("开始一维前向仿真...")

        # 调用前向仿真函数
        out = dpm_forward(dyn, simple_1d_model, None, grd, prb, options)

        print("一维前向仿真完成!")

        # 检查结果
        print(f"输出结构类型: {type(out)}")

        if hasattr(out, 'X') and 1 in out.X:
            print(f"状态轨迹长度: {len(out.X[1]) if hasattr(out.X[1], '__len__') else 1}")
            print(f"状态轨迹前3个值: {out.X[1][:3] if hasattr(out.X[1], '__len__') else out.X[1]}")

        if hasattr(out, 'U') and 1 in out.U:
            print(f"输入轨迹长度: {len(out.U[1]) if hasattr(out.U[1], '__len__') else 1}")
            print(f"输入轨迹前3个值: {out.U[1][:3] if hasattr(out.U[1], '__len__') else out.U[1]}")

        if hasattr(out, 'C') and 1 in out.C:
            print(f"代价轨迹前3个值: {out.C[1][:3] if hasattr(out.C[1], '__len__') else out.C[1]}")

        print("✓ 一维前向仿真测试通过")
        return True

    except Exception as e:
        print(f"✗ 一维前向仿真测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_2d_forward():
    """测试二维前向仿真"""
    print("\n" + "=" * 60)
    print("测试二维前向仿真")
    print("=" * 60)

    try:
        # 创建二维网格结构
        grd = Grid()
        grd.Nx = {1: [3] * 4, 2: [3] * 4}  # 每维3个网格点，3个时间步+1
        grd.Xn = {
            1: {'lo': [0.0] * 4, 'hi': [1.0] * 4},
            2: {'lo': [0.0] * 4, 'hi': [2.0] * 4}
        }
        grd.X0 = {1: 0.5, 2: 1.0}  # 初始状态
        grd.Nu = {1: [3] * 3, 2: [3] * 3}  # 每维3个网格点，3个时间步
        grd.Un = {
            1: {'lo': [-0.2] * 3, 'hi': [0.2] * 3},
            2: {'lo': [-0.2] * 3, 'hi': [0.2] * 3}
        }

        # 问题参数
        prb = Problem(Ts=0.1, N=3)
        prb.N0 = 1
        prb.W = {}

        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'off'
        options.Warnings = 'off'
        options.UseUmap = True
        options.InputType = ['c', 'c']  # 两个连续输入

        # 创建二维动态规划结果
        dyn = create_2d_dyn_result(N=3, Nx1=3, Nx2=3)

        print("开始二维前向仿真...")

        # 调用前向仿真函数
        out = dpm_forward(dyn, simple_2d_model, None, grd, prb, options)

        print("二维前向仿真完成!")
        print("✓ 二维前向仿真测试通过")
        return True

    except Exception as e:
        print(f"✗ 二维前向仿真测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_3d_forward():
    """测试三维前向仿真"""
    print("\n" + "=" * 60)
    print("测试三维前向仿真")
    print("=" * 60)

    try:
        # 创建三维网格结构（简化）
        grd = Grid()
        grd.Nx = {1: [2] * 3, 2: [2] * 3, 3: [2] * 3}  # 每维2个网格点，2个时间步+1
        grd.Xn = {
            1: {'lo': [0.0] * 3, 'hi': [1.0] * 3},
            2: {'lo': [0.0] * 3, 'hi': [1.0] * 3},
            3: {'lo': [0.0] * 3, 'hi': [1.0] * 3}
        }
        grd.X0 = {1: 0.5, 2: 0.5, 3: 0.5}  # 初始状态
        grd.Nu = {1: [2] * 2, 2: [2] * 2, 3: [2] * 2}  # 每维2个网格点，2个时间步
        grd.Un = {
            1: {'lo': [-0.1] * 2, 'hi': [0.1] * 2},
            2: {'lo': [-0.1] * 2, 'hi': [0.1] * 2},
            3: {'lo': [-0.1] * 2, 'hi': [0.1] * 2}
        }

        # 问题参数
        prb = Problem(Ts=0.1, N=2)
        prb.N0 = 1
        prb.W = {}

        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'off'
        options.Warnings = 'off'
        options.UseUmap = True
        options.InputType = ['c', 'c', 'c']  # 三个连续输入

        # 创建三维动态规划结果
        dyn = create_3d_dyn_result(N=2, Nx1=2, Nx2=2, Nx3=2)

        print("开始三维前向仿真...")

        # 调用前向仿真函数
        out = dpm_forward(dyn, simple_3d_model, None, grd, prb, options)

        print("三维前向仿真完成!")
        print("✓ 三维前向仿真测试通过")
        return True

    except Exception as e:
        print(f"✗ 三维前向仿真测试失败: {str(e)}")
        print("注意: 这可能是由于三维插值的复杂性")
        import traceback
        traceback.print_exc()
        return False


def test_discrete_input():
    """测试离散输入"""
    print("\n" + "=" * 60)
    print("测试离散输入")
    print("=" * 60)

    try:
        # 创建简单的网格结构
        grd = Grid()
        grd.Nx = {1: [3] * 4}  # 3个网格点，3个时间步+1
        grd.Xn = {1: {'lo': [0.0] * 4, 'hi': [1.0] * 4}}
        grd.X0 = {1: 0.5}  # 初始状态
        grd.Nu = {1: [3] * 3}  # 3个网格点，3个时间步
        grd.Un = {1: {'lo': [-0.2] * 3, 'hi': [0.2] * 3}}

        # 问题参数
        prb = Problem(Ts=0.1, N=3)
        prb.N0 = 1
        prb.W = {}

        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'off'
        options.Warnings = 'off'
        options.UseUmap = True
        options.InputType = ['d']  # 离散输入

        # 创建一维动态规划结果
        dyn = create_1d_dyn_result(N=3, Nx=3)

        print("开始离散输入前向仿真...")

        # 调用前向仿真函数
        out = dpm_forward(dyn, simple_1d_model, None, grd, prb, options)

        print("离散输入前向仿真完成!")
        print("✓ 离散输入测试通过")
        return True

    except Exception as e:
        print(f"✗ 离散输入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_boundary_line():
    """测试边界线方法"""
    print("\n" + "=" * 60)
    print("测试边界线方法")
    print("=" * 60)

    try:
        # 创建简单的网格结构
        grd = Grid()
        grd.Nx = {1: [5] * 6}  # 5个网格点，5个时间步+1
        grd.Xn = {1: {'lo': [0.0] * 6, 'hi': [1.0] * 6}}
        grd.X0 = {1: 0.5}  # 初始状态
        grd.Nu = {1: [5] * 5}  # 5个网格点，5个时间步
        grd.Un = {1: {'lo': [-0.2] * 5, 'hi': [0.2] * 5}}

        # 问题参数
        prb = Problem(Ts=0.1, N=5)
        prb.N0 = 1
        prb.W = {}

        # 选项设置
        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'off'
        options.Warnings = 'off'
        options.UseUmap = True
        options.InputType = ['d']  # 离散输入（边界线方法需要）

        # 创建带边界线的动态规划结果
        dyn = create_boundary_line_dyn_result()

        print("开始边界线方法前向仿真...")

        # 调用前向仿真函数
        out = dpm_forward(dyn, simple_1d_model, None, grd, prb, options)

        print("边界线方法前向仿真完成!")
        print("✓ 边界线方法测试通过")
        return True

    except Exception as e:
        print(f"✗ 边界线方法测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_no_umap():
    """测试不使用输入映射"""
    print("\n" + "=" * 60)
    print("测试不使用输入映射 (UseUmap=False)")
    print("=" * 60)

    try:
        # 创建简单的设置
        grd = Grid()
        grd.Nx = {1: [3] * 4}  # 3个网格点，3个时间步+1
        grd.Xn = {1: {'lo': [0.0] * 4, 'hi': [1.0] * 4}}
        grd.X0 = {1: 0.5}
        grd.Nu = {1: [3] * 3}  # 3个网格点，3个时间步
        grd.Un = {1: {'lo': [-0.3] * 3, 'hi': [0.3] * 3}}

        prb = Problem(Ts=0.2, N=3)
        prb.N0 = 1
        prb.W = {}

        options = Options()
        options.MyInf = 1e4
        options.Minimize = True
        options.Verbose = 'off'
        options.UseUmap = False  # 不使用输入映射
        options.InputType = ['c']

        # 创建简单的动态规划结果
        dyn = DynamicResult()
        dyn.Jo = {1: 0.0}  # 简单的标量代价

        print("开始前向仿真（不使用输入映射）...")

        out = dpm_forward(dyn, simple_1d_model, None, grd, prb, options)

        print("前向仿真完成!")
        print("✓ 不使用输入映射测试通过")
        return True

    except Exception as e:
        print(f"✗ 不使用输入映射测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_high_dimension_warning():
    """测试高维警告"""
    print("\n" + "=" * 60)
    print("测试高维警告 (>3维)")
    print("=" * 60)

    try:
        # 创建4维网格结构来触发警告
        grd = Grid()
        grd.Nx = {1: [2] * 3, 2: [2] * 3, 3: [2] * 3, 4: [2] * 3}  # 4维网格
        grd.Xn = {
            1: {'lo': [0.0] * 3, 'hi': [1.0] * 3},
            2: {'lo': [0.0] * 3, 'hi': [1.0] * 3},
            3: {'lo': [0.0] * 3, 'hi': [1.0] * 3},
            4: {'lo': [0.0] * 3, 'hi': [1.0] * 3}
        }
        grd.X0 = {1: 0.5, 2: 0.5, 3: 0.5, 4: 0.5}
        grd.Nu = {1: [2] * 2, 2: [2] * 2, 3: [2] * 2, 4: [2] * 2}
        grd.Un = {
            1: {'lo': [-0.1] * 2, 'hi': [0.1] * 2},
            2: {'lo': [-0.1] * 2, 'hi': [0.1] * 2},
            3: {'lo': [-0.1] * 2, 'hi': [0.1] * 2},
            4: {'lo': [-0.1] * 2, 'hi': [0.1] * 2}
        }

        prb = Problem(Ts=0.1, N=2)
        prb.N0 = 1
        prb.W = {}

        options = Options()
        options.MyInf = 1e6
        options.Minimize = True
        options.Verbose = 'off'
        options.Warnings = 'on'  # 开启警告
        options.UseUmap = True
        options.InputType = ['c'] * 4

        # 创建简单的动态规划结果
        dyn = DynamicResult()
        dyn.Uo = {i: {1: 0.0, 2: 0.0} for i in range(1, 5)}
        dyn.Jo = {1: 0.0}

        print("开始4维前向仿真（应该看到警告）...")

        # 这应该触发"警告:当前仅支持1-3维插值"
        out = dpm_forward(dyn, simple_1d_model, None, grd, prb, options)

        print("4维前向仿真完成!")
        print("✓ 高维警告测试通过")
        return True

    except Exception as e:
        print(f"✗ 高维警告测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理")
    print("=" * 60)

    try:
        # 测试无效的网格设置
        grd = Grid()
        grd.Nx = []  # 空的网格
        grd.X0 = {1: 0.5}

        prb = Problem(Ts=0.1, N=1)
        prb.N0 = 1
        prb.W = {}

        options = Options()
        options.UseUmap = False

        dyn = DynamicResult()
        dyn.Jo = {1: 0.0}

        print("测试空网格处理...")

        # 这应该优雅地处理错误
        try:
            out = dpm_forward(dyn, simple_1d_model, None, grd, prb, options)
            print("空网格处理成功")
        except Exception as e:
            print(f"空网格处理捕获异常: {type(e).__name__}")

        print("✓ 错误处理测试通过")
        return True

    except Exception as e:
        print(f"✗ 错误处理测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("dpm_forward 函数全面测试程序")
    print("=" * 80)
    print("这个程序全面测试 dpm_forward 函数的各种功能：")
    print("- 一维、二维、三维插值")
    print("- 连续和离散输入")
    print("- 边界线方法")
    print("- 高维警告")
    print("- 错误处理")
    print("=" * 80)

    # 测试列表
    tests = [
        ("一维前向仿真", test_1d_forward),
        ("二维前向仿真", test_2d_forward),
        ("三维前向仿真", test_3d_forward),
        ("离散输入", test_discrete_input),
        ("边界线方法", test_boundary_line),
        ("不使用输入映射", test_no_umap),
        ("高维警告", test_high_dimension_warning),
        ("错误处理", test_error_handling)
    ]

    success_count = 0
    total_tests = len(tests)

    # 运行所有测试
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")

    # 测试总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")

    if success_count == total_tests:
        print("\n🎉 所有测试都通过了！dpm_forward 函数工作正常。")
        print("✅ 一维、二维、三维插值功能正常")
        print("✅ 连续和离散输入处理正确")
        print("✅ 边界线方法实现正确")
        print("✅ 高维警告机制工作正常")
        print("✅ 错误处理健壮")
    elif success_count >= total_tests * 0.7:
        print("\n✅ 大部分测试通过！dpm_forward 函数基本正常。")
        print("⚠️  少数高级功能可能需要进一步完善。")
    else:
        print("\n⚠️  多个测试失败。dpm_forward 函数可能需要调试。")
        print("建议检查:")
        print("- 网格结构定义")
        print("- 动态规划结果格式")
        print("- 插值函数实现")
        print("- 边界线方法逻辑")

    print("=" * 80)


if __name__ == "__main__":
    main()
