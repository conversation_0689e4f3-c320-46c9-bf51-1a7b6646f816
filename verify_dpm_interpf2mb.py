#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证 dpm_interpf2mb 函数的脚本

这个脚本快速验证 dpm_interpf2mb 函数的基本功能
"""

import numpy as np
from dpm import dpm_interpf2mb


def main():
    """主验证函数"""
    print("快速验证 dpm_interpf2mb 函数")
    print("=" * 40)
    
    try:
        # 创建最简单的测试设置
        xx1 = np.array([0, 1, 2])
        xx2 = np.array([0, 1])
        YY = np.array([[0, 1, 4],
                       [1, 2, 5]])  # 2x3矩阵
        
        # 设置边界
        xlim = np.array([[0.5, 0.5],    # 下边界
                         [1.5, 1.5]])   # 上边界
        ylim = np.array([[0.5, 1.5],   # 下边界值
                         [1.5, 2.5]])  # 上边界值
        
        # 测试插值点
        A1 = np.array([1.0])  # 边界内的点
        A2 = np.array([0.5])  # 第二维的中间点
        myInf = 999
        
        print(f"输入网格 xx1: {xx1}")
        print(f"输入网格 xx2: {xx2}")
        print(f"值矩阵 YY:\n{YY}")
        print(f"边界 xlim:\n{xlim}")
        print(f"边界值 ylim:\n{ylim}")
        print(f"插值点 A1: {A1}, A2: {A2}")
        
        # 调用插值函数
        y = dpm_interpf2mb(xx1, xx2, YY, A1, A2, xlim, ylim, myInf)
        
        print(f"插值结果: {y}")
        
        # 测试边界外的点
        A1_out = np.array([0.2])  # 边界外的点
        A2_out = np.array([0.5])
        y_out = dpm_interpf2mb(xx1, xx2, YY, A1_out, A2_out, xlim, ylim, myInf)
        
        print(f"边界外插值结果: {y_out} (应该是 {myInf})")
        
        print("\n验证完成!")
        
        if y_out[0] == myInf:
            print("✓ 边界外检测正常")
        else:
            print("✗ 边界外检测异常")
        
        if not np.isnan(y[0]) and y[0] != myInf:
            print("✓ 边界内插值正常")
        else:
            print("✗ 边界内插值异常")
        
    except Exception as e:
        print(f"验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
