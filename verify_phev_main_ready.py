#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证 PHEV HT21 主函数准备就绪

测试主函数的所有组件是否准备好进行动态规划优化
"""

from phev_main_ht21 import load_driving_cycle, create_grid, define_problem, set_options
from phev_ht21_serip1p3 import phev_ht21_serip1p3
from dpm import dpm


def main():
    """验证主函数准备就绪"""
    print("=" * 80)
    print("PHEV HT21 主函数准备就绪验证")
    print("=" * 80)
    
    try:
        print("\n步骤1: 加载驾驶循环数据")
        print("-" * 40)
        speed_vector, acceleration_vector, gearnumber_vector = load_driving_cycle()
        
        print("\n步骤2: 创建动态规划网格")
        print("-" * 40)
        grd = create_grid()
        
        print("\n步骤3: 定义动态规划问题")
        print("-" * 40)
        prb = define_problem(speed_vector, acceleration_vector, gearnumber_vector)
        
        print("\n步骤4: 设置动态规划选项")
        print("-" * 40)
        options = set_options()
        
        print("\n步骤5: 验证车辆模型")
        print("-" * 40)
        # 测试车辆模型调用
        inp = {
            'W': [20.0, 0.5],  # 速度和加速度
            'U': [0.5, 1],     # 控制输入
            'X': [0.6]         # 状态
        }
        par = {}
        
        X, C, I, out = phev_ht21_serip1p3(inp, par)
        print(f"车辆模型测试: SOC {inp['X'][0]:.3f} → {X[0]:.3f}, 燃油 {C[0]:.6f} kg/s")
        
        print("\n步骤6: 验证DPM函数可用性")
        print("-" * 40)
        print(f"DPM函数类型: {type(dpm)}")
        print("DPM函数可以调用")
        
        print("\n" + "=" * 80)
        print("✅ 所有组件验证完成！")
        print("=" * 80)
        
        print("\n📊 系统配置总结:")
        print(f"  驾驶循环: {len(speed_vector)} 个数据点 ({(len(speed_vector)-1)/60:.1f} 分钟)")
        print(f"  SOC网格: {grd.Nx[0][0]} 点 (分辨率: {(grd.Xn[0]['hi'][0]-grd.Xn[0]['lo'][0])/(grd.Nx[0][0]-1):.4f})")
        print(f"  控制网格: {grd.Nu[0][0]} × {grd.Nu[0][1]} 点")
        print(f"  总网格点数: {grd.Nx[0][0] * grd.Nu[0][0] * grd.Nu[0][1]:,}")
        print(f"  时间步数: {prb.N}")
        print(f"  边界方法: {options.BoundaryMethod}")
        
        print("\n🚀 系统已准备好进行动态规划优化！")
        
        print("\n💡 使用方法:")
        print("  from phev_main_ht21 import main")
        print("  results, dynamics = main()")
        
        print("\n⚠️  注意事项:")
        print("  - 动态规划计算可能需要较长时间")
        print("  - 建议在性能较好的计算机上运行")
        print("  - 可以通过调整网格分辨率来平衡精度和计算时间")
        
        estimated_time = (grd.Nx[0][0] * grd.Nu[0][0] * grd.Nu[0][1] * prb.N) / 1e6
        print(f"  - 预估计算时间: {estimated_time:.1f} 分钟 (粗略估计)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 验证成功！系统准备就绪。")
    else:
        print("\n💥 验证失败！请检查系统配置。")
