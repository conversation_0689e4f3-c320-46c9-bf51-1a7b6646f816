#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试_check_state_boundaries和_check_final_constraints函数与MATLAB版本的一致性

验证Python版本的检查函数是否与MATLAB版本@dpm.m（383-408行）完全一致
"""

import numpy as np
from dpm import Grid, Options, _check_state_boundaries, _check_final_constraints

def test_state_boundaries_check():
    """测试状态边界检查函数"""
    print("=" * 60)
    print("测试1: 状态边界检查函数")
    print("=" * 60)
    
    # 测试1: 正常情况
    print("测试1.1: 正常边界")
    grd = Grid()
    grd.Xn = {
        1: {'lo': [0.0, 0.0, 0.0], 'hi': [1.0, 1.0, 1.0]},
        2: {'lo': [-1.0, -1.0], 'hi': [1.0, 1.0]}
    }
    
    try:
        _check_state_boundaries(grd)
        print("✓ 正常边界检查通过")
    except Exception as e:
        print(f"✗ 正常边界检查失败: {e}")
        return False
    
    # 测试2: 单个状态边界错误
    print("\n测试1.2: 单个状态边界错误")
    grd_error = Grid()
    grd_error.Xn = {
        1: {'lo': [1.0], 'hi': [0.0]}  # 上边界小于下边界
    }
    
    try:
        _check_state_boundaries(grd_error)
        print("✗ 应该抛出错误但没有")
        return False
    except ValueError as e:
        if "状态 1 的上边界小于下边界" in str(e):
            print("✓ 单个状态边界错误检测正确")
        else:
            print(f"✗ 错误信息不正确: {e}")
            return False
    
    # 测试3: 多实例状态边界错误
    print("\n测试1.3: 多实例状态边界错误")
    grd_multi_error = Grid()
    grd_multi_error.Xn = {
        1: {'lo': [0.0, 1.0, 0.0], 'hi': [1.0, 0.0, 1.0]}  # 第二个实例错误
    }
    
    try:
        _check_state_boundaries(grd_multi_error)
        print("✗ 应该抛出错误但没有")
        return False
    except ValueError as e:
        if "状态 1 在实例 2 的上边界小于下边界" in str(e):
            print("✓ 多实例状态边界错误检测正确")
        else:
            print(f"✗ 错误信息不正确: {e}")
            return False
    
    return True

def test_final_constraints_check():
    """测试最终状态约束检查函数"""
    print("\n测试2: 最终状态约束检查函数")
    print("=" * 60)
    
    # 测试1: FixedGrid=True时不检查
    print("测试2.1: FixedGrid=True时不检查")
    grd = Grid()
    grd.XN = {1: {'lo': 0.99, 'hi': 1.01}}
    grd.Nx = {1: [100]}
    
    options = Options()
    options.FixedGrid = 1  # True
    
    try:
        _check_final_constraints(grd, options)
        print("✓ FixedGrid=True时正确跳过检查")
    except Exception as e:
        print(f"✗ FixedGrid=True时错误抛出异常: {e}")
        return False
    
    # 测试2: FixedGrid=False且约束合理
    print("\n测试2.2: FixedGrid=False且约束合理")
    options.FixedGrid = 0  # False
    grd.XN = {1: {'lo': 0.0, 'hi': 1.0}}  # 宽松约束
    grd.Nx = {1: [10]}
    
    try:
        _check_final_constraints(grd, options)
        print("✓ 合理约束检查通过")
    except Exception as e:
        print(f"✗ 合理约束检查失败: {e}")
        return False
    
    # 测试3: FixedGrid=False且约束太紧
    print("\n测试2.3: FixedGrid=False且约束太紧")
    eps = np.finfo(float).eps
    very_tight_width = eps * 5  # 非常小的约束宽度
    grd.XN = {1: {'lo': 0.5, 'hi': 0.5 + very_tight_width}}
    grd.Nx = {1: [100]}  # 需要100个点但约束太紧
    
    try:
        _check_final_constraints(grd, options)
        print("✗ 应该抛出约束太紧的错误但没有")
        return False
    except ValueError as e:
        if "最终状态约束太紧" in str(e):
            print("✓ 约束太紧错误检测正确")
        else:
            print(f"✗ 错误信息不正确: {e}")
            return False
    
    # 测试4: 只检查第一个状态（与MATLAB一致）
    print("\n测试2.4: 只检查第一个状态")
    grd.XN = {
        1: {'lo': 0.0, 'hi': 1.0},  # 第一个状态约束合理
        2: {'lo': 0.5, 'hi': 0.5 + very_tight_width}  # 第二个状态约束太紧
    }
    grd.Nx = {
        1: [10],
        2: [100]
    }
    
    try:
        _check_final_constraints(grd, options)
        print("✓ 只检查第一个状态，忽略其他状态的约束")
    except Exception as e:
        print(f"✗ 错误检查了其他状态: {e}")
        return False
    
    return True

def test_matlab_consistency():
    """测试与MATLAB版本的完全一致性"""
    print("\n测试3: 与MATLAB版本的完全一致性")
    print("=" * 60)
    
    # 模拟MATLAB中的典型使用场景
    print("测试3.1: 典型使用场景")
    
    # 创建典型的网格结构
    grd = Grid()
    grd.Xn = {
        1: {'lo': [0.4, 0.4, 0.4, 0.4, 0.4, 0.4], 
            'hi': [0.6, 0.6, 0.6, 0.6, 0.6, 0.6]},
        2: {'lo': [-2.5, -2.5, -2.5, -2.5, -2.5], 
            'hi': [1.0, 1.0, 1.0, 1.0, 1.0]}
    }
    grd.XN = {1: {'lo': 0.495, 'hi': 0.5}}
    grd.Nx = {1: [401, 401, 401, 401, 401, 401]}
    
    options = Options()
    options.FixedGrid = 0
    
    try:
        _check_state_boundaries(grd)
        print("✓ 状态边界检查通过")
        
        _check_final_constraints(grd, options)
        print("✓ 最终约束检查通过")
        
        return True
    except Exception as e:
        print(f"✗ 典型场景测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("测试检查函数与MATLAB版本的一致性")
    print("对比MATLAB @dpm.m 第383-408行的逻辑")
    print()
    
    tests = [
        test_state_boundaries_check,
        test_final_constraints_check,
        test_matlab_consistency
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        print("✓ Python版本与MATLAB版本完全一致")
        print("✓ 状态边界检查逻辑正确")
        print("✓ 最终约束检查逻辑正确")
        print("✓ 错误信息格式正确")
        print("✓ 只检查第一个状态的最终约束（与MATLAB一致）")
    else:
        print("⚠️  部分测试失败。请检查实现。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
