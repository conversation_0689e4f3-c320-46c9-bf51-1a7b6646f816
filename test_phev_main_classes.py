#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 PHEV HT21 主函数的类对象使用

验证使用dpm.py中定义的类对象的正确性
"""

import numpy as np
from phev_main_ht21 import load_driving_cycle, create_grid, define_problem, set_options
from dpm import Grid, Problem, Options


def test_grid_class():
    """测试Grid类的使用"""
    print("=" * 60)
    print("测试Grid类的使用")
    print("=" * 60)
    
    try:
        grd = create_grid()
        
        print(f"\nGrid对象验证:")
        print(f"  对象类型: {type(grd)}")
        print(f"  是否为Grid实例: {isinstance(grd, Grid)}")
        
        # 检查属性
        print(f"\n属性检查:")
        print(f"  Nx: {grd.Nx}")
        print(f"  Xn: {grd.Xn}")
        print(f"  Nu: {grd.Nu}")
        print(f"  Un: {grd.Un}")
        print(f"  X0: {grd.X0}")
        print(f"  XN: {grd.XN}")
        
        # 验证数据类型和结构
        if isinstance(grd.Nx, dict) and 0 in grd.Nx:
            print("✓ Nx结构正确")
        else:
            print("✗ Nx结构错误")
            return False
        
        if isinstance(grd.Xn, dict) and 0 in grd.Xn:
            print("✓ Xn结构正确")
        else:
            print("✗ Xn结构错误")
            return False
        
        if isinstance(grd.Nu, dict) and 0 in grd.Nu:
            print("✓ Nu结构正确")
        else:
            print("✗ Nu结构错误")
            return False
        
        if isinstance(grd.Un, dict) and 0 in grd.Un:
            print("✓ Un结构正确")
        else:
            print("✗ Un结构错误")
            return False
        
        print("✓ Grid类测试通过")
        return True, grd
        
    except Exception as e:
        print(f"✗ Grid类测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None


def test_problem_class():
    """测试Problem类的使用"""
    print("\n" + "=" * 60)
    print("测试Problem类的使用")
    print("=" * 60)
    
    try:
        # 加载数据
        speed_vector, acceleration_vector, gearnumber_vector = load_driving_cycle()
        prb = define_problem(speed_vector, acceleration_vector, gearnumber_vector)
        
        print(f"\nProblem对象验证:")
        print(f"  对象类型: {type(prb)}")
        print(f"  是否为Problem实例: {isinstance(prb, Problem)}")
        
        # 检查属性
        print(f"\n属性检查:")
        print(f"  Ts: {prb.Ts}")
        print(f"  N: {prb.N}")
        print(f"  W类型: {type(prb.W)}")
        print(f"  W键: {list(prb.W.keys()) if hasattr(prb.W, 'keys') else 'N/A'}")
        
        # 验证数据
        if hasattr(prb, 'Ts') and prb.Ts > 0:
            print("✓ Ts设置正确")
        else:
            print("✗ Ts设置错误")
            return False
        
        if hasattr(prb, 'N') and prb.N > 0:
            print("✓ N设置正确")
        else:
            print("✗ N设置错误")
            return False
        
        if hasattr(prb, 'W') and isinstance(prb.W, dict):
            print("✓ W结构正确")
            # 检查W的内容
            if 0 in prb.W and 1 in prb.W and 2 in prb.W:
                print("✓ W包含所有必要的数据")
            else:
                print("✗ W缺少必要的数据")
                return False
        else:
            print("✗ W结构错误")
            return False
        
        print("✓ Problem类测试通过")
        return True, prb
        
    except Exception as e:
        print(f"✗ Problem类测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None


def test_options_class():
    """测试Options类的使用"""
    print("\n" + "=" * 60)
    print("测试Options类的使用")
    print("=" * 60)
    
    try:
        options = set_options()
        
        print(f"\nOptions对象验证:")
        print(f"  对象类型: {type(options)}")
        print(f"  是否为Options实例: {isinstance(options, Options)}")
        
        # 检查属性
        print(f"\n属性检查:")
        print(f"  MyInf: {options.MyInf}")
        print(f"  BoundaryMethod: {options.BoundaryMethod}")
        print(f"  Verbose: {options.Verbose}")
        print(f"  Waitbar: {options.Waitbar}")
        print(f"  SaveMap: {options.SaveMap}")
        
        if options.BoundaryMethod == 'Line':
            print(f"  Iter: {options.Iter}")
            print(f"  Tol: {options.Tol}")
            print(f"  FixedGrid: {options.FixedGrid}")
        
        # 验证设置
        if hasattr(options, 'MyInf') and options.MyInf > 0:
            print("✓ MyInf设置正确")
        else:
            print("✗ MyInf设置错误")
            return False
        
        if hasattr(options, 'BoundaryMethod') and options.BoundaryMethod in ['Line', 'none', 'LevelSet']:
            print("✓ BoundaryMethod设置正确")
        else:
            print("✗ BoundaryMethod设置错误")
            return False
        
        if hasattr(options, 'Verbose') and options.Verbose in ['on', 'off']:
            print("✓ Verbose设置正确")
        else:
            print("✗ Verbose设置错误")
            return False
        
        print("✓ Options类测试通过")
        return True, options
        
    except Exception as e:
        print(f"✗ Options类测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None


def test_integration():
    """测试所有组件的集成"""
    print("\n" + "=" * 60)
    print("测试组件集成")
    print("=" * 60)
    
    try:
        # 创建所有组件
        print("创建所有组件...")
        
        # 1. 加载数据
        speed_vector, acceleration_vector, gearnumber_vector = load_driving_cycle()
        print("✓ 数据加载完成")
        
        # 2. 创建网格
        grd = create_grid()
        print("✓ 网格创建完成")
        
        # 3. 定义问题
        prb = define_problem(speed_vector, acceleration_vector, gearnumber_vector)
        print("✓ 问题定义完成")
        
        # 4. 设置选项
        options = set_options()
        print("✓ 选项设置完成")
        
        # 验证所有对象都是正确的类型
        print(f"\n类型验证:")
        print(f"  Grid: {type(grd)} - {'✓' if isinstance(grd, Grid) else '✗'}")
        print(f"  Problem: {type(prb)} - {'✓' if isinstance(prb, Problem) else '✗'}")
        print(f"  Options: {type(options)} - {'✓' if isinstance(options, Options) else '✗'}")
        
        # 检查数据一致性
        print(f"\n数据一致性检查:")
        data_length = len(speed_vector)
        print(f"  原始数据长度: {data_length}")
        print(f"  问题时间步数: {prb.N}")
        print(f"  数据一致性: {'✓' if prb.N == data_length else '✗'}")
        
        # 检查网格合理性
        print(f"\n网格合理性检查:")
        soc_range = grd.Xn[0]['hi'][0] - grd.Xn[0]['lo'][0]
        soc_resolution = soc_range / (grd.Nx[0][0] - 1)
        print(f"  SOC范围: {grd.Xn[0]['lo'][0]:.1f} - {grd.Xn[0]['hi'][0]:.1f}")
        print(f"  SOC分辨率: {soc_resolution:.4f}")
        print(f"  网格合理性: {'✓' if 0.0001 <= soc_resolution <= 0.01 else '✗'}")
        
        print("\n✓ 所有组件集成测试通过")
        print("准备进行动态规划优化！")
        
        return True
        
    except Exception as e:
        print(f"✗ 组件集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("PHEV HT21 主函数类对象测试程序")
    print("验证使用dpm.py中定义的类对象的正确性")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: Grid类
    success, grd = test_grid_class()
    if success:
        success_count += 1
    
    # 测试2: Problem类
    success, prb = test_problem_class()
    if success:
        success_count += 1
    
    # 测试3: Options类
    success, options = test_options_class()
    if success:
        success_count += 1
    
    # 测试4: 集成测试
    if test_integration():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有类对象测试都通过了！")
        print("✓ Grid类使用正确")
        print("✓ Problem类使用正确")
        print("✓ Options类使用正确")
        print("✓ 组件集成正常")
        print("\n主函数已准备好使用dpm.py中的类对象进行动态规划优化！")
    else:
        print("⚠️  部分测试失败。请检查类对象的使用。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
