#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DPM Python版本测试脚本

这个脚本演示了如何使用Python版本的DPM（动态规划矩阵运算）库
来解决各种动态规划问题。

作者: Python版本翻译
日期: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
from dpm import dpm, DPM, Grid, Problem, Options, Input, Output, create_simple_problem


def test_model_1(inp, par=None):
    """
    测试模型1: 简单的积分器系统
    
    系统方程: x[k+1] = x[k] + u[k]*Ts
    代价函数: J = u[k]^2 * Ts
    
    参数:
        inp: 输入结构
        par: 参数（未使用）
    
    返回:
        X: 下一状态
        C: 代价
        I: 不可行性标志
    """
    # 状态更新
    X = {1: inp.X[1] + inp.U[1] * inp.Ts}
    
    # 代价函数（二次输入代价）
    C = {1: inp.U[1]**2 * inp.Ts}
    
    # 不可行性检查（此例中无约束）
    I = np.zeros_like(inp.X[1])
    
    return X, C, I


def test_model_2(inp, par=None):
    """
    测试模型2: 带阻尼的振荡器
    
    系统方程: 
        x1[k+1] = x1[k] + x2[k]*Ts
        x2[k+1] = x2[k] - 0.1*x1[k]*Ts - 0.05*x2[k]*Ts + u[k]*Ts
    
    代价函数: J = x1[k]^2 + x2[k]^2 + 0.1*u[k]^2
    """
    # 系统参数
    k_spring = 0.1  # 弹簧常数
    c_damping = 0.05  # 阻尼系数
    
    # 状态更新
    X = {
        1: inp.X[1] + inp.X[2] * inp.Ts,
        2: inp.X[2] - k_spring * inp.X[1] * inp.Ts - c_damping * inp.X[2] * inp.Ts + inp.U[1] * inp.Ts
    }
    
    # 代价函数
    C = {1: inp.X[1]**2 + inp.X[2]**2 + 0.1 * inp.U[1]**2}
    
    # 不可行性检查
    I = np.zeros_like(inp.X[1])
    
    return X, C, I


def test_model_3(inp, par=None):
    """
    测试模型3: 非线性系统
    
    系统方程: x[k+1] = x[k] + (x[k] - x[k]^3/3 + u[k])*Ts
    代价函数: J = x[k]^2 + u[k]^2
    """
    # 非线性状态更新
    X = {1: inp.X[1] + (inp.X[1] - inp.X[1]**3/3 + inp.U[1]) * inp.Ts}
    
    # 代价函数
    C = {1: inp.X[1]**2 + inp.U[1]**2}
    
    # 不可行性检查
    I = np.zeros_like(inp.X[1])
    
    return X, C, I


def run_test_1():
    """运行测试1: 简单积分器系统"""
    print("=" * 60)
    print("测试1: 简单积分器系统")
    print("目标: 从x=0到达x=10，最小化输入能量")
    print("=" * 60)
    
    # 创建问题
    grd, prb = create_simple_problem(
        x0=[0.0],  # 初始状态
        xf_bounds=[(9.5, 10.5)],  # 最终状态约束
        x_bounds=[(-2.0, 12.0)],  # 状态边界
        u_bounds=[(-3.0, 3.0)],   # 输入边界
        nx_values=[31],  # 状态网格点数
        nu_values=[21],  # 输入网格点数
        N=20,  # 时间步数
        Ts=0.5  # 时间步长
    )
    
    # 设置选项
    options = dpm()
    options.Verbose = 'on'
    options.MyInf = 1e6
    
    try:
        # 求解
        print("开始求解...")
        out, dyn = dpm(test_model_1, None, grd, prb, options)
        
        # 显示结果
        print(f"\n结果:")
        print(f"最终状态: {out['X'][1][-1]:.4f}")
        print(f"总代价: {out['total_cost']:.6f}")
        print(f"平均输入: {np.mean(np.abs(out['U'][1])):.4f}")
        
        # 绘制结果
        plot_results_1d(out, "测试1: 积分器系统")
        
        return True
        
    except Exception as e:
        print(f"测试1失败: {str(e)}")
        return False


def run_test_2():
    """运行测试2: 二维振荡器系统"""
    print("=" * 60)
    print("测试2: 带阻尼振荡器系统")
    print("目标: 从(1,0)到达原点(0,0)附近")
    print("=" * 60)
    
    # 创建网格结构
    grd = Grid()
    
    # 状态1 (位置)
    grd.Nx[1] = [21] * 21
    grd.Xn[1] = {'lo': [-2.0] * 21, 'hi': [2.0] * 21}
    grd.X0[1] = 1.0
    grd.XN[1] = {'lo': -0.2, 'hi': 0.2}
    
    # 状态2 (速度)
    grd.Nx[2] = [21] * 21
    grd.Xn[2] = {'lo': [-2.0] * 21, 'hi': [2.0] * 21}
    grd.X0[2] = 0.0
    grd.XN[2] = {'lo': -0.2, 'hi': 0.2}
    
    # 输入
    grd.Nu[1] = [11] * 20
    grd.Un[1] = {'lo': [-1.0] * 20, 'hi': [1.0] * 20}
    
    # 问题参数
    prb = Problem(Ts=0.2, N=20)
    
    # 选项
    options = dpm()
    options.Verbose = 'on'
    options.MyInf = 1e6
    
    try:
        print("开始求解...")
        out, dyn = dpm(test_model_2, None, grd, prb, options)
        
        # 显示结果
        print(f"\n结果:")
        print(f"最终位置: {out['X'][1][-1]:.4f}")
        print(f"最终速度: {out['X'][2][-1]:.4f}")
        print(f"总代价: {out['total_cost']:.6f}")
        
        # 绘制结果
        plot_results_2d(out, "测试2: 振荡器系统")
        
        return True
        
    except Exception as e:
        print(f"测试2失败: {str(e)}")
        return False


def plot_results_1d(out, title):
    """绘制一维系统结果"""
    try:
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(10, 8))
        
        # 时间轴
        N = len(out['X'][1]) - 1
        t = np.arange(N + 1) * 0.5  # 假设Ts=0.5
        t_u = np.arange(N) * 0.5
        
        # 状态轨迹
        ax1.plot(t, out['X'][1], 'b-o', linewidth=2, markersize=4)
        ax1.set_ylabel('状态 x(t)')
        ax1.grid(True)
        ax1.set_title(title)
        
        # 输入轨迹
        ax2.step(t_u, out['U'][1], 'r-', linewidth=2, where='post')
        ax2.set_ylabel('输入 u(t)')
        ax2.grid(True)
        
        # 代价
        ax3.plot(t_u, out['C'][1], 'g-', linewidth=2)
        ax3.set_ylabel('瞬时代价')
        ax3.set_xlabel('时间 (s)')
        ax3.grid(True)
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"绘图错误: {str(e)}")


def plot_results_2d(out, title):
    """绘制二维系统结果"""
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        
        # 时间轴
        N = len(out['X'][1]) - 1
        t = np.arange(N + 1) * 0.2  # 假设Ts=0.2
        t_u = np.arange(N) * 0.2
        
        # 相平面图
        ax1.plot(out['X'][1], out['X'][2], 'b-o', linewidth=2, markersize=3)
        ax1.plot(out['X'][1][0], out['X'][2][0], 'go', markersize=8, label='起点')
        ax1.plot(out['X'][1][-1], out['X'][2][-1], 'ro', markersize=8, label='终点')
        ax1.set_xlabel('位置')
        ax1.set_ylabel('速度')
        ax1.set_title('相平面轨迹')
        ax1.grid(True)
        ax1.legend()
        
        # 位置轨迹
        ax2.plot(t, out['X'][1], 'b-', linewidth=2)
        ax2.set_ylabel('位置')
        ax2.set_xlabel('时间 (s)')
        ax2.grid(True)
        ax2.set_title('位置轨迹')
        
        # 速度轨迹
        ax3.plot(t, out['X'][2], 'g-', linewidth=2)
        ax3.set_ylabel('速度')
        ax3.set_xlabel('时间 (s)')
        ax3.grid(True)
        ax3.set_title('速度轨迹')
        
        # 输入轨迹
        ax4.step(t_u, out['U'][1], 'r-', linewidth=2, where='post')
        ax4.set_ylabel('输入')
        ax4.set_xlabel('时间 (s)')
        ax4.grid(True)
        ax4.set_title('控制输入')
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"绘图错误: {str(e)}")


def main():
    """主测试函数"""
    print("DPM Python版本测试程序")
    print("这个程序将测试动态规划算法的各种功能")
    print()
    
    # 测试基本功能
    print("1. 测试基本功能...")
    
    # 获取默认选项
    options = dpm()
    print(f"✓ 默认选项获取成功")
    
    # 生成测试模型
    dpm('auto_test_model', 2, 1)
    print(f"✓ 测试模型生成成功")
    
    print()
    
    # 运行数值测试
    print("2. 运行数值测试...")
    
    success_count = 0
    total_tests = 2
    
    # 测试1
    if run_test_1():
        success_count += 1
        print("✓ 测试1通过")
    else:
        print("✗ 测试1失败")
    
    print()
    
    # 测试2
    if run_test_2():
        success_count += 1
        print("✓ 测试2通过")
    else:
        print("✗ 测试2失败")
    
    print()
    print("=" * 60)
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！DPM Python版本工作正常。")
    else:
        print("⚠️  部分测试失败。这可能是由于某些高级功能尚未完全实现。")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
